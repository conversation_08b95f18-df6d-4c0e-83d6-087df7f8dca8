<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PiP Master Settings Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #45a049;
        }
        
        .test-button.danger {
            background: #f44336;
        }
        
        .test-button.danger:hover {
            background: #da190b;
        }
        
        .test-button.info {
            background: #2196F3;
        }
        
        .test-button.info:hover {
            background: #1976D2;
        }
        
        .log-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 10px;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .settings-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .form-group input, .form-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .form-group input[type="checkbox"] {
            width: auto;
        }
        
        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚙️ PiP Master Settings Test Suite</h1>
        <p>This page tests all settings functionality including storage, synchronization, and UI communication.</p>
    </div>

    <div class="test-section">
        <h2>📊 Settings System Status</h2>
        <div id="settings-status" class="status info">Checking settings system...</div>
        <button class="test-button" onclick="checkSettingsStatus()">Refresh Status</button>
        <button class="test-button info" onclick="diagnoseSettings()">Diagnose Issues</button>
    </div>

    <div class="test-section">
        <h2>🔧 Settings Operations</h2>
        <button class="test-button" onclick="loadSettings()">Load Settings</button>
        <button class="test-button" onclick="saveTestSettings()">Save Test Settings</button>
        <button class="test-button" onclick="updateSingleSetting()">Update Single Setting</button>
        <button class="test-button info" onclick="exportSettings()">Export Settings</button>
        <button class="test-button info" onclick="importSettings()">Import Settings</button>
        <button class="test-button danger" onclick="resetSettings()">Reset to Defaults</button>
    </div>

    <div class="test-section">
        <h2>📝 Current Settings</h2>
        <div id="current-settings" class="json-display">Loading...</div>
        <button class="test-button" onclick="refreshCurrentSettings()">Refresh</button>
    </div>

    <div class="test-section">
        <h2>✏️ Settings Editor</h2>
        <div class="settings-form">
            <div class="form-group">
                <label>
                    <input type="checkbox" id="enabled" checked> Extension Enabled
                </label>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="showOverlay" checked> Show Overlay
                </label>
            </div>
            <div class="form-group">
                <label for="opacity">Opacity</label>
                <input type="range" id="opacity" min="0.1" max="1" step="0.1" value="0.9">
                <span id="opacityValue">90%</span>
            </div>
            <div class="form-group">
                <label for="overlayPosition">Overlay Position</label>
                <select id="overlayPosition">
                    <option value="top-left">Top Left</option>
                    <option value="top-right" selected>Top Right</option>
                    <option value="bottom-left">Bottom Left</option>
                    <option value="bottom-right">Bottom Right</option>
                </select>
            </div>
            <div class="form-group">
                <label for="theme">Theme</label>
                <select id="theme">
                    <option value="auto" selected>Auto</option>
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                </select>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="autoActivate"> Auto Activate
                </label>
            </div>
        </div>
        <button class="test-button" onclick="applyFormSettings()">Apply Settings</button>
        <button class="test-button" onclick="loadFormSettings()">Load to Form</button>
    </div>

    <div class="test-section">
        <h2>🔄 Communication Test</h2>
        <button class="test-button" onclick="testPopupCommunication()">Test Popup Communication</button>
        <button class="test-button" onclick="testContentScriptCommunication()">Test Content Script Communication</button>
        <button class="test-button" onclick="testBackgroundCommunication()">Test Background Communication</button>
    </div>

    <div class="test-section">
        <h2>📝 Debug Log</h2>
        <div id="log-output" class="log-output"></div>
        <button class="test-button danger" onclick="clearLog()">Clear Log</button>
    </div>

    <script src="settings-comprehensive-fix.js"></script>
    <script>
        // Logging system
        const logOutput = document.getElementById('log-output');
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        function addToLog(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logOutput.textContent += logEntry;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        // Override console methods
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToLog(args.join(' '), 'warn');
        };

        // Test functions
        async function checkSettingsStatus() {
            const statusDiv = document.getElementById('settings-status');
            
            try {
                if (window.settingsManager) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ Settings Manager loaded and ready';
                    console.log('Settings Manager is available');
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '❌ Settings Manager not loaded';
                    console.log('Settings Manager not available');
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Error checking settings status: ' + error.message;
                console.error('Error checking settings status:', error);
            }
        }

        async function diagnoseSettings() {
            console.log('Running settings diagnosis...');
            
            if (window.testSettings && window.testSettings.diagnose) {
                const diagnosis = await window.testSettings.diagnose();
                console.log('Settings diagnosis complete');
                
                // Display diagnosis results
                const issues = diagnosis.issues.length;
                if (issues === 0) {
                    console.log('✅ No issues found');
                } else {
                    console.log(`⚠️ Found ${issues} issues:`);
                    diagnosis.issues.forEach(issue => console.log(`  - ${issue}`));
                }
            } else {
                console.log('❌ Diagnosis function not available');
            }
        }

        async function loadSettings() {
            console.log('Loading settings...');
            
            if (window.testSettings && window.testSettings.get) {
                const settings = await window.testSettings.get();
                console.log('Settings loaded:', JSON.stringify(settings, null, 2));
                refreshCurrentSettings();
            } else {
                console.log('❌ Settings get function not available');
            }
        }

        async function saveTestSettings() {
            console.log('Saving test settings...');
            
            const testSettings = {
                enabled: true,
                showOverlay: true,
                opacity: 0.8,
                overlayPosition: 'bottom-right',
                theme: 'dark',
                testFlag: true,
                timestamp: new Date().toISOString()
            };
            
            if (window.settingsManager) {
                const success = await window.settingsManager.saveSettings(testSettings);
                if (success) {
                    console.log('✅ Test settings saved successfully');
                    refreshCurrentSettings();
                } else {
                    console.log('❌ Failed to save test settings');
                }
            } else {
                console.log('❌ Settings Manager not available');
            }
        }

        async function updateSingleSetting() {
            console.log('Updating single setting...');
            
            if (window.testSettings && window.testSettings.update) {
                const success = await window.testSettings.update('opacity', 0.75);
                if (success) {
                    console.log('✅ Single setting updated successfully');
                    refreshCurrentSettings();
                } else {
                    console.log('❌ Failed to update single setting');
                }
            } else {
                console.log('❌ Settings update function not available');
            }
        }

        async function exportSettings() {
            console.log('Exporting settings...');
            
            if (window.testSettings && window.testSettings.export) {
                const exportData = await window.testSettings.export();
                if (exportData) {
                    console.log('✅ Settings exported:', JSON.stringify(exportData, null, 2));
                    
                    // Create download link
                    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'pip-master-settings.json';
                    a.click();
                    URL.revokeObjectURL(url);
                } else {
                    console.log('❌ Failed to export settings');
                }
            } else {
                console.log('❌ Settings export function not available');
            }
        }

        async function importSettings() {
            console.log('Importing settings...');
            
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            
            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (file) {
                    try {
                        const text = await file.text();
                        const importData = JSON.parse(text);
                        
                        if (window.testSettings && window.testSettings.import) {
                            const success = await window.testSettings.import(importData);
                            if (success) {
                                console.log('✅ Settings imported successfully');
                                refreshCurrentSettings();
                            } else {
                                console.log('❌ Failed to import settings');
                            }
                        } else {
                            console.log('❌ Settings import function not available');
                        }
                    } catch (error) {
                        console.error('❌ Failed to parse import file:', error);
                    }
                }
            };
            
            input.click();
        }

        async function resetSettings() {
            if (confirm('Are you sure you want to reset all settings to defaults?')) {
                console.log('Resetting settings to defaults...');
                
                if (window.testSettings && window.testSettings.reset) {
                    const success = await window.testSettings.reset();
                    if (success) {
                        console.log('✅ Settings reset to defaults');
                        refreshCurrentSettings();
                        loadFormSettings();
                    } else {
                        console.log('❌ Failed to reset settings');
                    }
                } else {
                    console.log('❌ Settings reset function not available');
                }
            }
        }

        async function refreshCurrentSettings() {
            const settingsDiv = document.getElementById('current-settings');
            
            try {
                if (window.testSettings && window.testSettings.get) {
                    const settings = await window.testSettings.get();
                    settingsDiv.textContent = JSON.stringify(settings, null, 2);
                } else {
                    settingsDiv.textContent = 'Settings Manager not available';
                }
            } catch (error) {
                settingsDiv.textContent = 'Error loading settings: ' + error.message;
            }
        }

        async function applyFormSettings() {
            console.log('Applying form settings...');
            
            const formSettings = {
                enabled: document.getElementById('enabled').checked,
                showOverlay: document.getElementById('showOverlay').checked,
                opacity: parseFloat(document.getElementById('opacity').value),
                overlayPosition: document.getElementById('overlayPosition').value,
                theme: document.getElementById('theme').value,
                autoActivate: document.getElementById('autoActivate').checked
            };
            
            if (window.settingsManager) {
                const success = await window.settingsManager.saveSettings(formSettings);
                if (success) {
                    console.log('✅ Form settings applied successfully');
                    refreshCurrentSettings();
                } else {
                    console.log('❌ Failed to apply form settings');
                }
            } else {
                console.log('❌ Settings Manager not available');
            }
        }

        async function loadFormSettings() {
            console.log('Loading settings to form...');
            
            try {
                if (window.testSettings && window.testSettings.get) {
                    const settings = await window.testSettings.get();
                    
                    document.getElementById('enabled').checked = settings.enabled !== false;
                    document.getElementById('showOverlay').checked = settings.showOverlay !== false;
                    document.getElementById('opacity').value = settings.opacity || 0.9;
                    document.getElementById('opacityValue').textContent = Math.round((settings.opacity || 0.9) * 100) + '%';
                    document.getElementById('overlayPosition').value = settings.overlayPosition || 'top-right';
                    document.getElementById('theme').value = settings.theme || 'auto';
                    document.getElementById('autoActivate').checked = settings.autoActivate === true;
                    
                    console.log('✅ Settings loaded to form');
                } else {
                    console.log('❌ Settings get function not available');
                }
            } catch (error) {
                console.error('❌ Failed to load settings to form:', error);
            }
        }

        async function testPopupCommunication() {
            console.log('Testing popup communication...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                try {
                    const response = await chrome.runtime.sendMessage({ type: 'GET_SETTINGS' });
                    console.log('✅ Popup communication test passed:', response);
                } catch (error) {
                    console.error('❌ Popup communication test failed:', error);
                }
            } else {
                console.log('⚠️ Chrome runtime not available (not in extension context)');
            }
        }

        async function testContentScriptCommunication() {
            console.log('Testing content script communication...');
            
            if (typeof chrome !== 'undefined' && chrome.tabs) {
                try {
                    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
                    if (tab) {
                        const response = await chrome.tabs.sendMessage(tab.id, { type: 'GET_VIDEO_COUNT' });
                        console.log('✅ Content script communication test passed:', response);
                    } else {
                        console.log('❌ No active tab found');
                    }
                } catch (error) {
                    console.error('❌ Content script communication test failed:', error);
                }
            } else {
                console.log('⚠️ Chrome tabs API not available (not in extension context)');
            }
        }

        async function testBackgroundCommunication() {
            console.log('Testing background script communication...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                try {
                    const response = await chrome.runtime.sendMessage({ type: 'GET_SETTINGS' });
                    console.log('✅ Background communication test passed:', response);
                } catch (error) {
                    console.error('❌ Background communication test failed:', error);
                }
            } else {
                console.log('⚠️ Chrome runtime not available (not in extension context)');
            }
        }

        function clearLog() {
            logOutput.textContent = '';
            console.log('Log cleared');
        }

        // Setup opacity slider
        document.getElementById('opacity').addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            document.getElementById('opacityValue').textContent = Math.round(value * 100) + '%';
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧪 Settings Test Suite loaded');
            checkSettingsStatus();
            refreshCurrentSettings();
            loadFormSettings();
        });
    </script>
</body>
</html>
