// YouTube Verification Test Script
// Comprehensive testing to verify YouTube functionality is restored

console.log("✅ YouTube Verification Test Script");
console.log("===================================");

// Verification Test Manager
class YouTubeVerificationTest {
  constructor() {
    this.testResults = {
      environment: {},
      detection: {},
      functionality: {},
      timeline: {},
      overall: {}
    };
    this.runVerificationTests();
  }

  runVerificationTests() {
    console.log("🧪 Running comprehensive verification tests...");
    
    this.testEnvironment();
    this.testVideoDetection();
    this.testPiPFunctionality();
    this.testTimelineControl();
    this.generateVerificationReport();
  }

  testEnvironment() {
    console.log("\n1️⃣ Environment Verification");
    console.log("============================");
    
    const env = {
      isYouTube: window.location.hostname.includes('youtube.com'),
      isVideoPage: window.location.pathname.includes('/watch'),
      extensionLoaded: !!window.pipMasterInstance,
      pipSupported: document.pictureInPictureEnabled,
      videosInDOM: document.querySelectorAll('video').length
    };
    
    Object.entries(env).forEach(([key, value]) => {
      const status = value ? '✅' : '❌';
      console.log(`${status} ${key}: ${value}`);
    });
    
    this.testResults.environment = env;
    
    if (!env.isYouTube) {
      console.error("❌ CRITICAL: Not on YouTube - navigate to youtube.com");
      return false;
    }
    
    if (!env.extensionLoaded) {
      console.error("❌ CRITICAL: Extension not loaded");
      return false;
    }
    
    return true;
  }

  testVideoDetection() {
    console.log("\n2️⃣ Video Detection Verification");
    console.log("================================");
    
    // Test basic video detection
    const allVideos = document.querySelectorAll('video');
    console.log(`📹 Total videos in DOM: ${allVideos.length}`);
    
    // Test Google-style filtering
    const suitableVideos = Array.from(allVideos).filter(video => {
      const rect = video.getBoundingClientRect();
      const style = getComputedStyle(video);
      
      return (
        video.tagName === 'VIDEO' &&
        !video.disablePictureInPicture &&
        (rect.width > 0 || rect.height > 0 || video.videoWidth > 0 || video.videoHeight > 0) &&
        style.display !== 'none'
      );
    });
    
    console.log(`🎯 Google-style suitable videos: ${suitableVideos.length}`);
    
    // Test extension detection
    const trackedVideos = window.pipMasterInstance.videos ? window.pipMasterInstance.videos.size : 0;
    console.log(`🔧 Extension tracked videos: ${trackedVideos}`);
    
    // Test overlays
    const overlays = document.querySelectorAll('.pip-master-overlay');
    console.log(`🎨 Overlays created: ${overlays.length}`);
    
    // Test YouTube-specific selectors
    const youtubeVideo = document.querySelector('#movie_player video');
    const htmlPlayerVideo = document.querySelector('.html5-video-player video');
    
    console.log(`🎬 #movie_player video: ${youtubeVideo ? '✅ Found' : '❌ Not found'}`);
    console.log(`🎬 .html5-video-player video: ${htmlPlayerVideo ? '✅ Found' : '❌ Not found'}`);
    
    this.testResults.detection = {
      totalVideos: allVideos.length,
      suitableVideos: suitableVideos.length,
      trackedVideos: trackedVideos,
      overlays: overlays.length,
      youtubeVideo: !!youtubeVideo,
      htmlPlayerVideo: !!htmlPlayerVideo
    };
    
    // Detailed video analysis
    if (suitableVideos.length > 0) {
      console.log("\n📊 Video Analysis:");
      suitableVideos.forEach((video, index) => {
        const info = {
          index: index + 1,
          src: video.src || video.currentSrc || 'no src',
          readyState: video.readyState,
          dimensions: `${video.videoWidth || '?'}x${video.videoHeight || '?'}`,
          paused: video.paused,
          duration: video.duration || 'unknown',
          pipDisabled: video.disablePictureInPicture
        };
        
        console.log(`Video ${index + 1}:`, info);
        
        // Test suitability
        if (window.pipMasterInstance.isVideoSuitableForPiP) {
          const suitable = window.pipMasterInstance.isVideoSuitableForPiP(video);
          console.log(`  Suitability: ${suitable ? '✅ Suitable' : '❌ Not suitable'}`);
        }
      });
    }
    
    return suitableVideos.length > 0 && trackedVideos > 0;
  }

  async testPiPFunctionality() {
    console.log("\n3️⃣ PiP Functionality Verification");
    console.log("==================================");
    
    const testVideo = document.querySelector('#movie_player video') || 
                     document.querySelector('.html5-video-player video') || 
                     document.querySelector('video');
    
    if (!testVideo) {
      console.error("❌ No test video available");
      this.testResults.functionality = { available: false, reason: 'No video found' };
      return false;
    }
    
    console.log("🧪 Testing PiP on:", testVideo.src || testVideo.currentSrc || 'no src');
    
    try {
      // Test extension's toggle method
      if (window.pipMasterInstance.togglePiP) {
        console.log("🔧 Testing extension togglePiP method...");
        
        await window.pipMasterInstance.togglePiP(testVideo);
        
        // Check if PiP activated
        if (document.pictureInPictureElement) {
          console.log("✅ Extension PiP activation successful!");
          
          // Test exit
          setTimeout(async () => {
            try {
              await document.exitPictureInPicture();
              console.log("✅ PiP exit successful!");
            } catch (error) {
              console.warn("⚠️ PiP exit failed:", error.message);
            }
          }, 2000);
          
          this.testResults.functionality = { 
            available: true, 
            method: 'extension',
            success: true 
          };
          return true;
        } else {
          console.warn("⚠️ Extension method didn't activate PiP");
        }
      }
      
      // Fallback: Test direct browser PiP
      console.log("🔧 Testing direct browser PiP...");
      await testVideo.requestPictureInPicture();
      
      if (document.pictureInPictureElement) {
        console.log("✅ Direct browser PiP successful!");
        
        setTimeout(async () => {
          try {
            await document.exitPictureInPicture();
            console.log("✅ Direct PiP exit successful!");
          } catch (error) {
            console.warn("⚠️ Direct PiP exit failed:", error.message);
          }
        }, 2000);
        
        this.testResults.functionality = { 
          available: true, 
          method: 'direct',
          success: true,
          note: 'Extension method may need fixing'
        };
        return true;
      }
      
    } catch (error) {
      console.error("❌ PiP test failed:", error.message);
      
      this.testResults.functionality = { 
        available: false, 
        error: error.message,
        reason: this.analyzePiPError(error)
      };
      
      return false;
    }
  }

  testTimelineControl() {
    console.log("\n4️⃣ Timeline Control Verification");
    console.log("=================================");
    
    const timelineAvailable = !!window.pipMasterInstance?.timelineControl;
    console.log(`⏯️ Timeline control available: ${timelineAvailable ? '✅ Yes' : '❌ No'}`);
    
    if (timelineAvailable) {
      const timelineControl = window.pipMasterInstance.timelineControl;
      
      // Test timeline methods
      const methods = ['show', 'hide', 'toggle', 'getTimelineInfo'];
      const methodResults = {};
      
      methods.forEach(method => {
        try {
          if (typeof timelineControl[method] === 'function') {
            methodResults[method] = '✅ Available';
          } else {
            methodResults[method] = '❌ Missing';
          }
        } catch (error) {
          methodResults[method] = `❌ Error: ${error.message}`;
        }
      });
      
      console.log("Timeline methods:");
      Object.entries(methodResults).forEach(([method, status]) => {
        console.log(`  ${method}: ${status}`);
      });
      
      this.testResults.timeline = {
        available: true,
        methods: methodResults
      };
    } else {
      this.testResults.timeline = {
        available: false,
        reason: 'Timeline control not initialized'
      };
    }
    
    return timelineAvailable;
  }

  analyzePiPError(error) {
    const message = error.message.toLowerCase();
    
    if (message.includes('not allowed')) {
      return 'YouTube policy restriction or user gesture required';
    } else if (message.includes('not supported')) {
      return 'Browser does not support Picture-in-Picture';
    } else if (message.includes('disabled')) {
      return 'Picture-in-Picture disabled on this video';
    } else if (message.includes('state')) {
      return 'Video not in correct state for PiP';
    } else {
      return 'Unknown PiP restriction';
    }
  }

  generateVerificationReport() {
    console.log("\n📊 VERIFICATION REPORT");
    console.log("======================");
    
    const { environment, detection, functionality, timeline } = this.testResults;
    
    // Calculate overall score
    let score = 0;
    let maxScore = 0;
    
    // Environment score (30 points)
    maxScore += 30;
    if (environment.isYouTube) score += 10;
    if (environment.extensionLoaded) score += 10;
    if (environment.videosInDOM > 0) score += 10;
    
    // Detection score (40 points)
    maxScore += 40;
    if (detection.totalVideos > 0) score += 10;
    if (detection.suitableVideos > 0) score += 10;
    if (detection.trackedVideos > 0) score += 10;
    if (detection.overlays > 0) score += 10;
    
    // Functionality score (20 points)
    maxScore += 20;
    if (functionality.available && functionality.success) score += 20;
    else if (functionality.available) score += 10;
    
    // Timeline score (10 points)
    maxScore += 10;
    if (timeline.available) score += 10;
    
    const percentage = Math.round((score / maxScore) * 100);
    
    console.log(`🏥 Overall Score: ${score}/${maxScore} (${percentage}%)`);
    
    // Status determination
    if (percentage >= 90) {
      console.log("🎉 EXCELLENT: YouTube functionality fully restored!");
    } else if (percentage >= 70) {
      console.log("✅ GOOD: YouTube functionality mostly working");
    } else if (percentage >= 50) {
      console.log("⚠️ PARTIAL: Some YouTube functionality working");
    } else {
      console.log("❌ POOR: YouTube functionality needs significant fixes");
    }
    
    // Specific status messages
    console.log("\n📋 Detailed Status:");
    
    if (detection.trackedVideos === 0 && detection.totalVideos > 0) {
      console.log("❌ Video detection is broken - videos found but not tracked");
    } else if (detection.trackedVideos > 0 && detection.overlays === 0) {
      console.log("⚠️ Videos tracked but no overlays - overlay creation issue");
    } else if (detection.overlays > 0 && !functionality.success) {
      console.log("⚠️ Overlays present but PiP activation failing");
    } else if (functionality.success) {
      console.log("✅ Core PiP functionality working");
    }
    
    if (timeline.available) {
      console.log("✅ Timeline control feature available");
    } else {
      console.log("⚠️ Timeline control not available");
    }
    
    // Recommendations
    console.log("\n💡 Recommendations:");
    
    if (percentage < 70) {
      console.log("1. Run quickYouTubeFix() for immediate repair");
    }
    
    if (detection.trackedVideos === 0) {
      console.log("2. Run forceYouTubeDetection() to force video processing");
    }
    
    if (!functionality.success && functionality.available) {
      console.log("3. Check browser PiP permissions and YouTube policy");
    }
    
    if (!timeline.available) {
      console.log("4. Run initializeTimelineControl() to enable timeline feature");
    }
    
    this.testResults.overall = {
      score: score,
      maxScore: maxScore,
      percentage: percentage,
      status: percentage >= 70 ? 'working' : 'needs_fixes'
    };
    
    return this.testResults;
  }

  getResults() {
    return this.testResults;
  }
}

// Quick verification function
window.quickYouTubeVerification = function() {
  console.log("⚡ Quick YouTube Verification");
  console.log("============================");
  
  const checks = {
    onYouTube: window.location.hostname.includes('youtube.com'),
    extensionLoaded: !!window.pipMasterInstance,
    videosFound: document.querySelectorAll('video').length,
    videosTracked: window.pipMasterInstance?.videos?.size || 0,
    overlaysPresent: document.querySelectorAll('.pip-master-overlay').length,
    timelineAvailable: !!window.pipMasterInstance?.timelineControl
  };
  
  console.log("Quick checks:");
  Object.entries(checks).forEach(([check, result]) => {
    const status = result ? '✅' : '❌';
    console.log(`${status} ${check}: ${result}`);
  });
  
  const workingCount = Object.values(checks).filter(Boolean).length;
  const totalChecks = Object.keys(checks).length;
  const percentage = Math.round((workingCount / totalChecks) * 100);
  
  console.log(`\n📊 Quick Score: ${workingCount}/${totalChecks} (${percentage}%)`);
  
  if (percentage >= 80) {
    console.log("🎉 YouTube functionality appears to be working!");
  } else {
    console.log("⚠️ YouTube functionality needs attention");
    console.log("💡 Run 'new YouTubeVerificationTest()' for detailed analysis");
  }
  
  return { percentage, checks };
};

// Auto-run quick verification
console.log("🚀 Auto-running quick verification...");
window.quickYouTubeVerification();

console.log("\n📋 Available Verification Commands:");
console.log("===================================");
console.log("quickYouTubeVerification()           - Quick status check");
console.log("new YouTubeVerificationTest()        - Comprehensive verification");
console.log("testYouTubePiP()                     - Test PiP functionality only");
console.log("initializeTimelineControl()          - Initialize timeline feature");
