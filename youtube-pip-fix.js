// YouTube PiP Fix - Comprehensive solution for YouTube Picture-in-Picture issues
// This script provides enhanced YouTube compatibility and debugging

console.log("🎬 YouTube PiP Fix v3.0 - Loading comprehensive solution...");

class YouTubePiPFix {
  constructor() {
    this.isYouTube = window.location.hostname.includes('youtube.com');
    this.videos = new Set();
    this.overlays = new Map();
    this.retryCount = 0;
    this.maxRetries = 5;
    
    if (this.isYouTube) {
      this.init();
    }
  }

  init() {
    console.log("🎬 Initializing YouTube PiP Fix...");
    
    // Wait for page to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.start());
    } else {
      this.start();
    }
  }

  start() {
    console.log("🎬 Starting YouTube PiP Fix...");
    
    // Initial scan
    this.scanForVideos();
    
    // Set up observers
    this.setupObservers();
    
    // Set up periodic scanning
    this.setupPeriodicScanning();
    
    // Handle YouTube navigation
    this.setupNavigationHandling();
    
    console.log("🎬 YouTube PiP Fix initialized successfully");
  }

  scanForVideos() {
    console.log("🎬 Scanning for YouTube videos...");
    
    const selectors = [
      '#movie_player video',
      '.html5-video-player video', 
      'video.video-stream',
      '#ytd-player video',
      'video'
    ];

    let foundVideos = 0;
    
    for (const selector of selectors) {
      try {
        const videos = document.querySelectorAll(selector);
        for (const video of videos) {
          if (this.isValidVideo(video) && !this.videos.has(video)) {
            this.processVideo(video);
            foundVideos++;
          }
        }
      } catch (error) {
        console.warn(`🎬 Selector "${selector}" failed:`, error.message);
      }
    }
    
    console.log(`🎬 Found ${foundVideos} new videos`);
    return foundVideos > 0;
  }

  isValidVideo(video) {
    if (!video || video.tagName !== 'VIDEO') return false;
    if (this.videos.has(video)) return false;
    if (this.isAd(video)) return false;
    
    // Check if video has dimensions or is loading
    const rect = video.getBoundingClientRect();
    if (rect.width === 0 && rect.height === 0 && video.videoWidth === 0) {
      return false;
    }
    
    return true;
  }

  isAd(video) {
    // Check for YouTube ad indicators
    const moviePlayer = document.querySelector('#movie_player');
    if (moviePlayer && moviePlayer.classList.contains('ad-showing')) {
      return true;
    }
    
    const adContainer = video.closest('.ad-showing, .video-ads');
    return !!adContainer;
  }

  processVideo(video) {
    console.log("🎬 Processing video:", video.src || video.currentSrc || 'no src');
    
    this.videos.add(video);
    this.createOverlay(video);
    
    // Add event listeners
    video.addEventListener('loadedmetadata', () => {
      console.log("🎬 Video metadata loaded");
      this.updateOverlay(video);
    });
    
    video.addEventListener('play', () => {
      console.log("🎬 Video started playing");
      this.updateOverlay(video);
    });
  }

  createOverlay(video) {
    console.log("🎬 Creating overlay for video...");
    
    const overlay = document.createElement('div');
    overlay.className = 'youtube-pip-overlay';
    overlay.innerHTML = `
      <div class="youtube-pip-button" title="Picture-in-Picture (Alt+P)">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
          <path d="M19 7h-8v6h8V7zm2-4H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h18v14z"/>
        </svg>
      </div>
    `;
    
    // Style the overlay
    this.styleOverlay(overlay);
    
    // Add click handler
    overlay.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();
      this.togglePiP(video);
    });
    
    // Insert overlay
    this.insertOverlay(overlay, video);
    this.overlays.set(video, overlay);
  }

  styleOverlay(overlay) {
    overlay.style.cssText = `
      position: absolute !important;
      top: 16px !important;
      right: 16px !important;
      z-index: 10001 !important;
      pointer-events: auto !important;
      opacity: 1 !important;
      transition: all 0.2s ease !important;
    `;
    
    const button = overlay.querySelector('.youtube-pip-button');
    button.style.cssText = `
      background: rgba(0, 0, 0, 0.8) !important;
      border: 2px solid rgba(255, 255, 255, 0.3) !important;
      border-radius: 8px !important;
      padding: 8px !important;
      cursor: pointer !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      transition: all 0.2s ease !important;
      backdrop-filter: blur(4px) !important;
    `;
    
    // Hover effect
    button.addEventListener('mouseenter', () => {
      button.style.background = 'rgba(255, 0, 0, 0.9) !important';
      button.style.transform = 'scale(1.1) !important';
    });
    
    button.addEventListener('mouseleave', () => {
      button.style.background = 'rgba(0, 0, 0, 0.8) !important';
      button.style.transform = 'scale(1) !important';
    });
  }

  insertOverlay(overlay, video) {
    // Find the best container
    let container = document.querySelector('#movie_player') ||
                   document.querySelector('.html5-video-player') ||
                   video.closest('.html5-video-player') ||
                   video.parentElement;
    
    if (!container) {
      console.warn("🎬 No suitable container found");
      return;
    }
    
    // Ensure container can position overlay
    const style = window.getComputedStyle(container);
    if (style.position === 'static') {
      container.style.position = 'relative';
    }
    
    container.appendChild(overlay);
    console.log("🎬 Overlay inserted into:", container.tagName, container.className);
  }

  async togglePiP(video) {
    console.log("🎬 Toggling PiP...");
    
    try {
      if (document.pictureInPictureElement) {
        console.log("🎬 Exiting PiP...");
        await document.exitPictureInPicture();
      } else {
        console.log("🎬 Requesting PiP...");
        
        if (video.disablePictureInPicture) {
          throw new Error('PiP disabled on video element');
        }
        
        await video.requestPictureInPicture();
        console.log("🎬 PiP activated successfully!");
        this.showNotification("Picture-in-Picture activated!", "success");
      }
    } catch (error) {
      console.error("🎬 PiP failed:", error.message);
      this.showNotification(this.getErrorMessage(error), "error");
    }
  }

  getErrorMessage(error) {
    if (error.message.includes('disabled')) {
      return 'Picture-in-Picture is disabled for this video';
    } else if (error.name === 'NotAllowedError') {
      return 'Picture-in-Picture not allowed by YouTube';
    } else if (error.name === 'InvalidStateError') {
      return 'Video not ready for Picture-in-Picture';
    } else {
      return 'Picture-in-Picture not available';
    }
  }

  showNotification(message, type) {
    const notification = document.createElement('div');
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed !important;
      top: 20px !important;
      right: 20px !important;
      background: ${type === 'success' ? '#4CAF50' : '#f44336'} !important;
      color: white !important;
      padding: 12px 16px !important;
      border-radius: 8px !important;
      z-index: 10002 !important;
      font-family: Arial, sans-serif !important;
      font-size: 14px !important;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      if (notification.parentElement) {
        notification.parentElement.removeChild(notification);
      }
    }, 3000);
  }

  updateOverlay(video) {
    const overlay = this.overlays.get(video);
    if (overlay && video.readyState >= 1) {
      overlay.style.display = 'block';
    }
  }

  setupObservers() {
    // Simple mutation observer
    const observer = new MutationObserver(() => {
      if (this.videos.size === 0) {
        this.scanForVideos();
      }
    });
    
    const target = document.querySelector('ytd-app') || document.body;
    observer.observe(target, { childList: true, subtree: true });
  }

  setupPeriodicScanning() {
    setInterval(() => {
      if (this.videos.size === 0 && this.retryCount < this.maxRetries) {
        this.retryCount++;
        console.log(`🎬 Retry scan ${this.retryCount}/${this.maxRetries}`);
        this.scanForVideos();
      }
    }, 3000);
  }

  setupNavigationHandling() {
    document.addEventListener('yt-navigate-finish', () => {
      console.log("🎬 YouTube navigation detected");
      this.clearAll();
      this.retryCount = 0;
      setTimeout(() => this.scanForVideos(), 1000);
    });
  }

  clearAll() {
    this.overlays.forEach(overlay => {
      if (overlay.parentElement) {
        overlay.parentElement.removeChild(overlay);
      }
    });
    this.videos.clear();
    this.overlays.clear();
  }
}

// Initialize the fix
if (window.location.hostname.includes('youtube.com')) {
  window.youtubePiPFix = new YouTubePiPFix();
  console.log("🎬 YouTube PiP Fix loaded and ready!");
}
