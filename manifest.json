{"manifest_version": 3, "name": "PiP Master", "version": "1.0.1", "description": "Universal Picture-in-Picture extension for all video platforms with enhanced controls and customization", "author": "PiP Master Team", "permissions": ["storage", "activeTab", "scripting"], "host_permissions": ["https://*/*", "http://*/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content/content.js"], "css": ["content/content.css"], "run_at": "document_end", "all_frames": false}], "action": {"default_popup": "popup/popup.html", "default_title": "PiP Master"}, "options_page": "options/options.html", "commands": {"toggle-pip": {"suggested_key": {"default": "Alt+P", "mac": "Alt+P"}, "description": "Toggle Picture-in-Picture mode"}, "increase-opacity": {"suggested_key": {"default": "Alt+Period", "mac": "Alt+Period"}, "description": "Increase PiP window opacity"}, "decrease-opacity": {"suggested_key": {"default": "Alt+Comma", "mac": "Alt+Comma"}, "description": "Decrease PiP window opacity"}}, "web_accessible_resources": [{"resources": ["content/*"], "matches": ["<all_urls>"]}]}