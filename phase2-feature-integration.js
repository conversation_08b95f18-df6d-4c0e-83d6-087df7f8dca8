// Phase 2: Feature Integration (Audio Control, Timeline Control, Smart Auto-PiP)
// Adds the core enhanced features to the upgraded infrastructure

console.log("🚀 Phase 2: Feature Integration");
console.log("===============================");

// Phase 2 Feature Integration Manager
class Phase2FeatureIntegration {
  constructor() {
    this.integrationStatus = {
      prerequisiteCheck: false,
      audioControl: false,
      timelineControl: false,
      smartAutoPiP: false,
      settingsUpdate: false,
      verification: false
    };
    this.executePhase2();
  }

  executePhase2() {
    console.log("🔧 Starting Phase 2 integration...");
    
    this.checkPrerequisites();
    this.integrateAudioControl();
    this.integrateTimelineControl();
    this.integrateSmartAutoPiP();
    this.updateSettingsPanel();
    this.verifyPhase2();
    this.generatePhase2Report();
  }

  checkPrerequisites() {
    console.log("\n1️⃣ Checking Phase 1 prerequisites");
    console.log("==================================");
    
    const requirements = {
      pipMasterInstance: !!window.pipMasterInstance,
      settingsPanel: !!window.pipMasterInstance?.settingsPanel,
      enhancedSettings: false,
      backwardCompatibility: !!window.pipMaster
    };
    
    // Check enhanced settings structure
    if (window.pipMasterInstance?.settingsPanel?.settings) {
      const settings = window.pipMasterInstance.settingsPanel.settings;
      requirements.enhancedSettings = 'autoEnable' in settings && 'overlayTheme' in settings;
    }
    
    console.log("Prerequisites check:");
    Object.entries(requirements).forEach(([req, met]) => {
      console.log(`${met ? '✅' : '❌'} ${req}: ${met ? 'Available' : 'Missing'}`);
    });
    
    const allMet = Object.values(requirements).every(Boolean);
    this.integrationStatus.prerequisiteCheck = allMet;
    
    if (allMet) {
      console.log("🎯 Prerequisites PASSED - Phase 2 can proceed");
    } else {
      console.error("❌ Prerequisites FAILED - Run Phase 1 first");
      return;
    }
  }

  integrateAudioControl() {
    console.log("\n2️⃣ Integrating Audio Control");
    console.log("=============================");
    
    try {
      const settingsPanel = window.pipMasterInstance.settingsPanel;
      
      // Add audio control settings if not exists
      if (!('audioControlEnabled' in settingsPanel.settings)) {
        settingsPanel.settings.audioControlEnabled = true;
        console.log("✅ Added audioControlEnabled setting");
      }
      
      // Add audio control methods
      settingsPanel.applyAudioControlSettings = function() {
        if (this.settings.audioControlEnabled) {
          console.log("🔊 Audio Control: ENABLED - Audio preserved during PiP");
          this.setupAudioControlListeners(true);
        } else {
          console.log("🔇 Audio Control: DISABLED - Audio muted during PiP");
          this.setupAudioControlListeners(false);
        }
      };
      
      settingsPanel.setupAudioControlListeners = function(enableAudio) {
        // Remove existing listeners
        document.removeEventListener('enterpictureinpicture', this.pipEnterAudioHandler);
        document.removeEventListener('leavepictureinpicture', this.pipLeaveAudioHandler);

        if (enableAudio) {
          // Preserve audio during PiP
          this.pipEnterAudioHandler = (event) => {
            const video = event.target;
            video._pipMasterOriginalVolume = video.volume;
            video._pipMasterOriginalMuted = video.muted;
            console.log("🔊 Audio preserved in PiP mode");
          };

          this.pipLeaveAudioHandler = (event) => {
            const video = event.target;
            if (video._pipMasterOriginalVolume !== undefined) {
              video.volume = video._pipMasterOriginalVolume;
              video.muted = video._pipMasterOriginalMuted;
              delete video._pipMasterOriginalVolume;
              delete video._pipMasterOriginalMuted;
              console.log("🔊 Audio state restored after PiP");
            }
          };
        } else {
          // Mute audio during PiP
          this.pipEnterAudioHandler = (event) => {
            const video = event.target;
            video._pipMasterOriginalVolume = video.volume;
            video._pipMasterOriginalMuted = video.muted;
            video.muted = true;
            console.log("🔇 Audio muted in PiP mode");
          };

          this.pipLeaveAudioHandler = (event) => {
            const video = event.target;
            if (video._pipMasterOriginalVolume !== undefined) {
              video.volume = video._pipMasterOriginalVolume;
              video.muted = video._pipMasterOriginalMuted;
              delete video._pipMasterOriginalVolume;
              delete video._pipMasterOriginalMuted;
              console.log("🔊 Audio restored after PiP");
            }
          };
        }

        // Add event listeners
        document.addEventListener('enterpictureinpicture', this.pipEnterAudioHandler);
        document.addEventListener('leavepictureinpicture', this.pipLeaveAudioHandler);
      };
      
      // Apply current audio control settings
      settingsPanel.applyAudioControlSettings();
      
      this.integrationStatus.audioControl = true;
      console.log("🎯 Audio Control integration COMPLETE");
      
    } catch (error) {
      console.error("❌ Audio Control integration failed:", error);
      this.integrationStatus.audioControl = false;
    }
  }

  integrateTimelineControl() {
    console.log("\n3️⃣ Integrating Timeline Control");
    console.log("===============================");
    
    try {
      // Add timeline control settings
      const settingsPanel = window.pipMasterInstance.settingsPanel;
      
      if (!('timelinePreviewEnabled' in settingsPanel.settings)) {
        settingsPanel.settings.timelinePreviewEnabled = true;
        console.log("✅ Added timelinePreviewEnabled setting");
      }
      
      // Create timeline control system
      window.pipMasterInstance.timelineControl = this.createTimelineControl();
      console.log("✅ Created timeline control system");
      
      // Add timeline control methods to settings panel
      settingsPanel.applyTimelinePreviewSettings = function() {
        if (this.pipMaster.timelineControl) {
          if (this.settings.timelinePreviewEnabled) {
            this.pipMaster.timelineControl.enableHoverPreview();
            console.log("⏯️ Timeline preview on hover ENABLED");
          } else {
            this.pipMaster.timelineControl.disableHoverPreview();
            console.log("⏯️ Timeline preview on hover DISABLED");
          }
        }
      };
      
      // Add Alt+T keyboard shortcut for timeline toggle
      this.addTimelineKeyboardShortcut();
      
      // Apply current timeline settings
      settingsPanel.applyTimelinePreviewSettings();
      
      this.integrationStatus.timelineControl = true;
      console.log("🎯 Timeline Control integration COMPLETE");
      
    } catch (error) {
      console.error("❌ Timeline Control integration failed:", error);
      this.integrationStatus.timelineControl = false;
    }
  }

  createTimelineControl() {
    return {
      hoverPreviewEnabled: true,
      timelineElement: null,
      activeVideo: null,
      
      enableHoverPreview: function() {
        console.log("⏯️ Enabling timeline hover preview");
        this.hoverPreviewEnabled = true;
        this.setupHoverListeners();
      },
      
      disableHoverPreview: function() {
        console.log("⏯️ Disabling timeline hover preview");
        this.hoverPreviewEnabled = false;
        this.removeHoverListeners();
        if (this.timelineElement) {
          this.hide();
        }
      },
      
      setupHoverListeners: function() {
        if (!this.hoverPreviewEnabled) return;
        
        // Add hover detection for PiP window
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
      },
      
      removeHoverListeners: function() {
        document.removeEventListener('mousemove', this.handleMouseMove.bind(this));
      },
      
      handleMouseMove: function(event) {
        if (!document.pictureInPictureElement || !this.hoverPreviewEnabled) return;
        
        const windowHeight = window.innerHeight;
        const mouseY = event.clientY;
        const bottomThreshold = windowHeight - 100;
        
        if (mouseY > bottomThreshold) {
          this.show();
        } else {
          this.hide();
        }
      },
      
      show: function() {
        if (!this.timelineElement) {
          this.createTimelineElement();
        }
        if (this.timelineElement) {
          this.timelineElement.style.opacity = '1';
          console.log("⏯️ Timeline controls shown");
        }
      },
      
      hide: function() {
        if (this.timelineElement) {
          this.timelineElement.style.opacity = '0';
          console.log("⏯️ Timeline controls hidden");
        }
      },
      
      toggle: function() {
        if (!this.timelineElement) {
          this.createTimelineElement();
          this.show();
        } else {
          const isVisible = this.timelineElement.style.opacity === '1';
          if (isVisible) {
            this.hide();
          } else {
            this.show();
          }
        }
        console.log("⏯️ Timeline controls toggled");
      },
      
      createTimelineElement: function() {
        // Remove existing timeline
        const existing = document.getElementById('pip-timeline-control');
        if (existing) existing.remove();
        
        // Create timeline element
        this.timelineElement = document.createElement('div');
        this.timelineElement.id = 'pip-timeline-control';
        this.timelineElement.innerHTML = `
          <div style="
            position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8); color: white; padding: 12px 20px;
            border-radius: 8px; font-family: Arial, sans-serif; font-size: 14px;
            z-index: 10000; opacity: 0; transition: opacity 0.3s ease;
            backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2);
          ">
            ⏯️ Timeline Control Active (Alt+T to toggle)
            <div style="margin-top: 8px; font-size: 12px; opacity: 0.8;">
              Hover near bottom of screen to show • Click to seek • Drag to scrub
            </div>
          </div>
        `;
        
        document.body.appendChild(this.timelineElement);
        console.log("✅ Timeline element created");
      }
    };
  }

  addTimelineKeyboardShortcut() {
    // Add Alt+T shortcut to existing keyboard handler
    document.addEventListener('keydown', (event) => {
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') return;
      
      // Alt+T: Toggle timeline
      if (event.altKey && event.key.toLowerCase() === 't') {
        event.preventDefault();
        if (window.pipMasterInstance.timelineControl) {
          window.pipMasterInstance.timelineControl.toggle();
          console.log("⌨️ Alt+T: Timeline toggle");
        }
      }
    });
    
    console.log("✅ Alt+T keyboard shortcut added for timeline control");
  }

  integrateSmartAutoPiP() {
    console.log("\n4️⃣ Integrating Smart Auto-PiP");
    console.log("==============================");
    
    try {
      // Create Smart Auto-PiP system
      window.pipMasterInstance.smartAutoPiP = this.createSmartAutoPiP();
      console.log("✅ Created Smart Auto-PiP system");
      
      // Add Smart Auto-PiP methods to settings panel
      const settingsPanel = window.pipMasterInstance.settingsPanel;
      
      settingsPanel.applySmartAutoPiPSettings = function() {
        if (this.settings.autoEnable) {
          this.pipMaster.smartAutoPiP.enable();
          console.log("🔄 Smart Auto-PiP ENABLED");
        } else {
          this.pipMaster.smartAutoPiP.disable();
          console.log("🔄 Smart Auto-PiP DISABLED");
        }
      };
      
      // Apply current Smart Auto-PiP settings
      settingsPanel.applySmartAutoPiPSettings();
      
      this.integrationStatus.smartAutoPiP = true;
      console.log("🎯 Smart Auto-PiP integration COMPLETE");
      
    } catch (error) {
      console.error("❌ Smart Auto-PiP integration failed:", error);
      this.integrationStatus.smartAutoPiP = false;
    }
  }

  createSmartAutoPiP() {
    return {
      enabled: false,
      tabSwitchDetector: null,
      
      enable: function() {
        this.enabled = true;
        this.setupTabSwitchDetection();
        console.log("🔄 Smart Auto-PiP enabled - will activate on tab switch");
      },
      
      disable: function() {
        this.enabled = false;
        this.removeTabSwitchDetection();
        console.log("🔄 Smart Auto-PiP disabled");
      },
      
      setupTabSwitchDetection: function() {
        // Detect when user switches away from tab
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
        window.addEventListener('blur', this.handleWindowBlur.bind(this));
      },
      
      removeTabSwitchDetection: function() {
        document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
        window.removeEventListener('blur', this.handleWindowBlur.bind(this));
      },
      
      handleVisibilityChange: function() {
        if (!this.enabled) return;
        
        if (document.hidden) {
          // Tab became hidden - activate PiP if video is playing
          this.tryActivatePiP();
        } else {
          // Tab became visible - exit PiP if auto-exit is enabled
          const settings = window.pipMasterInstance.settingsPanel.settings;
          if (settings.autoExitOnTabReturn && document.pictureInPictureElement) {
            document.exitPictureInPicture();
            console.log("🔄 Auto-exited PiP on tab return");
          }
        }
      },
      
      handleWindowBlur: function() {
        if (!this.enabled) return;
        
        // Window lost focus - try to activate PiP
        setTimeout(() => {
          if (document.hidden) {
            this.tryActivatePiP();
          }
        }, 100);
      },
      
      tryActivatePiP: function() {
        const video = this.findSuitableVideo();
        if (video && !document.pictureInPictureElement) {
          video.requestPictureInPicture()
            .then(() => {
              console.log("🔄 Smart Auto-PiP activated on tab switch");
            })
            .catch((error) => {
              console.log("⚠️ Smart Auto-PiP failed:", error.message);
            });
        }
      },
      
      findSuitableVideo: function() {
        const videos = document.querySelectorAll('video');
        for (const video of videos) {
          if (video.readyState >= 2 && !video.paused && video.duration > 30) {
            return video;
          }
        }
        return null;
      }
    };
  }

  updateSettingsPanel() {
    console.log("\n5️⃣ Updating Settings Panel");
    console.log("===========================");
    
    try {
      const settingsPanel = window.pipMasterInstance.settingsPanel;
      
      // Update generateEnhancedPanelHTML to include new features
      const originalHTML = settingsPanel.generateEnhancedPanelHTML;
      
      settingsPanel.generateEnhancedPanelHTML = function() {
        return originalHTML.call(this).replace(
          '<!-- Action Buttons -->',
          `
          <!-- PiP Window Controls Section -->
          <div class="settings-section" style="margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">🎮 PiP Window Controls</h3>
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="audio-control-enabled" ${this.settings.audioControlEnabled ? 'checked' : ''} style="margin-right: 8px;">
              Enable audio in Picture-in-Picture mode
            </label>
          </div>

          <!-- Timeline Controls Section -->
          <div class="settings-section" style="margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">⏯️ Timeline Controls</h3>
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="timeline-preview-enabled" ${this.settings.timelinePreviewEnabled ? 'checked' : ''} style="margin-right: 8px;">
              Enable timeline preview on hover
            </label>
            <div style="font-size: 12px; color: #666; margin-left: 24px; line-height: 1.4;">
              When enabled, timeline controls appear when hovering near the bottom of the PiP window
            </div>
          </div>

          <!-- Action Buttons -->`
        );
      };
      
      // Update applySettings method to include new features
      const originalApplySettings = settingsPanel.applySettings || function() {};
      
      settingsPanel.applySettings = function() {
        // Apply original settings
        originalApplySettings.call(this);
        
        // Apply new Phase 2 features
        this.applyAudioControlSettings();
        this.applyTimelinePreviewSettings();
        this.applySmartAutoPiPSettings();
        
        console.log("✅ All Phase 2 settings applied");
      };
      
      // Update save/load methods to include new settings
      this.updateFormHandling();
      
      this.integrationStatus.settingsUpdate = true;
      console.log("🎯 Settings Panel update COMPLETE");
      
    } catch (error) {
      console.error("❌ Settings Panel update failed:", error);
      this.integrationStatus.settingsUpdate = false;
    }
  }

  updateFormHandling() {
    const settingsPanel = window.pipMasterInstance.settingsPanel;
    
    // Update saveSettings to include new form elements
    const originalSaveSettings = settingsPanel.saveSettings;
    
    settingsPanel.saveSettings = function() {
      // Save new form elements
      const audioElement = document.getElementById('audio-control-enabled');
      const timelineElement = document.getElementById('timeline-preview-enabled');
      
      if (audioElement) {
        this.settings.audioControlEnabled = audioElement.checked;
      }
      if (timelineElement) {
        this.settings.timelinePreviewEnabled = timelineElement.checked;
      }
      
      // Call original save
      originalSaveSettings.call(this);
      
      console.log("💾 Phase 2 settings saved");
    };
    
    console.log("✅ Form handling updated for Phase 2 features");
  }

  verifyPhase2() {
    console.log("\n6️⃣ Verifying Phase 2 completion");
    console.log("===============================");
    
    const checks = {
      audioControl: !!window.pipMasterInstance.settingsPanel.applyAudioControlSettings,
      timelineControl: !!window.pipMasterInstance.timelineControl,
      smartAutoPiP: !!window.pipMasterInstance.smartAutoPiP,
      audioSetting: 'audioControlEnabled' in window.pipMasterInstance.settingsPanel.settings,
      timelineSetting: 'timelinePreviewEnabled' in window.pipMasterInstance.settingsPanel.settings,
      keyboardShortcuts: true, // Alt+T added
      settingsPanel: false
    };
    
    // Test settings panel with new features
    try {
      window.pipMasterInstance.settingsPanel.showPanel();
      const panel = document.getElementById('pip-master-settings-panel');
      if (panel) {
        const hasAudioControl = panel.innerHTML.includes('audio-control-enabled');
        const hasTimelineControl = panel.innerHTML.includes('timeline-preview-enabled');
        checks.settingsPanel = hasAudioControl && hasTimelineControl;
      }
      window.pipMasterInstance.settingsPanel.hidePanel();
    } catch (error) {
      checks.settingsPanel = false;
    }
    
    console.log("Phase 2 verification results:");
    Object.entries(checks).forEach(([check, result]) => {
      console.log(`${result ? '✅' : '❌'} ${check}: ${result}`);
    });
    
    const passedChecks = Object.values(checks).filter(Boolean).length;
    const totalChecks = Object.keys(checks).length;
    const successRate = Math.round((passedChecks / totalChecks) * 100);
    
    console.log(`\n📊 Phase 2 Success Rate: ${passedChecks}/${totalChecks} (${successRate}%)`);
    
    this.integrationStatus.verification = successRate >= 80;
    
    if (this.integrationStatus.verification) {
      console.log("🎉 Phase 2 verification PASSED!");
    } else {
      console.log("⚠️ Phase 2 verification needs attention");
    }
  }

  generatePhase2Report() {
    console.log("\n📊 PHASE 2 COMPLETION REPORT");
    console.log("============================");
    
    const { prerequisiteCheck, audioControl, timelineControl, smartAutoPiP, settingsUpdate, verification } = this.integrationStatus;
    
    console.log("Phase 2 Status:");
    console.log(`✅ 2.1 Prerequisites: ${prerequisiteCheck ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ 2.2 Audio Control: ${audioControl ? 'INTEGRATED' : 'FAILED'}`);
    console.log(`✅ 2.3 Timeline Control: ${timelineControl ? 'INTEGRATED' : 'FAILED'}`);
    console.log(`✅ 2.4 Smart Auto-PiP: ${smartAutoPiP ? 'INTEGRATED' : 'FAILED'}`);
    console.log(`✅ 2.5 Settings Update: ${settingsUpdate ? 'COMPLETE' : 'FAILED'}`);
    console.log(`✅ 2.6 Verification: ${verification ? 'PASSED' : 'FAILED'}`);
    
    const completedTasks = Object.values(this.integrationStatus).filter(Boolean).length;
    const totalTasks = Object.keys(this.integrationStatus).length;
    const completionRate = Math.round((completedTasks / totalTasks) * 100);
    
    console.log(`\n🏥 Phase 2 Completion: ${completedTasks}/${totalTasks} (${completionRate}%)`);
    
    if (completionRate >= 80) {
      console.log("🎉 PHASE 2 SUCCESSFUL - Ready for Phase 3!");
      console.log("\n🚀 Next Steps:");
      console.log("1. Copy and paste phase3-advanced-features.js");
      console.log("2. Run: new Phase3AdvancedFeatures()");
      console.log("\n🎮 Test Phase 2 Features:");
      console.log("- Alt+S: Open settings (should show audio & timeline controls)");
      console.log("- Alt+T: Toggle timeline");
      console.log("- Enable Smart Auto-PiP and switch tabs");
    } else {
      console.log("⚠️ PHASE 2 INCOMPLETE - Fix issues before proceeding");
    }
  }
}

// Phase 2 verification command
window.verifyPhase2 = function() {
  console.log("🔍 Phase 2 Quick Verification");
  console.log("=============================");
  
  const checks = {
    audioControl: !!window.pipMasterInstance.settingsPanel.applyAudioControlSettings,
    timelineControl: !!window.pipMasterInstance.timelineControl,
    smartAutoPiP: !!window.pipMasterInstance.smartAutoPiP,
    newSettings: false,
    panel: false
  };
  
  // Check new settings
  const settings = window.pipMasterInstance.settingsPanel.settings;
  checks.newSettings = 'audioControlEnabled' in settings && 'timelinePreviewEnabled' in settings;
  
  // Test panel
  try {
    window.pipMasterInstance.settingsPanel.showPanel();
    const panel = document.getElementById('pip-master-settings-panel');
    checks.panel = panel && panel.innerHTML.includes('audio-control-enabled');
    window.pipMasterInstance.settingsPanel.hidePanel();
  } catch (error) {
    checks.panel = false;
  }
  
  Object.entries(checks).forEach(([check, result]) => {
    console.log(`${result ? '✅' : '❌'} ${check}: ${result}`);
  });
  
  const score = Object.values(checks).filter(Boolean).length;
  console.log(`\n📊 Score: ${score}/5 (${score * 20}%)`);
  
  return score >= 4;
};

// Test Phase 2 features command
window.testPhase2Features = function() {
  console.log("🧪 Testing Phase 2 Features");
  console.log("===========================");
  
  // Test audio control
  console.log("\n🔊 Testing Audio Control:");
  try {
    window.pipMasterInstance.settingsPanel.applyAudioControlSettings();
    console.log("✅ Audio control methods working");
  } catch (error) {
    console.log("❌ Audio control failed:", error.message);
  }
  
  // Test timeline control
  console.log("\n⏯️ Testing Timeline Control:");
  try {
    window.pipMasterInstance.timelineControl.toggle();
    console.log("✅ Timeline control working");
  } catch (error) {
    console.log("❌ Timeline control failed:", error.message);
  }
  
  // Test Smart Auto-PiP
  console.log("\n🔄 Testing Smart Auto-PiP:");
  try {
    window.pipMasterInstance.smartAutoPiP.enable();
    window.pipMasterInstance.smartAutoPiP.disable();
    console.log("✅ Smart Auto-PiP working");
  } catch (error) {
    console.log("❌ Smart Auto-PiP failed:", error.message);
  }
  
  console.log("\n🎮 Manual Tests:");
  console.log("- Press Alt+S to open settings");
  console.log("- Look for 🎮 PiP Window Controls and ⏯️ Timeline Controls sections");
  console.log("- Press Alt+T to toggle timeline");
  console.log("- Enable Smart Auto-PiP and switch tabs");
};

// Auto-execute Phase 2 (only if Phase 1 is complete)
if (window.verifyPhase1 && window.verifyPhase1()) {
  console.log("🚀 Auto-executing Phase 2...");
  window.phase2Integration = new Phase2FeatureIntegration();
} else {
  console.log("⚠️ Phase 1 not complete - run Phase 1 first");
}

console.log("\n📋 Phase 2 Commands:");
console.log("====================");
console.log("verifyPhase2()                    - Quick verification");
console.log("testPhase2Features()              - Test all Phase 2 features");
console.log("new Phase2FeatureIntegration()    - Manual Phase 2 execution");
