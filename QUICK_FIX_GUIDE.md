# PiP Master Extension - Quick Fix Guide

## 🚨 Extension Not Working? Follow These Steps

### Step 1: Load the Extension in Chrome

1. **Open Chrome** and navigate to `chrome://extensions/`
2. **Enable Developer Mode** (toggle in top-right corner)
3. **Click "Load unpacked"**
4. **Select the PiP folder** (`c:\git_projects\PIP`)
5. **Verify the extension appears** in the list with no errors

### Step 2: Check for Errors

1. **Look for red error messages** in the extension card
2. **Click "Errors"** if any appear to see details
3. **Check the console** for any JavaScript errors

### Step 3: Test Basic Functionality

1. **Open the test page**: `file:///c:/git_projects/PIP/test-page.html`
2. **Open Chrome DevTools** (F12)
3. **Check the Console tab** for PiP Master logs
4. **Look for these messages**:
   - "PiP Master: Background script starting..."
   - "PiP Master: Content script starting..."
   - "PiP Master: Initializing content script..."

### Step 4: Manual Testing

1. **Play a video** on the test page
2. **Look for overlay button** in top-right corner of video
3. **Try Alt+P shortcut** to toggle PiP
4. **Click extension icon** in toolbar to open popup

### Step 5: Common Issues & Fixes

#### Issue: Extension doesn't load
- **Check manifest.json** for syntax errors
- **Verify all files exist** (especially background.js, content/content.js)
- **Reload the extension** after any changes

#### Issue: Content script not injecting
- **Check permissions** in manifest.json
- **Verify content script paths** are correct
- **Try refreshing the test page**

#### Issue: No overlay appears
- **Check console** for JavaScript errors
- **Verify CSS is loading** (content/content.css)
- **Test on different video sites**

#### Issue: PiP doesn't activate
- **Check browser PiP support**: `document.pictureInPictureEnabled`
- **Verify video element** is suitable for PiP
- **Check for DRM restrictions** on video content

### Step 6: Debug Console Commands

Open DevTools Console and run these commands:

```javascript
// Check if extension is loaded
console.log('Extension loaded:', !!window.pipMasterContentLoaded);

// Check PiP support
console.log('PiP supported:', 'pictureInPictureEnabled' in document);
console.log('PiP enabled:', document.pictureInPictureEnabled);

// Find videos
console.log('Videos found:', document.querySelectorAll('video').length);

// Check for overlays
console.log('Overlays found:', document.querySelectorAll('.pip-master-overlay').length);

// Test PiP manually
const video = document.querySelector('video');
if (video) {
  video.requestPictureInPicture()
    .then(() => console.log('PiP activated'))
    .catch(err => console.error('PiP failed:', err));
}
```

### Step 7: Force Reload Everything

If nothing works:

1. **Remove the extension** from chrome://extensions/
2. **Close all Chrome windows**
3. **Restart Chrome**
4. **Load the extension again**
5. **Test on a fresh tab**

### Step 8: Check Extension Logs

1. **Go to chrome://extensions/**
2. **Click "service worker"** next to PiP Master
3. **Check for background script logs**
4. **Look for error messages**

## 🔧 Recent Changes Made

I've added extensive logging to help debug issues:

- **Background script**: Now logs all operations
- **Content script**: Detailed initialization logging
- **Manifest**: Simplified to avoid icon issues
- **Error handling**: Better error reporting

## 📊 Expected Console Output

When working correctly, you should see:

```
PiP Master: Background script starting...
PiP Master: Initializing background service...
PiP Master: Content script starting...
PiP Master: Detecting platform for hostname: ...
PiP Master: Initializing content script...
PiP Master: Requesting settings from background...
PiP Master: Settings loaded: ...
```

## 🆘 Still Not Working?

If the extension still doesn't work after following these steps:

1. **Check Chrome version** (need 88+ for PiP support)
2. **Try incognito mode** to rule out conflicts
3. **Disable other extensions** temporarily
4. **Check for corporate/school restrictions**
5. **Try a different computer/browser**

## 📝 Report Issues

If you find specific errors, note:
- Chrome version
- Operating system
- Exact error messages
- Steps to reproduce
- Console logs
