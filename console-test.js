// PiP Master Console Test Script
// Copy and paste this into the browser console to test the extension

console.log("🔧 PiP Master Extension Test Script");
console.log("=====================================");

// Test 1: Check if extension is loaded
console.log("\n1. Extension Loading Test:");
const extensionLoaded = !!window.pipMasterContentLoaded;
const instanceAvailable = !!window.pipMasterInstance;
const debugAvailable = !!window.pipMasterDebug;

console.log("✓ Content script loaded:", extensionLoaded);
console.log("✓ Instance available:", instanceAvailable);
console.log("✓ Debug functions available:", debugAvailable);

if (!extensionLoaded) {
  console.error("❌ Extension not loaded! Check chrome://extensions/");
  console.log("Stop here and fix extension loading first.");
}

// Test 2: Check PiP API support
console.log("\n2. PiP API Support Test:");
const pipSupported = 'pictureInPictureEnabled' in document;
const pipEnabled = document.pictureInPictureEnabled;

console.log("✓ PiP API supported:", pipSupported);
console.log("✓ PiP enabled:", pipEnabled);

if (!pipSupported) {
  console.error("❌ PiP API not supported in this browser!");
}
if (!pipEnabled) {
  console.warn("⚠️ PiP disabled by site policy or browser settings");
}

// Test 3: Check for video elements
console.log("\n3. Video Detection Test:");
const videos = document.querySelectorAll('video');
console.log("✓ Videos in DOM:", videos.length);

if (videos.length === 0) {
  console.warn("⚠️ No video elements found in DOM");
  console.log("Make sure you're on a page with videos (like the test page)");
} else {
  videos.forEach((video, i) => {
    console.log(`  Video ${i + 1}:`, {
      src: video.src || video.currentSrc || "no src",
      readyState: video.readyState,
      dimensions: `${video.videoWidth || '?'}x${video.videoHeight || '?'}`,
      paused: video.paused,
      duration: video.duration || 'unknown'
    });
  });
}

// Test 4: Check extension video tracking
console.log("\n4. Extension Video Tracking Test:");
if (window.pipMasterDebug) {
  const trackedCount = window.pipMasterDebug.getVideoCount();
  console.log("✓ Videos tracked by extension:", trackedCount);
  
  if (trackedCount === 0 && videos.length > 0) {
    console.warn("⚠️ Extension found no videos but DOM has videos");
    console.log("Running manual video scan...");
    window.pipMasterDebug.scanForVideos();
    
    setTimeout(() => {
      const newCount = window.pipMasterDebug.getVideoCount();
      console.log("✓ Videos after manual scan:", newCount);
      
      if (newCount > 0) {
        console.log("✅ Manual scan successful!");
        window.pipMasterDebug.listVideos();
      } else {
        console.error("❌ Manual scan failed - check console for errors");
      }
    }, 1000);
  } else if (trackedCount > 0) {
    console.log("✅ Extension successfully tracking videos!");
    window.pipMasterDebug.listVideos();
  }
} else {
  console.error("❌ Debug functions not available");
}

// Test 5: Check for overlays
console.log("\n5. Overlay Detection Test:");
setTimeout(() => {
  const overlays = document.querySelectorAll('.pip-master-overlay');
  console.log("✓ Overlays found:", overlays.length);
  
  if (overlays.length === 0) {
    console.warn("⚠️ No overlays found");
    console.log("Overlays should appear on suitable videos");
  } else {
    overlays.forEach((overlay, i) => {
      const style = getComputedStyle(overlay);
      console.log(`  Overlay ${i + 1}:`, {
        display: style.display,
        visibility: style.visibility,
        opacity: style.opacity,
        position: style.position,
        zIndex: style.zIndex
      });
    });
  }
}, 2000);

// Test 6: Test PiP functionality
console.log("\n6. PiP Functionality Test:");
setTimeout(() => {
  if (videos.length > 0 && pipSupported && pipEnabled) {
    console.log("Testing manual PiP on first video...");
    const testVideo = videos[0];
    
    testVideo.requestPictureInPicture()
      .then(() => {
        console.log("✅ Manual PiP test successful!");
        // Exit PiP after 2 seconds
        setTimeout(() => {
          if (document.pictureInPictureElement) {
            document.exitPictureInPicture();
            console.log("✓ PiP exited");
          }
        }, 2000);
      })
      .catch(error => {
        console.error("❌ Manual PiP test failed:", error.message);
        console.log("This might be due to video not being ready or DRM restrictions");
      });
  } else {
    console.log("⏭️ Skipping PiP test (no videos or PiP not supported)");
  }
}, 3000);

// Test 7: Test extension PiP
console.log("\n7. Extension PiP Test:");
setTimeout(() => {
  if (window.pipMasterDebug && window.pipMasterDebug.getVideoCount() > 0) {
    console.log("Testing extension PiP functionality...");
    window.pipMasterDebug.testPiP(0);
  } else {
    console.log("⏭️ Skipping extension PiP test (no tracked videos)");
  }
}, 4000);

// Summary
setTimeout(() => {
  console.log("\n📊 Test Summary:");
  console.log("================");
  console.log("Extension loaded:", extensionLoaded ? "✅" : "❌");
  console.log("PiP API support:", pipSupported ? "✅" : "❌");
  console.log("Videos in DOM:", videos.length);
  console.log("Videos tracked:", window.pipMasterDebug ? window.pipMasterDebug.getVideoCount() : "N/A");
  console.log("Overlays found:", document.querySelectorAll('.pip-master-overlay').length);
  
  console.log("\n💡 Next Steps:");
  if (!extensionLoaded) {
    console.log("1. Fix extension loading in chrome://extensions/");
  } else if (videos.length === 0) {
    console.log("1. Navigate to a page with videos");
  } else if (window.pipMasterDebug && window.pipMasterDebug.getVideoCount() === 0) {
    console.log("1. Check console for video detection errors");
    console.log("2. Try: window.pipMasterDebug.scanForVideos()");
  } else {
    console.log("1. Extension appears to be working!");
    console.log("2. Try clicking overlay buttons or pressing Alt+P");
  }
}, 5000);

console.log("\n⏳ Running tests... (will take ~5 seconds)");
console.log("Watch the console for results...");
