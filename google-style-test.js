// Google-Style Enhancement Test Script
// Tests the enhanced PiP Master with Google's proven approach

console.log("🔍 Google-Style Enhancement Test Script");
console.log("=======================================");

// Test the enhanced implementation
window.testGoogleStyleEnhancements = function() {
  console.log("🧪 Testing Google-Style Enhancements");
  console.log("====================================");
  
  // Check environment
  const isYouTube = window.location.hostname.includes('youtube.com');
  const isVideoPage = window.location.pathname.includes('/watch');
  
  console.log(`Environment: ${isYouTube ? 'YouTube' : 'Other'} ${isVideoPage ? '(video page)' : ''}`);
  
  if (!window.pipMasterInstance) {
    console.error("❌ Extension instance not available!");
    return false;
  }
  
  console.log(`Platform detected: ${window.pipMasterInstance.platform}`);
  
  // Test 1: Google-style video detection
  console.log("\n1️⃣ Testing Google-Style Video Detection:");
  console.log("========================================");
  
  const videos = Array.from(document.querySelectorAll('video')).filter(video => {
    const rect = video.getBoundingClientRect();
    const style = getComputedStyle(video);
    
    return (
      video.tagName === 'VIDEO' &&
      !video.disablePictureInPicture &&
      (rect.width > 0 || rect.height > 0 || video.videoWidth > 0 || video.videoHeight > 0) &&
      style.display !== 'none'
    );
  });
  
  console.log(`Google-style detection found: ${videos.length} videos`);
  
  if (videos.length === 0) {
    console.error("❌ No videos found with Google-style detection");
    return false;
  }
  
  // Test 2: Google-style suitability checks
  console.log("\n2️⃣ Testing Google-Style Suitability Checks:");
  console.log("============================================");
  
  let suitableCount = 0;
  videos.forEach((video, index) => {
    console.log(`\nVideo ${index + 1}:`);
    console.log(`  Source: ${video.src || video.currentSrc || 'no src'}`);
    console.log(`  Dimensions: ${video.videoWidth || '?'}x${video.videoHeight || '?'}`);
    console.log(`  Ready state: ${video.readyState}`);
    console.log(`  PiP disabled: ${video.disablePictureInPicture}`);
    
    // Test Google-style suitability
    const suitable = window.pipMasterInstance.isVideoSuitableForPiP(video);
    console.log(`  Google-style suitability: ${suitable ? '✅ PASSED' : '❌ FAILED'}`);
    
    if (suitable) {
      suitableCount++;
    }
  });
  
  console.log(`\n📊 Suitability Results: ${suitableCount}/${videos.length} videos passed`);
  
  // Test 3: Google-style ad detection
  console.log("\n3️⃣ Testing Google-Style Ad Detection:");
  console.log("=====================================");
  
  videos.forEach((video, index) => {
    console.log(`\nVideo ${index + 1} ad checks:`);
    
    // Test obvious ad detection
    const isObviousAd = window.pipMasterInstance.isObviousAd(video);
    console.log(`  Obvious ad: ${isObviousAd ? '🚫 YES' : '✅ NO'}`);
    
    // Test legacy ad detection for comparison
    const isLikelyAd = window.pipMasterInstance.isLikelyVideoAd(video);
    console.log(`  Legacy ad detection: ${isLikelyAd ? '🚫 YES' : '✅ NO'}`);
    
    if (isObviousAd !== isLikelyAd) {
      console.log(`  ⚠️ Ad detection methods disagree!`);
    }
  });
  
  // Test 4: Extension video processing
  console.log("\n4️⃣ Testing Extension Video Processing:");
  console.log("======================================");
  
  const trackedBefore = window.pipMasterInstance.videos.size;
  console.log(`Videos tracked before: ${trackedBefore}`);
  
  // Force a scan with the enhanced method
  window.pipMasterInstance.performUniversalVideoScan();
  
  const trackedAfter = window.pipMasterInstance.videos.size;
  console.log(`Videos tracked after: ${trackedAfter}`);
  console.log(`New videos processed: ${trackedAfter - trackedBefore}`);
  
  // Test 5: Overlay creation
  console.log("\n5️⃣ Testing Overlay Creation:");
  console.log("============================");
  
  const overlaysBefore = document.querySelectorAll('.pip-master-overlay').length;
  console.log(`Overlays before: ${overlaysBefore}`);
  
  // Wait a moment for overlays to be created
  setTimeout(() => {
    const overlaysAfter = document.querySelectorAll('.pip-master-overlay').length;
    console.log(`Overlays after: ${overlaysAfter}`);
    
    if (overlaysAfter > overlaysBefore) {
      console.log("✅ New overlays created successfully");
    } else if (overlaysAfter === 0) {
      console.warn("⚠️ No overlays found - check overlay creation logic");
    }
  }, 1000);
  
  return { videos: videos.length, suitable: suitableCount, tracked: trackedAfter };
};

// Test Google-style PiP activation
window.testGoogleStylePiP = function(videoIndex = 0) {
  console.log(`🚀 Testing Google-Style PiP Activation (Video ${videoIndex + 1})`);
  console.log("===========================================================");
  
  const videos = document.querySelectorAll('video');
  
  if (videos.length === 0) {
    console.error("❌ No videos found");
    return false;
  }
  
  if (videoIndex >= videos.length) {
    console.error(`❌ Video index ${videoIndex} not found (only ${videos.length} videos)`);
    return false;
  }
  
  const video = videos[videoIndex];
  console.log("Testing video:", {
    src: video.src || video.currentSrc || "no src",
    readyState: video.readyState,
    dimensions: `${video.videoWidth}x${video.videoHeight}`,
    pipDisabled: video.disablePictureInPicture
  });
  
  // Test Google-style suitability first
  if (window.pipMasterInstance) {
    const suitable = window.pipMasterInstance.isVideoSuitableForPiP(video);
    console.log(`Google-style suitability: ${suitable ? '✅ PASSED' : '❌ FAILED'}`);
    
    if (!suitable) {
      console.warn("⚠️ Video failed Google-style suitability check");
    }
  }
  
  // Test Google-style PiP activation
  console.log("🚀 Attempting Google-style PiP activation...");
  
  if (window.pipMasterInstance) {
    window.pipMasterInstance.togglePiP(video)
      .then(() => {
        console.log("✅ Google-style PiP activation successful!");
        console.log("🎉 YouTube PiP is working with Google's approach!");
        
        // Auto-exit after 3 seconds
        setTimeout(() => {
          if (document.pictureInPictureElement) {
            document.exitPictureInPicture();
            console.log("✅ PiP exited automatically");
          }
        }, 3000);
      })
      .catch(error => {
        console.error("❌ Google-style PiP activation failed:", error.message);
        
        // Test direct browser PiP as fallback
        console.log("🔄 Testing direct browser PiP...");
        video.requestPictureInPicture()
          .then(() => {
            console.log("✅ Direct browser PiP works - issue is in extension logic");
            setTimeout(() => {
              if (document.pictureInPictureElement) {
                document.exitPictureInPicture();
              }
            }, 3000);
          })
          .catch(directError => {
            console.error("❌ Direct browser PiP also failed:", directError.message);
            console.log("💡 This indicates a browser or platform restriction");
          });
      });
  } else {
    // Test direct browser PiP
    video.requestPictureInPicture()
      .then(() => {
        console.log("✅ Direct browser PiP successful!");
        setTimeout(() => {
          if (document.pictureInPictureElement) {
            document.exitPictureInPicture();
          }
        }, 3000);
      })
      .catch(error => {
        console.error("❌ Direct browser PiP failed:", error.message);
      });
  }
};

// Test automatic video selection (Google's approach)
window.testGoogleStyleAutoSelection = function() {
  console.log("🎯 Testing Google-Style Automatic Video Selection");
  console.log("=================================================");
  
  const videos = Array.from(document.querySelectorAll('video')).filter(v => {
    const rect = v.getBoundingClientRect();
    return (
      v.tagName === 'VIDEO' &&
      !v.disablePictureInPicture &&
      rect.width > 0 && rect.height > 0 &&
      getComputedStyle(v).display !== 'none'
    );
  });
  
  if (videos.length === 0) {
    console.error("❌ No suitable videos found");
    return null;
  }
  
  console.log(`Found ${videos.length} suitable videos`);
  
  // Google's approach: Select the largest video
  const selectedVideo = videos.reduce((largest, current) => {
    const largestRect = largest.getBoundingClientRect();
    const currentRect = current.getBoundingClientRect();
    const largestArea = largestRect.width * largestRect.height;
    const currentArea = currentRect.width * currentRect.height;
    return currentArea > largestArea ? current : largest;
  });
  
  console.log("Selected video (largest):", {
    src: selectedVideo.src || selectedVideo.currentSrc || "no src",
    dimensions: `${selectedVideo.videoWidth}x${selectedVideo.videoHeight}`,
    area: `${Math.round(selectedVideo.getBoundingClientRect().width * selectedVideo.getBoundingClientRect().height)} pixels²`
  });
  
  // Test PiP on selected video
  if (window.pipMasterInstance) {
    console.log("🚀 Testing PiP on auto-selected video...");
    window.pipMasterInstance.togglePiP(selectedVideo);
  }
  
  return selectedVideo;
};

// Auto-run tests on YouTube
if (window.location.hostname.includes('youtube.com')) {
  console.log("🎬 Auto-running Google-style tests on YouTube...");
  
  // Wait for page to load
  setTimeout(() => {
    const results = window.testGoogleStyleEnhancements();
    
    if (results && results.suitable > 0) {
      console.log("\n🎉 Google-style enhancements are working!");
      console.log("✅ Videos detected and processed successfully");
      
      // Test PiP activation
      setTimeout(() => {
        window.testGoogleStylePiP(0);
      }, 2000);
    } else {
      console.log("\n⚠️ Issues detected with Google-style enhancements");
      console.log("Running diagnostic...");
      
      setTimeout(() => {
        window.testGoogleStyleAutoSelection();
      }, 1000);
    }
  }, 3000);
}

// Display available commands
console.log("\n📋 Available Google-Style Test Commands:");
console.log("========================================");
console.log("testGoogleStyleEnhancements()    - Test all Google-style features");
console.log("testGoogleStylePiP(index)        - Test PiP on specific video");
console.log("testGoogleStyleAutoSelection()   - Test automatic video selection");
console.log("");
console.log("🚀 Quick start: testGoogleStyleEnhancements() (auto-runs on YouTube)");
console.log("🧪 Test PiP: testGoogleStylePiP(0)");
console.log("🎯 Auto-select: testGoogleStyleAutoSelection()");
