<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌐 Universal PiP Master Test Suite</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .header {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .test-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .test-card h3 {
            margin-top: 0;
            color: #4a5568;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        .video-container {
            margin: 15px 0;
            text-align: center;
            position: relative;
        }
        video {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: transform 0.2s;
        }
        .test-btn:hover {
            transform: translateY(-2px);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 6px;
            font-size: 14px;
        }
        .result-pass { background: #c6f6d5; color: #22543d; }
        .result-fail { background: #fed7d7; color: #742a2a; }
        .result-pending { background: #e2e8f0; color: #4a5568; }
        .platform-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .platform-link {
            display: block;
            padding: 15px;
            background: white;
            border-radius: 8px;
            text-decoration: none;
            color: #4a5568;
            text-align: center;
            transition: transform 0.2s;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .platform-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        .diagnostic-info {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .instructions {
            background: #ebf8ff;
            border-left: 4px solid #3182ce;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌐 Universal PiP Master Test Suite</h1>
        <p>Comprehensive testing for cross-platform Picture-in-Picture functionality</p>
        <div id="extensionStatus" class="diagnostic-info">
            Checking extension status...
        </div>
    </div>

    <div class="instructions">
        <h3>🧪 Testing Protocol</h3>
        <ol>
            <li><strong>Extension Check:</strong> Verify PiP Master is loaded and functioning</li>
            <li><strong>Local Testing:</strong> Test with embedded videos on this page</li>
            <li><strong>Platform Testing:</strong> Test on major video platforms</li>
            <li><strong>Edge Cases:</strong> Test dynamic content, iframes, and custom players</li>
        </ol>
    </div>

    <div class="test-grid">
        <!-- Local Video Tests -->
        <div class="test-card">
            <h3>📹 Local Video Tests</h3>
            <div class="video-container">
                <video controls width="300" id="testVideo1">
                    <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>
            <button class="test-btn" onclick="testLocalVideo('testVideo1')">Test PiP</button>
            <button class="test-btn" onclick="testKeyboardShortcut()">Test Alt+P</button>
            <div id="localTestResult" class="test-result result-pending">
                Ready for testing
            </div>
        </div>

        <!-- Dynamic Video Tests -->
        <div class="test-card">
            <h3>⚡ Dynamic Content Tests</h3>
            <div id="dynamicVideoContainer"></div>
            <button class="test-btn" onclick="addDynamicVideo()">Add Dynamic Video</button>
            <button class="test-btn" onclick="removeDynamicVideo()">Remove Video</button>
            <button class="test-btn" onclick="testSPANavigation()">Simulate SPA Navigation</button>
            <div id="dynamicTestResult" class="test-result result-pending">
                No dynamic content yet
            </div>
        </div>

        <!-- Platform Detection Tests -->
        <div class="test-card">
            <h3>🔍 Platform Detection</h3>
            <div id="platformInfo" class="diagnostic-info">
                Detecting platform...
            </div>
            <button class="test-btn" onclick="testPlatformDetection()">Test Detection</button>
            <button class="test-btn" onclick="testVideoSelectors()">Test Selectors</button>
            <div id="platformTestResult" class="test-result result-pending">
                Ready for platform testing
            </div>
        </div>

        <!-- Error Handling Tests -->
        <div class="test-card">
            <h3>🛡️ Error Handling Tests</h3>
            <button class="test-btn" onclick="testInvalidVideo()">Test Invalid Video</button>
            <button class="test-btn" onclick="testDisabledPiP()">Test Disabled PiP</button>
            <button class="test-btn" onclick="testNetworkError()">Test Network Error</button>
            <div id="errorTestResult" class="test-result result-pending">
                Ready for error testing
            </div>
        </div>
    </div>

    <!-- Platform Testing Links -->
    <div class="test-card">
        <h3>🌐 Cross-Platform Testing</h3>
        <p>Test the extension on these major video platforms:</p>
        <div class="platform-links">
            <a href="https://www.youtube.com/watch?v=dQw4w9WgXcQ" target="_blank" class="platform-link">
                📺 YouTube<br><small>Standard video platform</small>
            </a>
            <a href="https://vimeo.com/148751763" target="_blank" class="platform-link">
                🎬 Vimeo<br><small>Professional video hosting</small>
            </a>
            <a href="https://www.twitch.tv/directory/game/Just%20Chatting" target="_blank" class="platform-link">
                🎮 Twitch<br><small>Live streaming platform</small>
            </a>
            <a href="https://www.dailymotion.com/video/x7tgad0" target="_blank" class="platform-link">
                📱 Dailymotion<br><small>Alternative video platform</small>
            </a>
            <a href="https://www.facebook.com/watch" target="_blank" class="platform-link">
                👥 Facebook Watch<br><small>Social media videos</small>
            </a>
            <a href="https://twitter.com/search?q=video&src=typed_query&f=video" target="_blank" class="platform-link">
                🐦 Twitter/X<br><small>Social media videos</small>
            </a>
        </div>
    </div>

    <!-- Diagnostic Information -->
    <div class="test-card">
        <h3>🔧 Diagnostic Information</h3>
        <div id="diagnosticOutput" class="diagnostic-info">
            Loading diagnostics...
        </div>
        <button class="test-btn" onclick="runDiagnostics()">Refresh Diagnostics</button>
        <button class="test-btn" onclick="exportTestResults()">Export Results</button>
    </div>

    <script>
        let testResults = {};
        let dynamicVideoCount = 0;

        // Initialize testing suite
        window.addEventListener('load', () => {
            checkExtensionStatus();
            runDiagnostics();
            testPlatformDetection();
        });

        function checkExtensionStatus() {
            const statusDiv = document.getElementById('extensionStatus');
            
            if (typeof window.pipMasterContentLoaded !== 'undefined') {
                statusDiv.innerHTML = `
                    ✅ <strong>Extension Status:</strong> Loaded and Active<br>
                    📊 <strong>Diagnostics:</strong> ${JSON.stringify(window.pipMasterDiagnostics || {}, null, 2)}
                `;
                statusDiv.className = 'diagnostic-info result-pass';
            } else {
                statusDiv.innerHTML = `
                    ❌ <strong>Extension Status:</strong> Not Detected<br>
                    ⚠️ Please ensure PiP Master extension is installed and enabled
                `;
                statusDiv.className = 'diagnostic-info result-fail';
            }
        }

        function testLocalVideo(videoId) {
            const video = document.getElementById(videoId);
            const resultDiv = document.getElementById('localTestResult');
            
            if (!video) {
                updateResult('localTestResult', 'fail', 'Video element not found');
                return;
            }

            // Check if extension detected the video
            const hasOverlay = video.parentElement.querySelector('.pip-master-overlay');
            
            if (hasOverlay) {
                updateResult('localTestResult', 'pass', 'Extension detected video and created overlay');
                
                // Try to activate PiP
                hasOverlay.click();
                
                setTimeout(() => {
                    if (document.pictureInPictureElement) {
                        updateResult('localTestResult', 'pass', 'Picture-in-Picture activated successfully!');
                    } else {
                        updateResult('localTestResult', 'fail', 'PiP activation failed');
                    }
                }, 1000);
            } else {
                updateResult('localTestResult', 'fail', 'Extension did not detect video - check console for errors');
            }
        }

        function testKeyboardShortcut() {
            // Simulate Alt+P keypress
            const event = new KeyboardEvent('keydown', {
                key: 'p',
                altKey: true,
                bubbles: true
            });
            
            document.dispatchEvent(event);
            
            setTimeout(() => {
                if (document.pictureInPictureElement) {
                    updateResult('localTestResult', 'pass', 'Keyboard shortcut (Alt+P) works!');
                } else {
                    updateResult('localTestResult', 'fail', 'Keyboard shortcut failed');
                }
            }, 500);
        }

        function addDynamicVideo() {
            dynamicVideoCount++;
            const container = document.getElementById('dynamicVideoContainer');
            
            const videoElement = document.createElement('video');
            videoElement.controls = true;
            videoElement.width = 300;
            videoElement.id = `dynamicVideo${dynamicVideoCount}`;
            videoElement.innerHTML = `
                <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4" type="video/mp4">
            `;
            
            container.appendChild(videoElement);
            
            // Check if extension detects the dynamic video
            setTimeout(() => {
                const hasOverlay = videoElement.parentElement.querySelector('.pip-master-overlay');
                if (hasOverlay) {
                    updateResult('dynamicTestResult', 'pass', `Dynamic video ${dynamicVideoCount} detected by extension`);
                } else {
                    updateResult('dynamicTestResult', 'fail', `Dynamic video ${dynamicVideoCount} not detected`);
                }
            }, 2000);
        }

        function removeDynamicVideo() {
            const container = document.getElementById('dynamicVideoContainer');
            if (container.lastElementChild) {
                container.removeChild(container.lastElementChild);
                updateResult('dynamicTestResult', 'pass', 'Dynamic video removed');
            }
        }

        function testSPANavigation() {
            // Simulate SPA navigation by changing URL
            const originalUrl = window.location.href;
            history.pushState({}, '', window.location.href + '#test-navigation');
            
            setTimeout(() => {
                history.pushState({}, '', originalUrl);
                updateResult('dynamicTestResult', 'pass', 'SPA navigation simulation completed');
            }, 1000);
        }

        function testPlatformDetection() {
            const platformInfo = document.getElementById('platformInfo');
            const hostname = window.location.hostname;
            
            platformInfo.innerHTML = `
                <strong>Current Platform:</strong> ${hostname}<br>
                <strong>Detected As:</strong> Generic (test page)<br>
                <strong>User Agent:</strong> ${navigator.userAgent.substring(0, 100)}...
            `;
        }

        function testVideoSelectors() {
            const videos = document.querySelectorAll('video');
            const customPlayers = document.querySelectorAll('.jwplayer, .flowplayer, .video-js');
            
            updateResult('platformTestResult', 'pass', 
                `Found ${videos.length} video elements and ${customPlayers.length} custom players`);
        }

        function testInvalidVideo() {
            // Create an invalid video element
            const invalidVideo = document.createElement('video');
            invalidVideo.style.display = 'none';
            document.body.appendChild(invalidVideo);
            
            updateResult('errorTestResult', 'pass', 'Invalid video test completed - check console for handling');
        }

        function testDisabledPiP() {
            const video = document.getElementById('testVideo1');
            if (video) {
                video.disablePictureInPicture = true;
                updateResult('errorTestResult', 'pass', 'Disabled PiP test - video should be rejected');
                
                setTimeout(() => {
                    video.disablePictureInPicture = false;
                }, 3000);
            }
        }

        function testNetworkError() {
            // Create video with invalid source
            const errorVideo = document.createElement('video');
            errorVideo.src = 'https://invalid-url-for-testing.com/video.mp4';
            errorVideo.controls = true;
            document.body.appendChild(errorVideo);
            
            updateResult('errorTestResult', 'pass', 'Network error test initiated');
        }

        function runDiagnostics() {
            const output = document.getElementById('diagnosticOutput');
            
            const diagnostics = {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                pipSupported: 'pictureInPictureEnabled' in document,
                pipEnabled: document.pictureInPictureEnabled,
                videosFound: document.querySelectorAll('video').length,
                overlaysFound: document.querySelectorAll('.pip-master-overlay').length,
                extensionLoaded: typeof window.pipMasterContentLoaded !== 'undefined',
                extensionDiagnostics: window.pipMasterDiagnostics || null
            };
            
            output.textContent = JSON.stringify(diagnostics, null, 2);
        }

        function exportTestResults() {
            const results = {
                timestamp: new Date().toISOString(),
                testResults: testResults,
                diagnostics: JSON.parse(document.getElementById('diagnosticOutput').textContent)
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `pip-master-test-results-${Date.now()}.json`;
            a.click();
        }

        function updateResult(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `test-result result-${status}`;
            element.textContent = message;
            
            testResults[elementId] = {
                status: status,
                message: message,
                timestamp: new Date().toISOString()
            };
        }

        // Listen for PiP events
        document.addEventListener('enterpictureinpicture', (e) => {
            console.log('✅ Picture-in-Picture activated:', e.target);
        });

        document.addEventListener('leavepictureinpicture', (e) => {
            console.log('📺 Picture-in-Picture deactivated:', e.target);
        });
    </script>
</body>
</html>
