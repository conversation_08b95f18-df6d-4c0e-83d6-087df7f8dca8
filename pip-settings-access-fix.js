// PiP Settings Access Fix - Diagnose version and fix Alt+S in PiP mode
// Comprehensive fix for settings panel accessibility during Picture-in-Picture

console.log("🔧 PiP Settings Access Diagnostic & Fix");
console.log("=======================================");

// PiP Settings Access Manager
class PiPSettingsAccessFixer {
  constructor() {
    this.currentVersion = null;
    this.upgradeStatus = {
      version: false,
      enhancedFeatures: false,
      settingsPanel: false,
      keyboardHandlers: false,
      pipCompatibility: false
    };
    this.runComprehensiveDiagnostic();
  }

  runComprehensiveDiagnostic() {
    console.log("🚀 Running comprehensive settings access diagnostic...");
    
    this.verifyVersion();
    this.checkEnhancedFeatures();
    this.diagnoseSettingsPanel();
    this.testKeyboardShortcuts();
    this.checkPiPCompatibility();
    this.generateDiagnosticReport();
    this.applyFixes();
  }

  verifyVersion() {
    console.log("\n1️⃣ Version Verification");
    console.log("=======================");
    
    // Check version
    this.currentVersion = window.pipMasterInstance?.version || 'Unknown';
    console.log(`Current version: ${this.currentVersion}`);
    
    // Check if v2.0.0 features exist
    const v2Features = {
      smartAutoPiP: !!window.pipMasterInstance?.smartAutoPiP,
      timelineControl: !!window.pipMasterInstance?.timelineControl,
      themeManager: !!window.pipMasterInstance?.themeManager,
      performanceOptimizer: !!window.pipMasterInstance?.performanceOptimizer,
      accessibility: !!window.pipMasterInstance?.accessibility
    };
    
    console.log("v2.0.0 Features check:");
    Object.entries(v2Features).forEach(([feature, exists]) => {
      console.log(`${exists ? '✅' : '❌'} ${feature}: ${exists ? 'Present' : 'Missing'}`);
    });
    
    const v2FeatureCount = Object.values(v2Features).filter(Boolean).length;
    const isV2 = this.currentVersion === "2.0.0" && v2FeatureCount >= 4;
    
    this.upgradeStatus.version = isV2;
    
    if (isV2) {
      console.log("🎉 Extension is running v2.0.0 Enhanced");
    } else {
      console.log("⚠️ Extension appears to be v1.0.0 or incomplete v2.0.0");
      console.log(`Features present: ${v2FeatureCount}/5`);
    }
  }

  checkEnhancedFeatures() {
    console.log("\n2️⃣ Enhanced Features Check");
    console.log("==========================");
    
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    
    if (!settingsPanel) {
      console.log("❌ Settings panel not found");
      this.upgradeStatus.enhancedFeatures = false;
      return;
    }
    
    // Check for enhanced settings
    const enhancedSettings = {
      audioControlEnabled: 'audioControlEnabled' in settingsPanel.settings,
      timelinePreviewEnabled: 'timelinePreviewEnabled' in settingsPanel.settings,
      autoEnable: 'autoEnable' in settingsPanel.settings,
      overlayTheme: 'overlayTheme' in settingsPanel.settings,
      lowPowerMode: 'lowPowerMode' in settingsPanel.settings
    };
    
    console.log("Enhanced settings check:");
    Object.entries(enhancedSettings).forEach(([setting, exists]) => {
      console.log(`${exists ? '✅' : '❌'} ${setting}: ${exists ? 'Present' : 'Missing'}`);
    });
    
    // Check for enhanced methods
    const enhancedMethods = {
      applyAudioControlSettings: typeof settingsPanel.applyAudioControlSettings === 'function',
      applyTimelinePreviewSettings: typeof settingsPanel.applyTimelinePreviewSettings === 'function',
      generateEnhancedPanelHTML: typeof settingsPanel.generateEnhancedPanelHTML === 'function'
    };
    
    console.log("Enhanced methods check:");
    Object.entries(enhancedMethods).forEach(([method, exists]) => {
      console.log(`${exists ? '✅' : '❌'} ${method}: ${exists ? 'Available' : 'Missing'}`);
    });
    
    const enhancedCount = Object.values({...enhancedSettings, ...enhancedMethods}).filter(Boolean).length;
    this.upgradeStatus.enhancedFeatures = enhancedCount >= 6;
    
    console.log(`Enhanced features score: ${enhancedCount}/8`);
  }

  diagnoseSettingsPanel() {
    console.log("\n3️⃣ Settings Panel Diagnosis");
    console.log("============================");
    
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    
    if (!settingsPanel) {
      console.log("❌ Settings panel not available");
      this.upgradeStatus.settingsPanel = false;
      return;
    }
    
    // Test panel creation
    try {
      settingsPanel.showPanel();
      const panel = document.getElementById('pip-master-settings-panel');
      
      if (panel) {
        console.log("✅ Settings panel can be created");
        
        // Check for enhanced sections
        const panelHTML = panel.innerHTML;
        const enhancedSections = {
          smartAutoPiP: panelHTML.includes('🔄 Smart Auto-PiP'),
          themes: panelHTML.includes('🎨 Overlay Themes'),
          audioControl: panelHTML.includes('🔊 Enable audio in Picture-in-Picture mode'),
          timelineControl: panelHTML.includes('⏯️ Timeline Controls'),
          accessibility: panelHTML.includes('♿ Accessibility')
        };
        
        console.log("Enhanced sections in panel:");
        Object.entries(enhancedSections).forEach(([section, exists]) => {
          console.log(`${exists ? '✅' : '❌'} ${section}: ${exists ? 'Present' : 'Missing'}`);
        });
        
        const sectionCount = Object.values(enhancedSections).filter(Boolean).length;
        this.upgradeStatus.settingsPanel = sectionCount >= 4;
        
        console.log(`Enhanced sections score: ${sectionCount}/5`);
        
        settingsPanel.hidePanel();
      } else {
        console.log("❌ Settings panel creation failed");
        this.upgradeStatus.settingsPanel = false;
      }
    } catch (error) {
      console.log("❌ Settings panel test failed:", error.message);
      this.upgradeStatus.settingsPanel = false;
    }
  }

  testKeyboardShortcuts() {
    console.log("\n4️⃣ Keyboard Shortcuts Test");
    console.log("===========================");
    
    // Test Alt+S in different contexts
    const contexts = {
      mainWindow: this.testAltSInContext('main'),
      pipMode: this.testAltSInContext('pip'),
      globalListener: this.checkGlobalKeyboardListener()
    };
    
    console.log("Keyboard shortcut contexts:");
    Object.entries(contexts).forEach(([context, working]) => {
      console.log(`${working ? '✅' : '❌'} ${context}: ${working ? 'Working' : 'Not working'}`);
    });
    
    this.upgradeStatus.keyboardHandlers = contexts.mainWindow && contexts.globalListener;
    
    if (!contexts.pipMode) {
      console.log("⚠️ Alt+S not working in PiP mode - this is the main issue");
    }
  }

  testAltSInContext(context) {
    try {
      if (context === 'pip' && !document.pictureInPictureElement) {
        console.log("💡 PiP not active - cannot test PiP context");
        return false;
      }
      
      // Check if keyboard listeners are properly set up
      const hasAltSListener = this.checkForAltSListener();
      return hasAltSListener;
    } catch (error) {
      return false;
    }
  }

  checkForAltSListener() {
    // Check if Alt+S listener exists by looking for event listeners
    // This is a simplified check
    return typeof window.pipMasterInstance?.settingsPanel?.showPanel === 'function';
  }

  checkGlobalKeyboardListener() {
    // Check if global keyboard listeners are properly set up
    return document.querySelector('[data-pip-keyboard-listener]') !== null;
  }

  checkPiPCompatibility() {
    console.log("\n5️⃣ PiP Compatibility Check");
    console.log("===========================");
    
    const pipActive = !!document.pictureInPictureElement;
    console.log(`PiP currently active: ${pipActive ? '✅' : '❌'}`);
    
    if (pipActive) {
      // Test if settings can be accessed during PiP
      try {
        const settingsPanel = window.pipMasterInstance?.settingsPanel;
        if (settingsPanel) {
          // Test opening settings during PiP
          settingsPanel.showPanel();
          const panelVisible = !!document.getElementById('pip-master-settings-panel');
          console.log(`Settings accessible during PiP: ${panelVisible ? '✅' : '❌'}`);
          
          if (panelVisible) {
            settingsPanel.hidePanel();
          }
          
          this.upgradeStatus.pipCompatibility = panelVisible;
        }
      } catch (error) {
        console.log("❌ PiP compatibility test failed:", error.message);
        this.upgradeStatus.pipCompatibility = false;
      }
    } else {
      console.log("💡 Activate PiP mode to test compatibility");
      this.upgradeStatus.pipCompatibility = true; // Assume compatible if not in PiP
    }
  }

  generateDiagnosticReport() {
    console.log("\n📊 DIAGNOSTIC REPORT");
    console.log("====================");
    
    const { version, enhancedFeatures, settingsPanel, keyboardHandlers, pipCompatibility } = this.upgradeStatus;
    
    console.log("Status Summary:");
    console.log(`📦 Version Status: ${version ? '✅ v2.0.0' : '❌ v1.0.0 or incomplete'}`);
    console.log(`⚡ Enhanced Features: ${enhancedFeatures ? '✅ Present' : '❌ Missing'}`);
    console.log(`⚙️ Settings Panel: ${settingsPanel ? '✅ Enhanced' : '❌ Basic/Missing'}`);
    console.log(`⌨️ Keyboard Handlers: ${keyboardHandlers ? '✅ Working' : '❌ Not working'}`);
    console.log(`📺 PiP Compatibility: ${pipCompatibility ? '✅ Compatible' : '❌ Issues'}`);
    
    const overallScore = Object.values(this.upgradeStatus).filter(Boolean).length;
    console.log(`\n🏥 Overall Status: ${overallScore}/5 (${overallScore * 20}%)`);
    
    // Determine primary issue
    if (!version || !enhancedFeatures) {
      console.log("\n🔧 PRIMARY ISSUE: v2.0.0 upgrade incomplete");
      console.log("SOLUTION: Re-run upgrade phases");
    } else if (!keyboardHandlers || !pipCompatibility) {
      console.log("\n🔧 PRIMARY ISSUE: PiP keyboard event handling");
      console.log("SOLUTION: Implement cross-context event handling");
    } else {
      console.log("\n🎉 All systems operational!");
    }
  }

  applyFixes() {
    console.log("\n🔧 APPLYING FIXES");
    console.log("=================");
    
    const { version, enhancedFeatures, keyboardHandlers, pipCompatibility } = this.upgradeStatus;
    
    if (!version || !enhancedFeatures) {
      this.triggerUpgradeProcess();
    }
    
    if (!keyboardHandlers || !pipCompatibility) {
      this.setupCrossContextKeyboardHandling();
    }
    
    this.setupPiPSettingsAccess();
    this.addAlternativeAccessMethods();
  }

  triggerUpgradeProcess() {
    console.log("🔄 Triggering v2.0.0 upgrade process...");
    
    console.log("⚠️ UPGRADE NEEDED: Extension is not fully v2.0.0");
    console.log("\n📋 To complete upgrade, run these commands in order:");
    console.log("1. Copy and paste phase1-core-infrastructure.js");
    console.log("2. Copy and paste phase2-feature-integration.js");
    console.log("3. Copy and paste phase3-advanced-features.js");
    console.log("4. Run: verifyCompleteUpgrade()");
    
    // Try to auto-trigger if upgrade scripts are available
    if (typeof window.Phase1InfrastructureUpgrade === 'function') {
      console.log("🚀 Auto-triggering Phase 1...");
      new window.Phase1InfrastructureUpgrade();
    }
  }

  setupCrossContextKeyboardHandling() {
    console.log("⌨️ Setting up cross-context keyboard handling...");
    
    // Remove existing listeners
    this.removeExistingKeyboardListeners();
    
    // Create enhanced global keyboard handler
    const globalKeyboardHandler = (event) => {
      // Handle Alt+S for settings
      if (event.altKey && event.key.toLowerCase() === 's') {
        event.preventDefault();
        event.stopPropagation();
        
        this.openSettingsPanel();
        console.log("⚙️ Settings panel opened via global handler");
      }
      
      // Handle Alt+P for PiP toggle
      if (event.altKey && event.key.toLowerCase() === 'p') {
        event.preventDefault();
        event.stopPropagation();
        
        this.togglePiP();
        console.log("📺 PiP toggled via global handler");
      }
      
      // Handle Alt+H for help
      if (event.altKey && event.key.toLowerCase() === 'h') {
        event.preventDefault();
        event.stopPropagation();
        
        this.showHelp();
        console.log("📖 Help shown via global handler");
      }
    };
    
    // Add global listeners with high priority
    document.addEventListener('keydown', globalKeyboardHandler, true);
    window.addEventListener('keydown', globalKeyboardHandler, true);
    
    // Mark as active
    if (!document.querySelector('[data-pip-keyboard-listener]')) {
      const marker = document.createElement('div');
      marker.setAttribute('data-pip-keyboard-listener', 'true');
      marker.style.display = 'none';
      document.body.appendChild(marker);
    }
    
    console.log("✅ Cross-context keyboard handling activated");
  }

  setupPiPSettingsAccess() {
    console.log("📺 Setting up PiP-specific settings access...");
    
    // Enhanced settings panel methods for PiP compatibility
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    
    if (settingsPanel) {
      // Override showPanel to work in PiP context
      const originalShowPanel = settingsPanel.showPanel;
      
      settingsPanel.showPanel = function() {
        try {
          // Call original method
          if (originalShowPanel) {
            originalShowPanel.call(this);
          } else {
            this.createSettingsPanel();
            this.panel.style.display = 'block';
          }
          
          this.isVisible = true;
          
          // Ensure panel is visible even in PiP mode
          const panel = document.getElementById('pip-master-settings-panel');
          if (panel) {
            panel.style.zIndex = '10005';
            panel.style.position = 'fixed';
            console.log("✅ Settings panel optimized for PiP mode");
          }
        } catch (error) {
          console.error("Settings panel show failed:", error);
        }
      };
      
      console.log("✅ PiP settings access configured");
    }
  }

  addAlternativeAccessMethods() {
    console.log("🔘 Adding alternative access methods...");
    
    // Create floating settings button for PiP mode
    this.createFloatingSettingsButton();
    
    // Add context menu option (if possible)
    this.setupContextMenuAccess();
    
    console.log("✅ Alternative access methods added");
  }

  createFloatingSettingsButton() {
    // Remove existing button
    const existing = document.getElementById('pip-floating-settings-btn');
    if (existing) existing.remove();
    
    // Create floating settings button
    const button = document.createElement('div');
    button.id = 'pip-floating-settings-btn';
    button.style.cssText = `
      position: fixed; top: 20px; left: 20px; z-index: 10004;
      background: rgba(0, 0, 0, 0.8); color: white; padding: 8px 12px;
      border-radius: 6px; cursor: pointer; font-family: Arial, sans-serif;
      font-size: 14px; backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      display: none; transition: opacity 0.3s ease;
    `;
    
    button.innerHTML = '⚙️ Settings';
    
    // Show only when PiP is active
    const updateVisibility = () => {
      const pipActive = !!document.pictureInPictureElement;
      button.style.display = pipActive ? 'block' : 'none';
    };
    
    // Add click handler
    button.addEventListener('click', () => {
      this.openSettingsPanel();
    });
    
    // Monitor PiP status
    document.addEventListener('enterpictureinpicture', updateVisibility);
    document.addEventListener('leavepictureinpicture', updateVisibility);
    
    document.body.appendChild(button);
    updateVisibility();
    
    console.log("✅ Floating settings button created");
  }

  setupContextMenuAccess() {
    // Context menu access is limited by browser security
    console.log("💡 Context menu access limited by browser security");
  }

  openSettingsPanel() {
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    if (settingsPanel) {
      if (settingsPanel.isVisible) {
        settingsPanel.hidePanel();
      } else {
        settingsPanel.showPanel();
      }
    } else {
      console.log("❌ Settings panel not available");
    }
  }

  togglePiP() {
    const video = document.querySelector('video');
    if (video) {
      if (document.pictureInPictureElement) {
        document.exitPictureInPicture();
      } else {
        video.requestPictureInPicture().catch(console.error);
      }
    }
  }

  showHelp() {
    console.log("📖 PiP Master v2.0.0 Help:");
    console.log("Alt+P: Toggle Picture-in-Picture");
    console.log("Alt+S: Open/close settings panel");
    console.log("Alt+T: Toggle timeline (in PiP mode)");
    console.log("Ctrl+↑/↓: Volume control (in PiP mode)");
    console.log("Alt+H: Show this help");
  }

  removeExistingKeyboardListeners() {
    // Remove existing keyboard listener markers
    const markers = document.querySelectorAll('[data-pip-keyboard-listener]');
    markers.forEach(marker => marker.remove());
  }
}

// Quick test commands
window.testAltSShortcut = function() {
  console.log("🧪 Testing Alt+S Shortcut");
  console.log("=========================");
  
  const pipActive = !!document.pictureInPictureElement;
  console.log(`PiP mode: ${pipActive ? '✅ Active' : '❌ Inactive'}`);
  
  console.log("🎮 Test: Press Alt+S to open settings panel");
  console.log("Expected: Settings panel should open regardless of PiP status");
  
  // Test programmatically
  try {
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    if (settingsPanel) {
      settingsPanel.showPanel();
      console.log("✅ Settings panel opened programmatically");
      setTimeout(() => settingsPanel.hidePanel(), 2000);
    } else {
      console.log("❌ Settings panel not available");
    }
  } catch (error) {
    console.log("❌ Settings panel test failed:", error.message);
  }
};

window.checkUpgradeStatus = function() {
  console.log("🔍 Quick Upgrade Status Check");
  console.log("=============================");
  
  const version = window.pipMasterInstance?.version || 'Unknown';
  const hasEnhanced = !!(window.pipMasterInstance?.smartAutoPiP && 
                        window.pipMasterInstance?.timelineControl &&
                        window.pipMasterInstance?.themeManager);
  
  console.log(`Version: ${version}`);
  console.log(`Enhanced features: ${hasEnhanced ? '✅ Present' : '❌ Missing'}`);
  
  if (version !== "2.0.0" || !hasEnhanced) {
    console.log("⚠️ Upgrade incomplete - run upgrade phases");
  } else {
    console.log("✅ v2.0.0 upgrade complete");
  }
  
  return { version, hasEnhanced };
};

window.fixPiPSettingsAccess = function() {
  console.log("🔧 Quick PiP Settings Access Fix");
  console.log("================================");
  
  new PiPSettingsAccessFixer();
  
  console.log("✅ PiP settings access fix applied");
  console.log("🎮 Test with: testAltSShortcut()");
};

// Auto-run diagnostic
console.log("🚀 Auto-running PiP settings access diagnostic...");
window.pipSettingsAccessFixer = new PiPSettingsAccessFixer();

console.log("\n📋 PiP Settings Commands:");
console.log("=========================");
console.log("testAltSShortcut()        - Test Alt+S in current context");
console.log("checkUpgradeStatus()      - Quick version and feature check");
console.log("fixPiPSettingsAccess()    - Apply comprehensive fix");
