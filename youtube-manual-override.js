// YouTube Manual Override Commands
// Emergency repair commands for immediate YouTube fixes

console.log("🚨 YouTube Manual Override Commands");
console.log("==================================");

// Command 1: Complete YouTube Reset and Setup
window.youtubeEmergencyFix = function() {
  console.log("🔧 EMERGENCY FIX: Complete YouTube Reset and Setup");
  console.log("==================================================");
  
  if (!window.pipMasterInstance) {
    console.error("❌ Extension instance not available!");
    return false;
  }
  
  try {
    // Step 1: Force platform
    console.log("1. Forcing platform to YouTube...");
    window.pipMasterInstance.platform = 'youtube';
    
    // Step 2: Clear existing state
    console.log("2. Clearing existing state...");
    window.pipMasterInstance.videos.clear();
    window.pipMasterInstance.overlays.forEach(overlay => {
      if (overlay.parentElement) {
        overlay.parentElement.removeChild(overlay);
      }
    });
    window.pipMasterInstance.overlays.clear();
    
    // Step 3: Force YouTube setup
    console.log("3. Setting up YouTube detection...");
    if (window.pipMasterInstance.setupYouTubeDetection) {
      window.pipMasterInstance.setupYouTubeDetection();
    }
    
    // Step 4: Force video scan
    console.log("4. Scanning for videos...");
    window.pipMasterInstance.performUniversalVideoScan();
    
    // Step 5: Manual video processing
    console.log("5. Manual video processing...");
    const videos = document.querySelectorAll('#movie_player video, .html5-video-player video, video');
    let processed = 0;
    
    videos.forEach((video, index) => {
      try {
        if (!window.pipMasterInstance.videos.has(video)) {
          window.pipMasterInstance.videos.add(video);
          window.pipMasterInstance.createOverlay(video);
          processed++;
          console.log(`   ✅ Processed video ${index + 1}`);
        }
      } catch (error) {
        console.warn(`   ⚠️ Failed to process video ${index + 1}:`, error.message);
      }
    });
    
    console.log(`✅ Emergency fix complete! Processed ${processed} videos.`);
    console.log("👀 Look for overlay buttons on videos");
    return true;
    
  } catch (error) {
    console.error("❌ Emergency fix failed:", error);
    return false;
  }
};

// Command 2: Force Video Detection Only
window.youtubeForceDetection = function() {
  console.log("🔍 FORCE DETECTION: YouTube Video Scanning");
  console.log("==========================================");
  
  const selectors = [
    "#movie_player video",
    ".html5-video-player video",
    ".video-stream",
    "video.video-stream",
    "video"
  ];
  
  let foundVideos = [];
  
  selectors.forEach(selector => {
    try {
      const videos = document.querySelectorAll(selector);
      console.log(`${selector}: ${videos.length} videos`);
      videos.forEach(video => {
        if (!foundVideos.includes(video)) {
          foundVideos.push(video);
        }
      });
    } catch (error) {
      console.warn(`Selector ${selector} failed:`, error.message);
    }
  });
  
  console.log(`Total unique videos found: ${foundVideos.length}`);
  
  if (foundVideos.length > 0 && window.pipMasterInstance) {
    foundVideos.forEach((video, index) => {
      console.log(`Video ${index + 1}:`, {
        src: video.src || video.currentSrc || "no src",
        readyState: video.readyState,
        dimensions: `${video.videoWidth}x${video.videoHeight}`,
        paused: video.paused
      });
    });
  }
  
  return foundVideos;
};

// Command 3: Force Overlay Creation
window.youtubeForceOverlays = function() {
  console.log("🎨 FORCE OVERLAYS: Creating YouTube Overlays");
  console.log("============================================");
  
  const videos = document.querySelectorAll('video');
  let created = 0;
  
  videos.forEach((video, index) => {
    try {
      if (window.pipMasterInstance && !window.pipMasterInstance.overlays.has(video)) {
        // Add to tracking first
        window.pipMasterInstance.videos.add(video);
        
        // Create overlay
        window.pipMasterInstance.createOverlay(video);
        created++;
        console.log(`✅ Created overlay for video ${index + 1}`);
      }
    } catch (error) {
      console.warn(`⚠️ Failed to create overlay for video ${index + 1}:`, error.message);
    }
  });
  
  console.log(`✅ Created ${created} overlays`);
  return created;
};

// Command 4: Test PiP Functionality
window.youtubeTestPiP = function(videoIndex = 0) {
  console.log(`🧪 TEST PIP: Testing Picture-in-Picture on video ${videoIndex + 1}`);
  console.log("=============================================================");
  
  const videos = document.querySelectorAll('video');
  
  if (videos.length === 0) {
    console.error("❌ No videos found for testing");
    return false;
  }
  
  if (videoIndex >= videos.length) {
    console.error(`❌ Video index ${videoIndex} not found (only ${videos.length} videos available)`);
    return false;
  }
  
  const video = videos[videoIndex];
  console.log("Testing video:", {
    src: video.src || video.currentSrc || "no src",
    readyState: video.readyState,
    dimensions: `${video.videoWidth}x${video.videoHeight}`,
    paused: video.paused,
    pipDisabled: video.disablePictureInPicture
  });
  
  video.requestPictureInPicture()
    .then(() => {
      console.log("✅ PiP test successful!");
      setTimeout(() => {
        if (document.pictureInPictureElement) {
          document.exitPictureInPicture();
          console.log("✅ PiP exited");
        }
      }, 3000);
    })
    .catch(error => {
      console.error("❌ PiP test failed:", error.message);
      
      if (error.message.includes('policy')) {
        console.log("💡 YouTube policy restriction - try different video");
      } else if (error.message.includes('disabled')) {
        console.log("💡 PiP disabled on this video");
      } else if (error.message.includes('state')) {
        console.log("💡 Video not ready - wait for it to load");
      }
    });
  
  return true;
};

// Command 5: Complete Diagnostic
window.youtubeFullDiagnostic = function() {
  console.log("🔬 FULL DIAGNOSTIC: Complete YouTube Analysis");
  console.log("=============================================");
  
  const diagnostic = {
    environment: {
      isYouTube: window.location.hostname.includes('youtube.com'),
      isVideoPage: window.location.pathname.includes('/watch'),
      url: window.location.href
    },
    extension: {
      loaded: !!window.pipMasterContentLoaded,
      instance: !!window.pipMasterInstance,
      platform: window.pipMasterInstance?.platform
    },
    videos: {
      found: document.querySelectorAll('video').length,
      tracked: window.pipMasterInstance?.videos.size || 0,
      overlays: document.querySelectorAll('.pip-master-overlay').length
    },
    youtube: {
      moviePlayer: !!document.querySelector('#movie_player'),
      htmlPlayer: !!document.querySelector('.html5-video-player'),
      ytdPlayer: !!document.querySelector('ytd-player')
    }
  };
  
  console.log("Diagnostic Results:", diagnostic);
  
  // Recommendations
  console.log("\n📋 RECOMMENDATIONS:");
  if (!diagnostic.environment.isYouTube) {
    console.log("❌ Not on YouTube - navigate to youtube.com");
  } else if (!diagnostic.extension.loaded) {
    console.log("❌ Extension not loaded - check chrome://extensions/");
  } else if (diagnostic.extension.platform !== 'youtube') {
    console.log("⚠️ Platform not detected as YouTube - run youtubeEmergencyFix()");
  } else if (diagnostic.videos.found === 0) {
    console.log("❌ No videos found - make sure you're on a video page");
  } else if (diagnostic.videos.tracked === 0) {
    console.log("⚠️ Videos found but not tracked - run youtubeForceDetection()");
  } else if (diagnostic.videos.overlays === 0) {
    console.log("⚠️ Videos tracked but no overlays - run youtubeForceOverlays()");
  } else {
    console.log("✅ Everything looks good - try youtubeTestPiP()");
  }
  
  return diagnostic;
};

// Command 6: Netflix Support Check
window.netflixCheck = function() {
  console.log("🎬 NETFLIX CHECK: Verifying Netflix Support");
  console.log("===========================================");
  
  const isNetflix = window.location.hostname.includes('netflix.com');
  
  if (!isNetflix) {
    console.log("ℹ️ Not on Netflix - navigate to netflix.com to test");
    return false;
  }
  
  console.log("✅ On Netflix, checking video detection...");
  
  const netflixSelectors = [
    "video",
    ".VideoContainer video",
    ".watch-video video",
    ".nfp video"
  ];
  
  let foundVideos = [];
  
  netflixSelectors.forEach(selector => {
    try {
      const videos = document.querySelectorAll(selector);
      console.log(`${selector}: ${videos.length} videos`);
      videos.forEach(video => {
        if (!foundVideos.includes(video)) {
          foundVideos.push(video);
        }
      });
    } catch (error) {
      console.warn(`Selector ${selector} failed:`, error.message);
    }
  });
  
  console.log(`Total Netflix videos found: ${foundVideos.length}`);
  
  if (foundVideos.length > 0) {
    foundVideos.forEach((video, index) => {
      console.log(`Netflix Video ${index + 1}:`, {
        src: video.src || video.currentSrc || "no src",
        readyState: video.readyState,
        dimensions: `${video.videoWidth}x${video.videoHeight}`,
        blob: (video.src || video.currentSrc || "").startsWith('blob:')
      });
    });
    
    // Test Netflix PiP
    const testVideo = foundVideos[0];
    testVideo.requestPictureInPicture()
      .then(() => {
        console.log("✅ Netflix PiP test successful!");
        setTimeout(() => {
          if (document.pictureInPictureElement) {
            document.exitPictureInPicture();
          }
        }, 3000);
      })
      .catch(error => {
        console.warn("⚠️ Netflix PiP failed (expected for DRM content):", error.message);
      });
  }
  
  return foundVideos.length;
};

// Display available commands
console.log("\n📋 AVAILABLE COMMANDS:");
console.log("======================");
console.log("youtubeEmergencyFix()     - Complete reset and setup");
console.log("youtubeForceDetection()   - Force video detection");
console.log("youtubeForceOverlays()    - Force overlay creation");
console.log("youtubeTestPiP(index)     - Test PiP on video (default: 0)");
console.log("youtubeFullDiagnostic()   - Complete diagnostic");
console.log("netflixCheck()            - Test Netflix support");
console.log("");
console.log("🚀 QUICK START: Run youtubeEmergencyFix() first!");
console.log("📊 For analysis: Run youtubeFullDiagnostic()");
console.log("🧪 To test: Run youtubeTestPiP()");
