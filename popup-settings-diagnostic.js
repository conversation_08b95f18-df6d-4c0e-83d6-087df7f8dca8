// Quick Settings Diagnostic for Extension Popup
// Add this script to popup.html or run in popup console

console.log("🔧 PiP Master Popup Settings Diagnostic");
console.log("======================================");

class PopupSettingsDiagnostic {
  constructor() {
    this.runDiagnostic();
  }

  async runDiagnostic() {
    console.log("🔍 Running popup settings diagnostic...");
    
    const results = {
      storageAccess: await this.testStorageAccess(),
      settingsLoad: await this.testSettingsLoad(),
      settingsSave: await this.testSettingsSave(),
      communication: await this.testCommunication(),
      uiElements: this.testUIElements()
    };

    this.displayResults(results);
    return results;
  }

  async testStorageAccess() {
    console.log("📦 Testing storage access...");
    
    const results = {
      sync: false,
      local: false,
      error: null
    };

    try {
      // Test sync storage
      if (chrome.storage && chrome.storage.sync) {
        await chrome.storage.sync.set({ popupTest: 'test' });
        const syncResult = await chrome.storage.sync.get('popupTest');
        results.sync = syncResult.popupTest === 'test';
        await chrome.storage.sync.remove('popupTest');
      }

      // Test local storage
      if (chrome.storage && chrome.storage.local) {
        await chrome.storage.local.set({ popupTest: 'test' });
        const localResult = await chrome.storage.local.get('popupTest');
        results.local = localResult.popupTest === 'test';
        await chrome.storage.local.remove('popupTest');
      }
    } catch (error) {
      results.error = error.message;
      console.error("Storage test failed:", error);
    }

    return results;
  }

  async testSettingsLoad() {
    console.log("📥 Testing settings load...");
    
    const results = {
      success: false,
      settingsCount: 0,
      hasDefaults: false,
      error: null
    };

    try {
      // Try to get settings via background script
      const response = await chrome.runtime.sendMessage({ type: 'GET_SETTINGS' });
      
      if (response && response.success && response.data) {
        results.success = true;
        results.settingsCount = Object.keys(response.data).length;
        results.hasDefaults = 'enabled' in response.data && 'showOverlay' in response.data;
      } else {
        results.error = response?.error || 'No settings data received';
      }
    } catch (error) {
      results.error = error.message;
      console.error("Settings load test failed:", error);
    }

    return results;
  }

  async testSettingsSave() {
    console.log("💾 Testing settings save...");
    
    const results = {
      success: false,
      verified: false,
      error: null
    };

    try {
      // Get current settings
      const currentResponse = await chrome.runtime.sendMessage({ type: 'GET_SETTINGS' });
      if (!currentResponse.success) {
        throw new Error('Could not get current settings');
      }

      const originalOpacity = currentResponse.data.opacity || 0.9;
      const testOpacity = originalOpacity === 0.8 ? 0.7 : 0.8;

      // Update a setting
      const updateResponse = await chrome.runtime.sendMessage({
        type: 'UPDATE_SETTINGS',
        data: { opacity: testOpacity }
      });

      if (updateResponse.success) {
        results.success = true;

        // Verify the change
        const verifyResponse = await chrome.runtime.sendMessage({ type: 'GET_SETTINGS' });
        if (verifyResponse.success && verifyResponse.data.opacity === testOpacity) {
          results.verified = true;
        }

        // Restore original value
        await chrome.runtime.sendMessage({
          type: 'UPDATE_SETTINGS',
          data: { opacity: originalOpacity }
        });
      } else {
        results.error = updateResponse.error || 'Update failed';
      }
    } catch (error) {
      results.error = error.message;
      console.error("Settings save test failed:", error);
    }

    return results;
  }

  async testCommunication() {
    console.log("📡 Testing communication...");
    
    const results = {
      background: false,
      contentScript: false,
      error: null
    };

    try {
      // Test background script communication
      const bgResponse = await chrome.runtime.sendMessage({ type: 'GET_SETTINGS' });
      results.background = !!bgResponse;

      // Test content script communication (if tabs available)
      if (chrome.tabs) {
        try {
          const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
          if (tab) {
            const csResponse = await chrome.tabs.sendMessage(tab.id, { type: 'GET_VIDEO_COUNT' });
            results.contentScript = !!csResponse;
          }
        } catch (csError) {
          // Content script might not be loaded, this is expected
          console.debug("Content script not available (expected for non-video pages)");
        }
      }
    } catch (error) {
      results.error = error.message;
      console.error("Communication test failed:", error);
    }

    return results;
  }

  testUIElements() {
    console.log("🎨 Testing UI elements...");
    
    const results = {
      enabledToggle: false,
      overlayToggle: false,
      opacitySlider: false,
      positionSelect: false,
      error: null
    };

    try {
      // Check for common settings UI elements
      results.enabledToggle = !!document.querySelector('#enabled, [data-setting="enabled"], .enabled-toggle');
      results.overlayToggle = !!document.querySelector('#showOverlay, [data-setting="showOverlay"], .overlay-toggle');
      results.opacitySlider = !!document.querySelector('#opacity, [data-setting="opacity"], .opacity-slider');
      results.positionSelect = !!document.querySelector('#overlayPosition, [data-setting="overlayPosition"], .position-select');
    } catch (error) {
      results.error = error.message;
      console.error("UI elements test failed:", error);
    }

    return results;
  }

  displayResults(results) {
    console.log("\n📊 Diagnostic Results");
    console.log("====================");

    // Storage Access
    console.log("📦 Storage Access:");
    console.log(`  Sync Storage: ${results.storageAccess.sync ? '✅' : '❌'}`);
    console.log(`  Local Storage: ${results.storageAccess.local ? '✅' : '❌'}`);
    if (results.storageAccess.error) {
      console.log(`  Error: ${results.storageAccess.error}`);
    }

    // Settings Load
    console.log("\n📥 Settings Load:");
    console.log(`  Success: ${results.settingsLoad.success ? '✅' : '❌'}`);
    console.log(`  Settings Count: ${results.settingsLoad.settingsCount}`);
    console.log(`  Has Defaults: ${results.settingsLoad.hasDefaults ? '✅' : '❌'}`);
    if (results.settingsLoad.error) {
      console.log(`  Error: ${results.settingsLoad.error}`);
    }

    // Settings Save
    console.log("\n💾 Settings Save:");
    console.log(`  Save Success: ${results.settingsSave.success ? '✅' : '❌'}`);
    console.log(`  Save Verified: ${results.settingsSave.verified ? '✅' : '❌'}`);
    if (results.settingsSave.error) {
      console.log(`  Error: ${results.settingsSave.error}`);
    }

    // Communication
    console.log("\n📡 Communication:");
    console.log(`  Background Script: ${results.communication.background ? '✅' : '❌'}`);
    console.log(`  Content Script: ${results.communication.contentScript ? '✅' : '❌'}`);
    if (results.communication.error) {
      console.log(`  Error: ${results.communication.error}`);
    }

    // UI Elements
    console.log("\n🎨 UI Elements:");
    console.log(`  Enabled Toggle: ${results.uiElements.enabledToggle ? '✅' : '❌'}`);
    console.log(`  Overlay Toggle: ${results.uiElements.overlayToggle ? '✅' : '❌'}`);
    console.log(`  Opacity Slider: ${results.uiElements.opacitySlider ? '✅' : '❌'}`);
    console.log(`  Position Select: ${results.uiElements.positionSelect ? '✅' : '❌'}`);
    if (results.uiElements.error) {
      console.log(`  Error: ${results.uiElements.error}`);
    }

    // Overall Assessment
    const totalTests = 11; // Total number of individual tests
    const passedTests = [
      results.storageAccess.sync,
      results.storageAccess.local,
      results.settingsLoad.success,
      results.settingsLoad.hasDefaults,
      results.settingsSave.success,
      results.settingsSave.verified,
      results.communication.background,
      results.uiElements.enabledToggle,
      results.uiElements.overlayToggle,
      results.uiElements.opacitySlider,
      results.uiElements.positionSelect
    ].filter(Boolean).length;

    const percentage = Math.round((passedTests / totalTests) * 100);

    console.log(`\n🎯 Overall Score: ${passedTests}/${totalTests} (${percentage}%)`);

    if (percentage >= 90) {
      console.log("🎉 Excellent! Settings system is working perfectly.");
    } else if (percentage >= 70) {
      console.log("✅ Good! Settings system is mostly working.");
    } else if (percentage >= 50) {
      console.log("⚠️ Fair! Settings system has some issues.");
    } else {
      console.log("❌ Poor! Settings system needs attention.");
    }

    // Recommendations
    console.log("\n💡 Recommendations:");
    if (!results.storageAccess.sync && !results.storageAccess.local) {
      console.log("- Check extension storage permissions");
    }
    if (!results.settingsLoad.success) {
      console.log("- Verify background script is running");
    }
    if (!results.settingsSave.success) {
      console.log("- Check settings update message handling");
    }
    if (!results.communication.background) {
      console.log("- Reload extension and try again");
    }
    if (!results.uiElements.enabledToggle) {
      console.log("- Verify popup HTML has settings controls");
    }

    console.log("\n🔧 Quick Fixes:");
    console.log("- Reload extension: chrome://extensions/");
    console.log("- Clear storage: chrome.storage.sync.clear()");
    console.log("- Reset settings: chrome.runtime.sendMessage({type:'UPDATE_SETTINGS',data:{enabled:true}})");
  }
}

// Auto-run diagnostic
if (typeof chrome !== 'undefined' && chrome.runtime) {
  console.log("🚀 Running popup settings diagnostic...");
  window.popupDiagnostic = new PopupSettingsDiagnostic();
} else {
  console.log("⚠️ Chrome extension APIs not available. Run this in extension popup.");
}

// Export for manual use
window.PopupSettingsDiagnostic = PopupSettingsDiagnostic;
