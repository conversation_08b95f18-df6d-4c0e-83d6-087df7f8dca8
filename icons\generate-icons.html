<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PiP Master Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .icon-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        .icon-item h3 {
            margin: 10px 0;
            color: #333;
        }
        .download-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .download-btn:hover {
            background: #3367d6;
        }
    </style>
</head>
<body>
    <h1>PiP Master Icon Generator</h1>
    <p>Click the download buttons to save the icons as PNG files for the Chrome extension.</p>
    
    <div class="icon-container">
        <div class="icon-item">
            <svg id="icon16" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="2" y="3" width="20" height="14" rx="2" fill="#4285f4" stroke="#2563eb" stroke-width="1"/>
                <rect x="13" y="7" width="6" height="4" rx="1" fill="white" stroke="#4285f4" stroke-width="0.5"/>
                <circle cx="6" cy="10" r="1" fill="white"/>
                <rect x="8" y="9.5" width="3" height="1" rx="0.5" fill="white"/>
            </svg>
            <h3>16x16</h3>
            <button class="download-btn" onclick="downloadIcon('icon16', 16)">Download PNG</button>
        </div>
        
        <div class="icon-item">
            <svg id="icon48" width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="2" y="3" width="20" height="14" rx="2" fill="#4285f4" stroke="#2563eb" stroke-width="0.5"/>
                <rect x="13" y="7" width="6" height="4" rx="1" fill="white" stroke="#4285f4" stroke-width="0.3"/>
                <circle cx="6" cy="10" r="1.5" fill="white"/>
                <rect x="8.5" y="9" width="3" height="2" rx="1" fill="white"/>
                <path d="M4 6 L8 8 L4 10 Z" fill="white"/>
            </svg>
            <h3>48x48</h3>
            <button class="download-btn" onclick="downloadIcon('icon48', 48)">Download PNG</button>
        </div>
        
        <div class="icon-item">
            <svg id="icon128" width="128" height="128" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect x="2" y="3" width="20" height="14" rx="2" fill="url(#grad1)" stroke="#5a67d8" stroke-width="0.3"/>
                <rect x="13" y="7" width="6" height="4" rx="1" fill="white" stroke="#667eea" stroke-width="0.2"/>
                <circle cx="6" cy="10" r="1.5" fill="white"/>
                <rect x="8.5" y="9" width="3" height="2" rx="1" fill="white"/>
                <path d="M4 6 L8 8 L4 10 Z" fill="white"/>
                <circle cx="19" cy="5" r="1" fill="#ff4444"/>
            </svg>
            <h3>128x128</h3>
            <button class="download-btn" onclick="downloadIcon('icon128', 128)">Download PNG</button>
        </div>
    </div>

    <div style="margin-top: 40px; padding: 20px; background: white; border-radius: 8px;">
        <h2>Instructions</h2>
        <ol>
            <li>Click each download button to save the icons</li>
            <li>Save them as <code>icon16.png</code>, <code>icon48.png</code>, and <code>icon128.png</code></li>
            <li>Place them in the <code>icons/</code> folder of your extension</li>
            <li>The extension will automatically use these icons</li>
        </ol>
        
        <h3>Alternative: Use Online Converter</h3>
        <p>You can also copy the SVG code and use an online SVG to PNG converter:</p>
        <ul>
            <li><a href="https://cloudconvert.com/svg-to-png" target="_blank">CloudConvert</a></li>
            <li><a href="https://convertio.co/svg-png/" target="_blank">Convertio</a></li>
            <li><a href="https://www.freeconvert.com/svg-to-png" target="_blank">FreeConvert</a></li>
        </ul>
    </div>

    <script>
        function downloadIcon(iconId, size) {
            const svg = document.getElementById(iconId);
            const svgData = new XMLSerializer().serializeToString(svg);
            
            // Create canvas
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Create image from SVG
            const img = new Image();
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                
                // Download as PNG
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.download = `icon${size}.png`;
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    
                    URL.revokeObjectURL(url);
                    URL.revokeObjectURL(link.href);
                });
            };
            
            img.src = url;
        }
    </script>
</body>
</html>
