// PiP Master Content Script - Simplified Debug Version
// This is a minimal version to test basic functionality

console.log("PiP Master: Simple content script starting...");

// Mark that content script is loaded
window.pipMasterContentLoaded = true;
window.pipMasterDiagnostics = {
  version: "1.0.1-debug",
  loaded: true,
  timestamp: new Date().toISOString(),
  url: window.location.href,
  videosDetected: 0,
  pipSupported: "pictureInPictureEnabled" in document,
  pipEnabled: document.pictureInPictureEnabled,
};

class SimplePiPMaster {
  constructor() {
    console.log("PiP Master: Simple version initializing...");
    this.videos = new Set();
    this.settings = { enabled: true, showOverlay: true, overlayPosition: "top-right" };
    this.init();
  }

  async init() {
    console.log("PiP Master: Simple init starting...");
    
    // Set up basic event listeners
    this.setupEventListeners();
    
    // Start simple video detection
    this.startVideoDetection();
    
    console.log("PiP Master: Simple version initialized");
  }

  setupEventListeners() {
    console.log("PiP Master: Setting up simple event listeners...");
    
    // Listen for messages from background
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      console.log("PiP Master: Message received:", message.type);
      this.handleMessage(message, sender, sendResponse);
      return true;
    });

    // Listen for video events
    document.addEventListener("loadedmetadata", (event) => {
      if (event.target.tagName === "VIDEO") {
        console.log("PiP Master: Video found via loadedmetadata");
        this.handleVideoFound(event.target);
      }
    }, true);

    console.log("PiP Master: Event listeners set up");
  }

  startVideoDetection() {
    console.log("PiP Master: Starting simple video detection...");
    
    // Initial scan
    this.scanForVideos();
    
    // Set up mutation observer for dynamic content
    const observer = new MutationObserver(() => {
      this.scanForVideos();
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Periodic scan as backup
    setInterval(() => {
      this.scanForVideos();
    }, 3000);
  }

  scanForVideos() {
    const videos = document.querySelectorAll("video");
    console.log(`PiP Master: Found ${videos.length} video elements`);
    
    videos.forEach(video => {
      if (!this.videos.has(video)) {
        this.handleVideoFound(video);
      }
    });
  }

  handleVideoFound(video) {
    console.log("PiP Master: Processing new video:", video.src || video.currentSrc || "no src");
    
    if (this.videos.has(video)) {
      return;
    }

    // Basic suitability check
    if (!this.isVideoSuitable(video)) {
      console.log("PiP Master: Video not suitable for PiP");
      return;
    }

    this.videos.add(video);
    this.createSimpleOverlay(video);
    
    // Update diagnostics
    window.pipMasterDiagnostics.videosDetected = this.videos.size;
    
    console.log(`PiP Master: Video added. Total: ${this.videos.size}`);
  }

  isVideoSuitable(video) {
    // Very basic checks
    if (video.disablePictureInPicture) {
      return false;
    }

    // Check if video is visible
    const style = window.getComputedStyle(video);
    if (style.display === "none" || style.visibility === "hidden") {
      return false;
    }

    return true;
  }

  createSimpleOverlay(video) {
    console.log("PiP Master: Creating simple overlay for video");
    
    const overlay = document.createElement("div");
    overlay.className = "pip-master-overlay pip-master-visible";
    overlay.innerHTML = `
      <div class="pip-master-container">
        <div class="pip-master-indicator" title="Picture-in-Picture">
          📺
        </div>
      </div>
    `;

    // Position overlay
    overlay.style.position = "absolute";
    overlay.style.top = "10px";
    overlay.style.right = "10px";
    overlay.style.zIndex = "10000";
    overlay.style.cursor = "pointer";

    // Add click handler
    overlay.addEventListener("click", (e) => {
      e.stopPropagation();
      e.preventDefault();
      console.log("PiP Master: Overlay clicked, attempting PiP...");
      this.togglePiP(video);
    });

    // Insert overlay
    const container = video.parentElement;
    if (container) {
      container.style.position = "relative";
      container.appendChild(overlay);
      console.log("PiP Master: Overlay added to DOM");
    }
  }

  async togglePiP(video) {
    console.log("PiP Master: Attempting to toggle PiP...");
    
    try {
      if (document.pictureInPictureElement) {
        console.log("PiP Master: Exiting existing PiP...");
        await document.exitPictureInPicture();
      } else {
        console.log("PiP Master: Requesting PiP...");
        await video.requestPictureInPicture();
        console.log("PiP Master: PiP activated successfully!");
      }
    } catch (error) {
      console.error("PiP Master: PiP failed:", error);
      this.showError(`PiP failed: ${error.message}`);
    }
  }

  showError(message) {
    console.error("PiP Master:", message);
    
    // Create simple error notification
    const notification = document.createElement("div");
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #f44336;
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      z-index: 10001;
      font-family: Arial, sans-serif;
      font-size: 14px;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      if (notification.parentElement) {
        notification.parentElement.removeChild(notification);
      }
    }, 3000);
  }

  handleMessage(message, sender, sendResponse) {
    console.log("PiP Master: Handling message:", message.type);
    
    switch (message.type) {
      case "KEYBOARD_COMMAND":
        if (message.command === "toggle-pip") {
          console.log("PiP Master: Keyboard toggle PiP command");
          const video = this.findBestVideo();
          if (video) {
            this.togglePiP(video);
          } else {
            this.showError("No suitable video found");
          }
        }
        sendResponse({ success: true });
        break;
        
      case "GET_VIDEO_COUNT":
        sendResponse({ success: true, data: { count: this.videos.size } });
        break;
        
      default:
        sendResponse({ success: false, error: "Unknown message type" });
    }
  }

  findBestVideo() {
    // Return the first video that's not paused, or just the first video
    for (const video of this.videos) {
      if (!video.paused) {
        return video;
      }
    }
    return this.videos.values().next().value;
  }
}

// Initialize when DOM is ready
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", () => {
    console.log("PiP Master: DOM loaded, initializing...");
    new SimplePiPMaster();
  });
} else {
  console.log("PiP Master: DOM already loaded, initializing...");
  new SimplePiPMaster();
}

console.log("PiP Master: Simple content script loaded");
