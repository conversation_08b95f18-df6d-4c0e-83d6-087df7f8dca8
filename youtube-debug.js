// YouTube-specific debug script for PiP Master
// Copy and paste this into the console on YouTube to debug issues

console.log("🎬 PiP Master YouTube Debug Script v2.0");
console.log("========================================");

// Test 1: Check YouTube page detection
console.log("\n1. YouTube Platform Detection:");
const isYouTube = window.location.hostname.includes("youtube.com");
const isVideoPage = window.location.pathname.includes("/watch");
console.log("✓ On YouTube:", isYouTube);
console.log("✓ On video page:", isVideoPage);
console.log("✓ Current URL:", window.location.href);

if (!isYouTube) {
  console.warn("⚠️ Not on YouTube! This script is for YouTube debugging.");
  console.log("Navigate to youtube.com and try again.");
}

if (!isVideoPage) {
  console.warn("⚠️ Not on a YouTube video page!");
  console.log(
    "Navigate to a specific video (youtube.com/watch?v=...) for best results."
  );
}

// Test 2: Check for YouTube video elements
console.log("\n2. YouTube Video Element Detection:");
const youtubeSelectors = [
  "#movie_player video",
  ".html5-video-player video",
  ".video-stream",
  "video.video-stream",
  "#player video",
  ".ytp-html5-video",
  "video[src*='googlevideo']",
  "video[src*='youtube']",
  ".ytp-html5-video-container video",
  "#ytd-player video",
];

let foundVideos = [];
youtubeSelectors.forEach((selector, index) => {
  try {
    const videos = document.querySelectorAll(selector);
    console.log(
      `  Selector ${index + 1} "${selector}": ${videos.length} videos`
    );

    videos.forEach((video, i) => {
      if (!foundVideos.includes(video)) {
        foundVideos.push(video);
        console.log(`    Video ${foundVideos.length}:`, {
          src: video.src || video.currentSrc || "no src",
          readyState: video.readyState,
          dimensions: `${video.videoWidth || "?"}x${video.videoHeight || "?"}`,
          paused: video.paused,
          duration: video.duration || "unknown",
        });
      }
    });
  } catch (error) {
    console.warn(`  Selector ${index + 1} failed:`, error.message);
  }
});

console.log(`✓ Total unique YouTube videos found: ${foundVideos.length}`);

// Test 3: Check YouTube player state
console.log("\n3. YouTube Player State:");
const moviePlayer = document.querySelector("#movie_player");
const ytdPlayer = document.querySelector("ytd-player");
const playerContainer = document.querySelector(".html5-video-player");

console.log("✓ #movie_player found:", !!moviePlayer);
console.log("✓ ytd-player found:", !!ytdPlayer);
console.log("✓ .html5-video-player found:", !!playerContainer);

if (moviePlayer) {
  console.log("  Movie player classes:", moviePlayer.className);
  console.log(
    "  Is fullscreen:",
    moviePlayer.classList.contains("ytp-fullscreen")
  );
  console.log(
    "  Is theater mode:",
    moviePlayer.classList.contains("ytp-large-width-mode")
  );
  console.log("  Is ad showing:", moviePlayer.classList.contains("ad-showing"));

  const playerVideos = moviePlayer.querySelectorAll("video");
  console.log("  Videos in movie player:", playerVideos.length);
}

// Test 4: Check for YouTube ads
console.log("\n4. YouTube Ad Detection:");
const adSelectors = [
  ".ad-showing",
  ".video-ads",
  ".ytp-ad-player-overlay",
  ".ytp-ad-module",
  "[class*='ad-']",
  "[id*='ad-']",
];

let adElementsFound = 0;
adSelectors.forEach((selector) => {
  const elements = document.querySelectorAll(selector);
  if (elements.length > 0) {
    console.log(`  Ad selector "${selector}": ${elements.length} elements`);
    adElementsFound += elements.length;
  }
});

console.log("✓ Total ad elements found:", adElementsFound);
console.log("✓ Likely showing ads:", adElementsFound > 0);

// Test 5: Check extension status on YouTube
console.log("\n5. Extension Status on YouTube:");
console.log("✓ Extension loaded:", !!window.pipMasterContentLoaded);
console.log("✓ Instance available:", !!window.pipMasterInstance);
console.log("✓ Debug functions available:", !!window.pipMasterDebug);

if (window.pipMasterInstance) {
  console.log("✓ Platform detected as:", window.pipMasterInstance.platform);
  console.log("✓ Videos tracked:", window.pipMasterInstance.videos.size);

  if (window.pipMasterInstance.platform !== "youtube") {
    console.warn("⚠️ Platform not detected as YouTube! This may cause issues.");
  }
}

// Test 6: Manual YouTube video detection
console.log("\n6. Manual YouTube Video Processing:");
if (window.pipMasterInstance && foundVideos.length > 0) {
  console.log("Testing video processing...");

  foundVideos.forEach((video, index) => {
    console.log(`\nProcessing video ${index + 1}:`);

    // Check suitability
    const suitable = window.pipMasterInstance.isVideoSuitableForPiP(video);
    console.log("  Suitable for PiP:", suitable);

    // Check platform-specific checks
    const platformCheck =
      window.pipMasterInstance.platformSpecificVideoCheck(video);
    console.log("  Passes platform check:", platformCheck);

    // Check if it's an ad
    const isAd = window.pipMasterInstance.isLikelyVideoAd(video);
    console.log("  Likely ad:", isAd);

    // Check if already tracked
    const tracked = window.pipMasterInstance.videos.has(video);
    console.log("  Already tracked:", tracked);

    if (!tracked && suitable && platformCheck && !isAd) {
      console.log("  → This video should be processed!");
      // Manually process it
      window.pipMasterInstance.handleVideoFound(video);
    }
  });
} else {
  console.log("⏭️ Skipping (no extension instance or videos)");
}

// Test 7: Check for overlays
console.log("\n7. YouTube Overlay Check:");
setTimeout(() => {
  const overlays = document.querySelectorAll(".pip-master-overlay");
  console.log("✓ Overlays found:", overlays.length);

  overlays.forEach((overlay, i) => {
    const style = getComputedStyle(overlay);
    const platform = overlay.getAttribute("data-platform");
    console.log(`  Overlay ${i + 1}:`, {
      platform: platform,
      display: style.display,
      visibility: style.visibility,
      opacity: style.opacity,
      position: style.position,
      top: style.top,
      right: style.right,
      zIndex: style.zIndex,
    });
  });

  if (overlays.length === 0 && foundVideos.length > 0) {
    console.warn("⚠️ No overlays found but videos exist!");
    console.log("Try running: window.pipMasterDebug.scanForVideos()");
  }
}, 2000);

// Test 8: Test YouTube PiP functionality
console.log("\n8. YouTube PiP Test:");
setTimeout(() => {
  if (foundVideos.length > 0) {
    const testVideo = foundVideos[0];
    console.log("Testing PiP on first video...");

    // Check if video is ready
    if (testVideo.readyState >= 1) {
      console.log("Video ready state: OK");

      // Test manual PiP
      testVideo
        .requestPictureInPicture()
        .then(() => {
          console.log("✅ Manual YouTube PiP successful!");
          // Exit after 2 seconds
          setTimeout(() => {
            if (document.pictureInPictureElement) {
              document.exitPictureInPicture();
              console.log("✓ PiP exited");
            }
          }, 2000);
        })
        .catch((error) => {
          console.error("❌ Manual YouTube PiP failed:", error.message);

          // Provide specific YouTube troubleshooting
          if (error.message.includes("policy")) {
            console.log("💡 This might be a YouTube Premium video or ad");
          } else if (error.message.includes("disabled")) {
            console.log("💡 PiP might be disabled by YouTube for this content");
          } else if (error.message.includes("state")) {
            console.log("💡 Video might not be fully loaded yet");
          }
        });
    } else {
      console.log("⏳ Video not ready yet, waiting...");
      testVideo.addEventListener("loadedmetadata", () => {
        console.log("✓ Video metadata loaded, PiP should now be possible");
      });
    }
  }
}, 3000);

// Test 9: YouTube-specific debugging commands
console.log("\n9. YouTube Debug Commands:");
console.log("Run these commands to debug further:");
console.log("");
console.log("// Force YouTube video scan");
console.log("window.pipMasterDebug.scanForVideos()");
console.log("");
console.log("// Check YouTube player state");
console.log("document.querySelector('#movie_player').className");
console.log("");
console.log("// List all video elements");
console.log("document.querySelectorAll('video')");
console.log("");
console.log("// Test PiP on specific video");
console.log("window.pipMasterDebug.testPiP(0)");
console.log("");
console.log("// Check for YouTube navigation events");
console.log(
  "document.addEventListener('yt-navigate-finish', () => console.log('YouTube navigation!'))"
);

// Summary
setTimeout(() => {
  console.log("\n📊 YouTube Debug Summary:");
  console.log("========================");
  console.log("YouTube page:", isYouTube ? "✅" : "❌");
  console.log("Videos found:", foundVideos.length);
  console.log("Extension loaded:", window.pipMasterContentLoaded ? "✅" : "❌");
  console.log(
    "Platform detected:",
    window.pipMasterInstance?.platform || "unknown"
  );
  console.log("Videos tracked:", window.pipMasterInstance?.videos.size || 0);
  console.log(
    "Overlays present:",
    document.querySelectorAll(".pip-master-overlay").length
  );
  console.log("Ads detected:", adElementsFound > 0 ? "⚠️" : "✅");

  console.log("\n💡 Next Steps:");
  if (!window.pipMasterContentLoaded) {
    console.log("1. Check if extension is loaded in chrome://extensions/");
  } else if (foundVideos.length === 0) {
    console.log("1. Make sure you're on a YouTube video page");
    console.log("2. Try refreshing the page");
  } else if (window.pipMasterInstance?.videos.size === 0) {
    console.log("1. Run: window.pipMasterDebug.scanForVideos()");
    console.log("2. Check console for video processing errors");
  } else {
    console.log("1. Extension appears to be working on YouTube!");
    console.log("2. Look for overlay buttons on videos");
    console.log("3. Try Alt+P keyboard shortcut");
  }
}, 4000);

console.log("\n⏳ Running YouTube tests... (will take ~4 seconds)");
console.log("Watch the console for results...");
