// Site-Specific Preferences and Enhanced Accessibility
// Smart preferences that remember user choices per website

console.log("🎯 Site-Specific Preferences & Accessibility");
console.log("============================================");

// Enhancement 4: Site-Specific Preferences Manager
class SitePreferencesManager {
  constructor(pipMaster) {
    this.pipMaster = pipMaster;
    this.currentSite = this.getCurrentSite();
    this.preferences = this.loadPreferences();
    this.setupAutoSave();
  }

  getCurrentSite() {
    const hostname = window.location.hostname;
    // Normalize common subdomains
    return hostname.replace(/^(www\.|m\.|mobile\.)/, '');
  }

  loadPreferences() {
    try {
      const stored = localStorage.getItem('pipMaster_sitePreferences');
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.warn("Failed to load site preferences:", error);
      return {};
    }
  }

  savePreferences() {
    try {
      localStorage.setItem('pipMaster_sitePreferences', JSON.stringify(this.preferences));
      console.log(`✅ Saved preferences for ${this.currentSite}`);
    } catch (error) {
      console.warn("Failed to save site preferences:", error);
    }
  }

  setupAutoSave() {
    // Auto-save preferences when PiP is used
    document.addEventListener('enterpictureinpicture', () => {
      this.recordPiPUsage();
    });

    // Save overlay position preferences
    document.addEventListener('mousemove', (event) => {
      if (event.target.closest('.pip-master-overlay')) {
        this.recordOverlayInteraction();
      }
    });
  }

  recordPiPUsage() {
    if (!this.preferences[this.currentSite]) {
      this.preferences[this.currentSite] = {};
    }

    const sitePrefs = this.preferences[this.currentSite];
    sitePrefs.lastUsed = Date.now();
    sitePrefs.usageCount = (sitePrefs.usageCount || 0) + 1;
    sitePrefs.autoEnable = true; // User has used PiP on this site

    this.savePreferences();
  }

  recordOverlayInteraction() {
    if (!this.preferences[this.currentSite]) {
      this.preferences[this.currentSite] = {};
    }

    this.preferences[this.currentSite].overlayInteracted = true;
    this.savePreferences();
  }

  getSitePreferences() {
    return this.preferences[this.currentSite] || {};
  }

  setSitePreference(key, value) {
    if (!this.preferences[this.currentSite]) {
      this.preferences[this.currentSite] = {};
    }

    this.preferences[this.currentSite][key] = value;
    this.savePreferences();
  }

  shouldAutoEnablePiP() {
    const sitePrefs = this.getSitePreferences();
    return sitePrefs.autoEnable === true && sitePrefs.usageCount > 2;
  }

  getPreferredTheme() {
    const sitePrefs = this.getSitePreferences();
    return sitePrefs.theme || 'default';
  }

  getPreferredOverlayPosition() {
    const sitePrefs = this.getSitePreferences();
    return sitePrefs.overlayPosition || 'top-right';
  }

  applyAutoSettings() {
    const sitePrefs = this.getSitePreferences();
    
    // Apply preferred theme
    if (sitePrefs.theme && this.pipMaster.themeManager) {
      this.pipMaster.themeManager.setTheme(sitePrefs.theme);
    }

    // Apply auto-PiP if user frequently uses it on this site
    if (this.shouldAutoEnablePiP() && this.pipMaster.smartAutoPiP) {
      this.pipMaster.smartAutoPiP.enable();
      console.log(`🎯 Auto-enabled Smart PiP for ${this.currentSite} (${sitePrefs.usageCount} uses)`);
    }

    // Apply overlay position preference
    if (sitePrefs.overlayPosition) {
      this.pipMaster.settings.overlayPosition = sitePrefs.overlayPosition;
    }
  }

  getUsageStats() {
    const stats = {};
    Object.keys(this.preferences).forEach(site => {
      const prefs = this.preferences[site];
      stats[site] = {
        usageCount: prefs.usageCount || 0,
        lastUsed: prefs.lastUsed ? new Date(prefs.lastUsed).toLocaleDateString() : 'Never',
        autoEnabled: prefs.autoEnable || false
      };
    });
    return stats;
  }
}

// Enhancement 5: Enhanced Accessibility Features
class AccessibilityEnhancer {
  constructor(pipMaster) {
    this.pipMaster = pipMaster;
    this.isHighContrast = false;
    this.isReducedMotion = false;
    this.screenReaderMode = false;
    this.setupAccessibilityFeatures();
  }

  setupAccessibilityFeatures() {
    this.detectSystemPreferences();
    this.setupKeyboardNavigation();
    this.setupScreenReaderSupport();
    this.setupHighContrastMode();
    this.setupReducedMotionMode();
  }

  detectSystemPreferences() {
    // Detect system preferences
    this.isHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
    this.isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    // Apply automatic adjustments
    if (this.isHighContrast) {
      this.enableHighContrastMode();
    }
    
    if (this.isReducedMotion) {
      this.enableReducedMotionMode();
    }

    console.log(`🎯 Accessibility: High Contrast: ${this.isHighContrast}, Reduced Motion: ${this.isReducedMotion}`);
  }

  setupKeyboardNavigation() {
    // Enhanced keyboard navigation
    document.addEventListener('keydown', (event) => {
      // Focus management for overlays
      if (event.key === 'Tab' && event.shiftKey) {
        this.handleTabNavigation(event, 'backward');
      } else if (event.key === 'Tab') {
        this.handleTabNavigation(event, 'forward');
      }
      
      // Escape key to exit PiP
      if (event.key === 'Escape' && document.pictureInPictureElement) {
        event.preventDefault();
        document.exitPictureInPicture();
      }
      
      // Space bar to toggle play/pause in PiP
      if (event.key === ' ' && document.pictureInPictureElement && event.target === document.body) {
        event.preventDefault();
        const video = document.pictureInPictureElement;
        if (video.paused) {
          video.play();
        } else {
          video.pause();
        }
      }
    });
  }

  handleTabNavigation(event, direction) {
    const overlays = Array.from(document.querySelectorAll('.pip-master-overlay'));
    const focusableOverlays = overlays.filter(overlay => 
      overlay.style.display !== 'none' && 
      overlay.offsetParent !== null
    );

    if (focusableOverlays.length === 0) return;

    // Make overlays focusable
    focusableOverlays.forEach(overlay => {
      if (!overlay.hasAttribute('tabindex')) {
        overlay.setAttribute('tabindex', '0');
        overlay.setAttribute('role', 'button');
        overlay.setAttribute('aria-label', 'Activate Picture-in-Picture');
      }
    });
  }

  setupScreenReaderSupport() {
    // Add ARIA labels and descriptions
    const addAriaSupport = (overlay) => {
      overlay.setAttribute('role', 'button');
      overlay.setAttribute('aria-label', 'Activate Picture-in-Picture for this video');
      overlay.setAttribute('aria-describedby', 'pip-master-description');
      
      // Add hidden description for screen readers
      if (!document.getElementById('pip-master-description')) {
        const description = document.createElement('div');
        description.id = 'pip-master-description';
        description.style.cssText = `
          position: absolute;
          left: -10000px;
          width: 1px;
          height: 1px;
          overflow: hidden;
        `;
        description.textContent = 'Click to open this video in a Picture-in-Picture window. Use Alt+P keyboard shortcut as alternative.';
        document.body.appendChild(description);
      }
    };

    // Apply to existing overlays
    document.querySelectorAll('.pip-master-overlay').forEach(addAriaSupport);

    // Apply to new overlays
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE && node.classList.contains('pip-master-overlay')) {
            addAriaSupport(node);
          }
        });
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });
  }

  enableHighContrastMode() {
    this.isHighContrast = true;
    
    // Apply high contrast styles to overlays
    const style = document.createElement('style');
    style.id = 'pip-master-high-contrast';
    style.textContent = `
      .pip-master-overlay .pip-master-container {
        background: #000000 !important;
        color: #ffffff !important;
        border: 2px solid #ffffff !important;
        box-shadow: 0 0 0 1px #000000 !important;
      }
      
      .pip-master-overlay:hover .pip-master-container {
        background: #ffffff !important;
        color: #000000 !important;
        border: 2px solid #000000 !important;
      }
      
      .pip-master-overlay:focus .pip-master-container {
        outline: 3px solid #ffff00 !important;
        outline-offset: 2px !important;
      }
    `;
    
    document.head.appendChild(style);
    console.log("✅ High contrast mode enabled");
  }

  disableHighContrastMode() {
    this.isHighContrast = false;
    const style = document.getElementById('pip-master-high-contrast');
    if (style) {
      style.remove();
    }
    console.log("❌ High contrast mode disabled");
  }

  enableReducedMotionMode() {
    this.isReducedMotion = true;
    
    // Disable animations and transitions
    const style = document.createElement('style');
    style.id = 'pip-master-reduced-motion';
    style.textContent = `
      .pip-master-overlay,
      .pip-master-overlay * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        transition-delay: 0ms !important;
      }
    `;
    
    document.head.appendChild(style);
    console.log("✅ Reduced motion mode enabled");
  }

  disableReducedMotionMode() {
    this.isReducedMotion = false;
    const style = document.getElementById('pip-master-reduced-motion');
    if (style) {
      style.remove();
    }
    console.log("❌ Reduced motion mode disabled");
  }

  announceToScreenReader(message) {
    // Create live region for screen reader announcements
    let liveRegion = document.getElementById('pip-master-live-region');
    if (!liveRegion) {
      liveRegion = document.createElement('div');
      liveRegion.id = 'pip-master-live-region';
      liveRegion.setAttribute('aria-live', 'polite');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.style.cssText = `
        position: absolute;
        left: -10000px;
        width: 1px;
        height: 1px;
        overflow: hidden;
      `;
      document.body.appendChild(liveRegion);
    }
    
    liveRegion.textContent = message;
    
    // Clear after announcement
    setTimeout(() => {
      liveRegion.textContent = '';
    }, 1000);
  }

  getAccessibilityStatus() {
    return {
      highContrast: this.isHighContrast,
      reducedMotion: this.isReducedMotion,
      screenReaderMode: this.screenReaderMode,
      systemPreferences: {
        prefersHighContrast: window.matchMedia('(prefers-contrast: high)').matches,
        prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches
      }
    };
  }
}

// Initialize site preferences and accessibility
window.initializeSiteFeatures = function() {
  console.log("🎯 Initializing Site-Specific Features");
  
  if (!window.pipMasterInstance) {
    console.error("❌ PiP Master instance not available");
    return false;
  }
  
  // Initialize Site Preferences
  window.pipMasterInstance.sitePreferences = new SitePreferencesManager(window.pipMasterInstance);
  
  // Initialize Accessibility
  window.pipMasterInstance.accessibility = new AccessibilityEnhancer(window.pipMasterInstance);
  
  // Apply auto settings for current site
  window.pipMasterInstance.sitePreferences.applyAutoSettings();
  
  console.log("✅ Site-specific features initialized");
  
  // Show current site info
  const currentSite = window.pipMasterInstance.sitePreferences.currentSite;
  const sitePrefs = window.pipMasterInstance.sitePreferences.getSitePreferences();
  
  console.log(`\n🌐 Current Site: ${currentSite}`);
  console.log("Site Preferences:", sitePrefs);
  
  return true;
};

// Auto-initialize
if (window.pipMasterInstance) {
  window.initializeSiteFeatures();
} else {
  setTimeout(() => {
    if (window.pipMasterInstance) {
      window.initializeSiteFeatures();
    }
  }, 2000);
}

console.log("\n📋 Site Features Commands:");
console.log("==========================");
console.log("initializeSiteFeatures()                                    - Initialize site features");
console.log("pipMasterInstance.sitePreferences.getUsageStats()           - View usage statistics");
console.log("pipMasterInstance.sitePreferences.setSitePreference(key, value) - Set site preference");
console.log("pipMasterInstance.accessibility.enableHighContrastMode()    - Enable high contrast");
console.log("pipMasterInstance.accessibility.getAccessibilityStatus()    - Check accessibility status");
