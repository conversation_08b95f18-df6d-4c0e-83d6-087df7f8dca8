// YouTube Compatibility Fix for PiP Master v2.0.0
// Diagnoses and fixes YouTube functionality regression after upgrade

console.log("🔧 YouTube Compatibility Fix for PiP Master v2.0.0");
console.log("==================================================");

// YouTube Compatibility Fixer
class YouTubeCompatibilityFixer {
  constructor() {
    this.diagnostics = {
      platformDetection: false,
      videoDetection: false,
      overlayCreation: false,
      pipActivation: false,
      enhancedFeatures: false,
      domStructure: false,
    };
    this.fixes = [];
    this.runYouTubeDiagnostics();
  }

  runYouTubeDiagnostics() {
    console.log("🚀 Running YouTube-specific diagnostics...");

    this.checkYouTubeEnvironment();
    this.diagnoseVideoDetection();
    this.diagnoseOverlayCreation();
    this.diagnosePiPActivation();
    this.diagnoseEnhancedFeatures();
    this.diagnoseDOMStructure();
    this.generateDiagnosticReport();
    this.applyYouTubeFixes();
  }

  checkYouTubeEnvironment() {
    console.log("\n1️⃣ Checking YouTube Environment");
    console.log("===============================");

    const checks = {
      onYouTube: window.location.hostname.includes("youtube.com"),
      pipMasterInstance: !!window.pipMasterInstance,
      platformDetected: window.pipMasterInstance?.platform === "youtube",
      videosInDOM: document.querySelectorAll("video").length > 0,
      youtubePlayer: !!document.querySelector("#movie_player"),
      youtubeContainer: !!document.querySelector("#player-container"),
    };

    console.log("Environment check:");
    Object.entries(checks).forEach(([check, result]) => {
      console.log(`${result ? "✅" : "❌"} ${check}: ${result}`);
    });

    this.diagnostics.platformDetection =
      checks.onYouTube && checks.pipMasterInstance;

    if (!checks.onYouTube) {
      console.log("⚠️ Not on YouTube - navigate to youtube.com to test");
      return;
    }

    if (!checks.platformDetected) {
      this.fixes.push("setPlatformToYouTube");
      console.log("🔧 Fix needed: Set platform to YouTube");
    }
  }

  diagnoseVideoDetection() {
    console.log("\n2️⃣ Diagnosing Video Detection");
    console.log("=============================");

    const videos = document.querySelectorAll("video");
    const trackedVideos = window.pipMasterInstance?.videos?.size || 0;
    const processedVideos = document.querySelectorAll(
      "video[data-pip-master-processed]"
    ).length;

    console.log(`Videos in DOM: ${videos.length}`);
    console.log(`Videos tracked by PiP Master: ${trackedVideos}`);
    console.log(`Videos processed: ${processedVideos}`);

    // Check video properties
    if (videos.length > 0) {
      const mainVideo = videos[0];
      console.log("Main video analysis:");
      console.log(`  Duration: ${mainVideo.duration || "Unknown"}`);
      console.log(`  Ready state: ${mainVideo.readyState}`);
      console.log(`  Paused: ${mainVideo.paused}`);
      console.log(`  Has source: ${!!mainVideo.src || !!mainVideo.currentSrc}`);
      console.log(
        `  Dimensions: ${mainVideo.videoWidth}x${mainVideo.videoHeight}`
      );

      // Check if video is suitable for PiP
      const suitable = this.isVideoSuitableForPiP(mainVideo);
      console.log(`  PiP suitable: ${suitable}`);

      this.diagnostics.videoDetection = videos.length > 0 && trackedVideos > 0;

      if (videos.length > 0 && trackedVideos === 0) {
        this.fixes.push("fixVideoDetection");
        console.log("🔧 Fix needed: Video detection not working");
      }
    } else {
      console.log("❌ No videos found in DOM");
      this.fixes.push("waitForVideoLoad");
    }
  }

  isVideoSuitableForPiP(video) {
    try {
      // Check basic requirements
      if (!video || video.readyState < 2) return false;
      if (video.duration < 30) return false;
      if (video.videoWidth < 200 || video.videoHeight < 100) return false;

      // Check for ads (YouTube-specific)
      const isAd =
        video.closest(".ad-showing") ||
        video.closest('[class*="ad"]') ||
        video.duration < 60; // Short videos might be ads

      return !isAd;
    } catch (error) {
      console.error("Error checking video suitability:", error);
      return false;
    }
  }

  diagnoseOverlayCreation() {
    console.log("\n3️⃣ Diagnosing Overlay Creation");
    console.log("==============================");

    const overlays = document.querySelectorAll(".pip-master-overlay");
    const videos = document.querySelectorAll("video");

    console.log(`Overlays created: ${overlays.length}`);
    console.log(`Videos available: ${videos.length}`);

    if (videos.length > 0 && overlays.length === 0) {
      this.fixes.push("recreateOverlays");
      console.log("🔧 Fix needed: No overlays created for videos");
    }

    // Check overlay positioning and visibility
    overlays.forEach((overlay, index) => {
      const rect = overlay.getBoundingClientRect();
      const visible = rect.width > 0 && rect.height > 0;
      console.log(
        `Overlay ${index + 1}: ${visible ? "Visible" : "Hidden"} (${
          rect.width
        }x${rect.height})`
      );
    });

    this.diagnostics.overlayCreation = overlays.length > 0;
  }

  diagnosePiPActivation() {
    console.log("\n4️⃣ Diagnosing PiP Activation");
    console.log("============================");

    const videos = document.querySelectorAll("video");
    let pipCapable = false;
    let pipActive = !!document.pictureInPictureElement;

    console.log(`PiP currently active: ${pipActive}`);

    if (videos.length > 0) {
      const mainVideo = videos[0];

      // Check PiP capability
      pipCapable = "requestPictureInPicture" in mainVideo;
      console.log(`Video PiP capable: ${pipCapable}`);

      // Test PiP activation (without actually activating)
      try {
        // Check if video can enter PiP
        const canEnterPiP =
          !mainVideo.disablePictureInPicture &&
          mainVideo.readyState >= 2 &&
          !document.pictureInPictureElement;
        console.log(`Can enter PiP: ${canEnterPiP}`);

        this.diagnostics.pipActivation = pipCapable && canEnterPiP;

        if (!canEnterPiP) {
          this.fixes.push("fixPiPActivation");
          console.log("🔧 Fix needed: PiP activation issues");
        }
      } catch (error) {
        console.error("PiP activation test failed:", error);
        this.fixes.push("fixPiPActivation");
      }
    }
  }

  diagnoseEnhancedFeatures() {
    console.log("\n5️⃣ Diagnosing Enhanced Features on YouTube");
    console.log("==========================================");

    const features = {
      smartAutoPiP: !!window.pipMasterInstance?.smartAutoPiP,
      timelineControl: !!window.pipMasterInstance?.timelineControl,
      audioControl:
        !!window.pipMasterInstance?.settingsPanel?.applyAudioControlSettings,
      themeManager: !!window.pipMasterInstance?.themeManager,
    };

    console.log("Enhanced features status:");
    Object.entries(features).forEach(([feature, available]) => {
      console.log(
        `${available ? "✅" : "❌"} ${feature}: ${
          available ? "Available" : "Missing"
        }`
      );
    });

    // Test Smart Auto-PiP on YouTube
    if (features.smartAutoPiP) {
      try {
        const smartAutoPiP = window.pipMasterInstance.smartAutoPiP;
        const canDetectTabSwitch =
          typeof smartAutoPiP.handleVisibilityChange === "function";
        console.log(
          `Smart Auto-PiP tab detection: ${canDetectTabSwitch ? "✅" : "❌"}`
        );
      } catch (error) {
        console.error("Smart Auto-PiP test failed:", error);
        this.fixes.push("fixSmartAutoPiP");
      }
    }

    // Test timeline control on YouTube
    if (features.timelineControl) {
      try {
        const timelineControl = window.pipMasterInstance.timelineControl;
        const canToggle = typeof timelineControl.toggle === "function";
        console.log(`Timeline control toggle: ${canToggle ? "✅" : "❌"}`);
      } catch (error) {
        console.error("Timeline control test failed:", error);
        this.fixes.push("fixTimelineControl");
      }
    }

    this.diagnostics.enhancedFeatures = Object.values(features).every(Boolean);
  }

  diagnoseDOMStructure() {
    console.log("\n6️⃣ Diagnosing YouTube DOM Structure");
    console.log("===================================");

    const youtubeElements = {
      player: document.querySelector("#movie_player"),
      playerContainer: document.querySelector("#player-container"),
      videoContainer: document.querySelector(".html5-video-container"),
      video: document.querySelector("#movie_player video"),
      controls: document.querySelector(".ytp-chrome-bottom"),
    };

    console.log("YouTube DOM elements:");
    Object.entries(youtubeElements).forEach(([element, found]) => {
      console.log(
        `${found ? "✅" : "❌"} ${element}: ${found ? "Found" : "Missing"}`
      );
    });

    // Check for YouTube's dynamic loading
    const isWatchPage = window.location.pathname === "/watch";
    const hasVideoId = window.location.search.includes("v=");

    console.log(`On watch page: ${isWatchPage}`);
    console.log(`Has video ID: ${hasVideoId}`);

    this.diagnostics.domStructure =
      !!youtubeElements.player && !!youtubeElements.video;

    if (!this.diagnostics.domStructure) {
      this.fixes.push("waitForYouTubeLoad");
      console.log("🔧 Fix needed: YouTube DOM not fully loaded");
    }
  }

  generateDiagnosticReport() {
    console.log("\n📊 YOUTUBE DIAGNOSTIC REPORT");
    console.log("============================");

    const {
      platformDetection,
      videoDetection,
      overlayCreation,
      pipActivation,
      enhancedFeatures,
      domStructure,
    } = this.diagnostics;

    console.log("Diagnostic Results:");
    console.log(
      `🌐 Platform Detection: ${platformDetection ? "✅ PASS" : "❌ FAIL"}`
    );
    console.log(
      `📹 Video Detection: ${videoDetection ? "✅ PASS" : "❌ FAIL"}`
    );
    console.log(
      `🎨 Overlay Creation: ${overlayCreation ? "✅ PASS" : "❌ FAIL"}`
    );
    console.log(`📺 PiP Activation: ${pipActivation ? "✅ PASS" : "❌ FAIL"}`);
    console.log(
      `⚡ Enhanced Features: ${enhancedFeatures ? "✅ PASS" : "❌ FAIL"}`
    );
    console.log(`🏗️ DOM Structure: ${domStructure ? "✅ PASS" : "❌ FAIL"}`);

    const passedTests = Object.values(this.diagnostics).filter(Boolean).length;
    const totalTests = Object.keys(this.diagnostics).length;
    const successRate = Math.round((passedTests / totalTests) * 100);

    console.log(
      `\n📊 YouTube Compatibility: ${passedTests}/${totalTests} (${successRate}%)`
    );

    if (successRate >= 80) {
      console.log("🎉 YouTube compatibility is good!");
    } else if (successRate >= 60) {
      console.log("⚠️ YouTube compatibility has issues");
    } else {
      console.log("❌ YouTube compatibility is broken");
    }

    console.log(`\n🔧 Fixes needed: ${this.fixes.length}`);
    this.fixes.forEach((fix, index) => {
      console.log(`${index + 1}. ${fix}`);
    });
  }

  applyYouTubeFixes() {
    console.log("\n🔧 APPLYING YOUTUBE FIXES");
    console.log("=========================");

    if (this.fixes.length === 0) {
      console.log("✅ No fixes needed - YouTube compatibility is good!");
      return;
    }

    this.fixes.forEach((fix) => {
      try {
        switch (fix) {
          case "setPlatformToYouTube":
            this.setPlatformToYouTube();
            break;
          case "fixVideoDetection":
            this.fixVideoDetection();
            break;
          case "recreateOverlays":
            this.recreateOverlays();
            break;
          case "fixPiPActivation":
            this.fixPiPActivation();
            break;
          case "fixSmartAutoPiP":
            this.fixSmartAutoPiP();
            break;
          case "fixTimelineControl":
            this.fixTimelineControl();
            break;
          case "waitForVideoLoad":
            this.waitForVideoLoad();
            break;
          case "waitForYouTubeLoad":
            this.waitForYouTubeLoad();
            break;
          default:
            console.log(`⚠️ Unknown fix: ${fix}`);
        }
      } catch (error) {
        console.error(`❌ Fix '${fix}' failed:`, error);
      }
    });

    // Re-run diagnostics after fixes
    setTimeout(() => {
      console.log("\n🔄 Re-running diagnostics after fixes...");
      this.runPostFixDiagnostics();
    }, 2000);
  }

  setPlatformToYouTube() {
    console.log("🔧 Setting platform to YouTube...");
    if (window.pipMasterInstance) {
      window.pipMasterInstance.platform = "youtube";
      console.log("✅ Platform set to YouTube");
    }
  }

  fixVideoDetection() {
    console.log("🔧 Fixing video detection...");

    if (!window.pipMasterInstance.videos) {
      window.pipMasterInstance.videos = new Set();
    }

    // Clear existing tracking
    window.pipMasterInstance.videos.clear();

    // Remove processed markers
    document
      .querySelectorAll("video[data-pip-master-processed]")
      .forEach((video) => {
        video.removeAttribute("data-pip-master-processed");
      });

    // Force video scan
    this.performYouTubeVideoScan();

    console.log("✅ Video detection fixed");
  }

  performYouTubeVideoScan() {
    const videos = document.querySelectorAll("video");
    let processedCount = 0;

    videos.forEach((video) => {
      if (this.isVideoSuitableForPiP(video)) {
        window.pipMasterInstance.videos.add(video);
        this.createYouTubeOverlay(video);
        video.setAttribute("data-pip-master-processed", "true");
        processedCount++;
      }
    });

    console.log(`📹 Processed ${processedCount} suitable videos`);
  }

  createYouTubeOverlay(video) {
    // Remove existing overlay
    const existingOverlay = video.parentElement?.querySelector(
      ".pip-master-overlay"
    );
    if (existingOverlay) {
      existingOverlay.remove();
    }

    // Find appropriate container for YouTube
    let container = video.parentElement;

    // Look for YouTube-specific containers
    const youtubeContainer =
      video.closest(".html5-video-container") ||
      video.closest("#movie_player") ||
      video.parentElement;

    if (youtubeContainer) {
      container = youtubeContainer;
    }

    // Create overlay
    const overlay = document.createElement("div");
    overlay.className = "pip-master-overlay";
    overlay.style.cssText = `
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 9999;
      pointer-events: auto;
    `;

    // Apply current theme
    const theme =
      window.pipMasterInstance.themeManager?.currentTheme || "default";
    const themeStyles = this.getThemeStyles(theme);

    overlay.innerHTML = `
      <div class="pip-master-container" style="
        ${themeStyles}
        padding: 8px 12px;
        border-radius: 6px;
        font-family: Arial, sans-serif;
        font-size: 14px;
        cursor: pointer;
        transition: opacity 0.3s ease;
        user-select: none;
      ">
        📺 PiP
      </div>
    `;

    // Add click handler
    overlay.addEventListener("click", (e) => {
      e.preventDefault();
      e.stopPropagation();

      if (document.pictureInPictureElement) {
        document.exitPictureInPicture();
      } else {
        video.requestPictureInPicture().catch((error) => {
          console.error("PiP activation failed:", error);
        });
      }
    });

    // Position overlay relative to container
    container.style.position = "relative";
    container.appendChild(overlay);
  }

  getThemeStyles(theme) {
    const themes = {
      default:
        "background: rgba(0, 0, 0, 0.8); color: #ffffff; border: 2px solid rgba(255, 255, 255, 0.3);",
      minimal:
        "background: rgba(255, 255, 255, 0.95); color: #333333; border: 1px solid rgba(0, 0, 0, 0.1);",
      neon: "background: rgba(0, 20, 40, 0.9); color: #00ffff; border: 2px solid #00ffff; box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);",
      dark: "background: rgba(20, 20, 20, 0.95); color: #ffffff; border: 2px solid #444444;",
      youtube:
        "background: rgba(255, 0, 0, 0.9); color: #ffffff; border: 2px solid #ff0000;",
    };

    return themes[theme] || themes.default;
  }

  recreateOverlays() {
    console.log("🔧 Recreating overlays...");

    // Remove all existing overlays
    document.querySelectorAll(".pip-master-overlay").forEach((overlay) => {
      overlay.remove();
    });

    // Create new overlays for all suitable videos
    const videos = document.querySelectorAll("video");
    videos.forEach((video) => {
      if (this.isVideoSuitableForPiP(video)) {
        this.createYouTubeOverlay(video);
      }
    });

    console.log("✅ Overlays recreated");
  }

  fixPiPActivation() {
    console.log("🔧 Fixing PiP activation...");

    // Ensure PiP Master has proper toggle method
    if (!window.pipMasterInstance.togglePiP) {
      window.pipMasterInstance.togglePiP = function () {
        const video = document.querySelector("video");
        if (video) {
          if (document.pictureInPictureElement) {
            document.exitPictureInPicture();
          } else {
            video.requestPictureInPicture().catch((error) => {
              console.error("PiP toggle failed:", error);
            });
          }
        }
      };
    }

    console.log("✅ PiP activation fixed");
  }

  fixSmartAutoPiP() {
    console.log("🔧 Fixing Smart Auto-PiP for YouTube...");

    const smartAutoPiP = window.pipMasterInstance.smartAutoPiP;
    if (smartAutoPiP) {
      // Enhance findSuitableVideo for YouTube
      smartAutoPiP.findSuitableVideo = () => {
        const videos = document.querySelectorAll("video");
        for (const video of videos) {
          if (this.isVideoSuitableForPiP(video) && !video.paused) {
            return video;
          }
        }
        return null;
      };
    }

    console.log("✅ Smart Auto-PiP fixed for YouTube");
  }

  fixTimelineControl() {
    console.log("🔧 Fixing timeline control for YouTube...");

    const timelineControl = window.pipMasterInstance.timelineControl;
    if (timelineControl) {
      // Ensure timeline works with YouTube videos
      timelineControl.getActiveVideo = () => {
        return (
          document.pictureInPictureElement || document.querySelector("video")
        );
      };
    }

    console.log("✅ Timeline control fixed for YouTube");
  }

  waitForVideoLoad() {
    console.log("🔧 Waiting for video to load...");

    const checkForVideo = () => {
      const videos = document.querySelectorAll("video");
      if (videos.length > 0) {
        console.log("✅ Video found, re-running detection");
        this.fixVideoDetection();
      } else {
        setTimeout(checkForVideo, 1000);
      }
    };

    setTimeout(checkForVideo, 1000);
  }

  waitForYouTubeLoad() {
    console.log("🔧 Waiting for YouTube to fully load...");

    const checkForYouTube = () => {
      const player = document.querySelector("#movie_player");
      const video = document.querySelector("#movie_player video");

      if (player && video) {
        console.log("✅ YouTube fully loaded, re-running diagnostics");
        setTimeout(() => this.runYouTubeDiagnostics(), 500);
      } else {
        setTimeout(checkForYouTube, 1000);
      }
    };

    setTimeout(checkForYouTube, 1000);
  }

  runPostFixDiagnostics() {
    console.log("🔄 POST-FIX DIAGNOSTICS");
    console.log("=======================");

    const videos = document.querySelectorAll("video");
    const overlays = document.querySelectorAll(".pip-master-overlay");
    const trackedVideos = window.pipMasterInstance?.videos?.size || 0;

    console.log(`Videos in DOM: ${videos.length}`);
    console.log(`Overlays created: ${overlays.length}`);
    console.log(`Videos tracked: ${trackedVideos}`);

    if (videos.length > 0 && overlays.length > 0 && trackedVideos > 0) {
      console.log("🎉 YouTube functionality restored!");
    } else {
      console.log("⚠️ Some issues may remain");
    }
  }
}

// Quick YouTube fix command
window.quickFixYouTube = function () {
  console.log("⚡ Quick YouTube Fix");
  console.log("===================");

  // Set platform
  if (window.pipMasterInstance) {
    window.pipMasterInstance.platform = "youtube";
  }

  // Clear and rescan
  if (window.pipMasterInstance.videos) {
    window.pipMasterInstance.videos.clear();
  }

  // Remove existing overlays
  document
    .querySelectorAll(".pip-master-overlay")
    .forEach((overlay) => overlay.remove());

  // Remove processed markers
  document
    .querySelectorAll("video[data-pip-master-processed]")
    .forEach((video) => {
      video.removeAttribute("data-pip-master-processed");
    });

  // Force scan
  if (window.pipMasterInstance.performUniversalVideoScan) {
    window.pipMasterInstance.performUniversalVideoScan();
  }

  console.log("✅ Quick YouTube fix applied");
};

// Test YouTube functionality command
window.testYouTubeFunctionality = function () {
  console.log("🧪 Testing YouTube Functionality");
  console.log("================================");

  const tests = {
    onYouTube: window.location.hostname.includes("youtube.com"),
    videosFound: document.querySelectorAll("video").length > 0,
    overlaysCreated:
      document.querySelectorAll(".pip-master-overlay").length > 0,
    pipCapable: false,
    enhancedFeatures: false,
  };

  // Test PiP capability
  const video = document.querySelector("video");
  if (video) {
    tests.pipCapable =
      "requestPictureInPicture" in video && !video.disablePictureInPicture;
  }

  // Test enhanced features
  tests.enhancedFeatures = !!(
    window.pipMasterInstance?.smartAutoPiP &&
    window.pipMasterInstance?.timelineControl &&
    window.pipMasterInstance?.settingsPanel?.applyAudioControlSettings
  );

  console.log("YouTube functionality test:");
  Object.entries(tests).forEach(([test, result]) => {
    console.log(`${result ? "✅" : "❌"} ${test}: ${result}`);
  });

  const score = Object.values(tests).filter(Boolean).length;
  console.log(`\n📊 Score: ${score}/5 (${score * 20}%)`);

  if (score >= 4) {
    console.log("🎉 YouTube functionality is working!");
  } else {
    console.log("⚠️ YouTube functionality needs attention");
  }

  return { score, tests };
};

// Auto-run YouTube diagnostics if on YouTube
if (window.location.hostname.includes("youtube.com")) {
  console.log("🚀 Auto-running YouTube compatibility diagnostics...");
  window.youtubeCompatibilityFixer = new YouTubeCompatibilityFixer();
} else {
  console.log("⚠️ Not on YouTube - navigate to youtube.com to run diagnostics");
}

console.log("\n📋 YouTube Fix Commands:");
console.log("========================");
console.log("quickFixYouTube()                 - Quick YouTube fix");
console.log("testYouTubeFunctionality()        - Test YouTube features");
console.log("new YouTubeCompatibilityFixer()   - Full diagnostic and fix");
