// YouTube Regression Fix Script
// Targeted fixes for common YouTube functionality issues

console.log("🔧 YouTube Regression Fix Script");
console.log("================================");

// Regression Fix Manager
class YouTubeRegressionFix {
  constructor() {
    this.fixes = [];
    this.appliedFixes = [];
    this.runFixes();
  }

  runFixes() {
    console.log("🚀 Analyzing and applying fixes...");
    
    // Check what needs fixing
    this.analyzeIssues();
    
    // Apply fixes in order of priority
    this.applyHighPriorityFixes();
    this.applyMediumPriorityFixes();
    this.applyLowPriorityFixes();
    
    // Verify fixes
    this.verifyFixes();
    
    this.generateFixReport();
  }

  analyzeIssues() {
    console.log("\n🔍 Analyzing issues...");
    
    // Check if extension is loaded
    if (!window.pipMasterInstance) {
      this.fixes.push({
        priority: 'high',
        issue: 'Extension not loaded',
        fix: 'waitForExtension',
        description: 'Extension instance not available'
      });
      return; // Can't continue without extension
    }
    
    // Check if core detection is working
    const videos = document.querySelectorAll('video');
    const trackedVideos = window.pipMasterInstance.videos ? window.pipMasterInstance.videos.size : 0;
    
    if (videos.length > 0 && trackedVideos === 0) {
      this.fixes.push({
        priority: 'high',
        issue: 'Core detection broken',
        fix: 'restoreCoreDetection',
        description: `${videos.length} videos found but ${trackedVideos} tracked`
      });
    }
    
    // Check for method overrides
    if (window.pipMasterInstance._originalFindVideos || 
        window.pipMasterInstance._originalIsVideoSuitable || 
        window.pipMasterInstance._originalTogglePiP) {
      this.fixes.push({
        priority: 'high',
        issue: 'Methods overridden',
        fix: 'restoreOriginalMethods',
        description: 'Enhanced features may have broken core methods'
      });
    }
    
    // Check for excessive overlays
    const overlayCount = document.querySelectorAll('.pip-master-overlay').length;
    if (overlayCount > 10) {
      this.fixes.push({
        priority: 'medium',
        issue: 'Excessive overlays',
        fix: 'cleanupOverlays',
        description: `${overlayCount} overlays found`
      });
    }
    
    // Check for performance issues
    if (window.pipMasterInstance.performanceOptimizer?.scanThrottle > 5000) {
      this.fixes.push({
        priority: 'medium',
        issue: 'Scan throttle too high',
        fix: 'resetPerformanceSettings',
        description: 'Video scanning may be too slow'
      });
    }
    
    // Check platform detection
    if (window.pipMasterInstance.platform !== 'youtube') {
      this.fixes.push({
        priority: 'low',
        issue: 'Platform not detected as YouTube',
        fix: 'forcePlatformDetection',
        description: `Platform detected as: ${window.pipMasterInstance.platform}`
      });
    }
  }

  applyHighPriorityFixes() {
    console.log("\n🚨 Applying high priority fixes...");
    
    const highPriorityFixes = this.fixes.filter(fix => fix.priority === 'high');
    
    highPriorityFixes.forEach(fix => {
      console.log(`🔧 Fixing: ${fix.issue}`);
      try {
        this[fix.fix]();
        this.appliedFixes.push(fix);
        console.log(`✅ Fixed: ${fix.issue}`);
      } catch (error) {
        console.error(`❌ Failed to fix ${fix.issue}:`, error);
      }
    });
  }

  applyMediumPriorityFixes() {
    console.log("\n⚠️ Applying medium priority fixes...");
    
    const mediumPriorityFixes = this.fixes.filter(fix => fix.priority === 'medium');
    
    mediumPriorityFixes.forEach(fix => {
      console.log(`🔧 Fixing: ${fix.issue}`);
      try {
        this[fix.fix]();
        this.appliedFixes.push(fix);
        console.log(`✅ Fixed: ${fix.issue}`);
      } catch (error) {
        console.error(`❌ Failed to fix ${fix.issue}:`, error);
      }
    });
  }

  applyLowPriorityFixes() {
    console.log("\n💡 Applying low priority fixes...");
    
    const lowPriorityFixes = this.fixes.filter(fix => fix.priority === 'low');
    
    lowPriorityFixes.forEach(fix => {
      console.log(`🔧 Fixing: ${fix.issue}`);
      try {
        this[fix.fix]();
        this.appliedFixes.push(fix);
        console.log(`✅ Fixed: ${fix.issue}`);
      } catch (error) {
        console.error(`❌ Failed to fix ${fix.issue}:`, error);
      }
    });
  }

  // Fix implementations
  waitForExtension() {
    console.log("⏳ Waiting for extension to load...");
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (window.pipMasterInstance) {
          clearInterval(checkInterval);
          console.log("✅ Extension loaded");
          resolve();
        }
      }, 100);
      
      setTimeout(() => {
        clearInterval(checkInterval);
        console.warn("⚠️ Extension load timeout");
        resolve();
      }, 5000);
    });
  }

  restoreCoreDetection() {
    console.log("🎯 Restoring core video detection...");
    
    // Reset to Google-style detection
    window.pipMasterInstance.performUniversalVideoScan = function() {
      console.log("PiP Master: [RESTORED] Enhanced universal video scan (Google-style)...");

      const videos = Array.from(document.querySelectorAll('video')).filter(video => {
        const rect = video.getBoundingClientRect();
        const style = getComputedStyle(video);
        
        return (
          video.tagName === 'VIDEO' &&
          !video.disablePictureInPicture &&
          (rect.width > 0 || rect.height > 0 || video.videoWidth > 0 || video.videoHeight > 0) &&
          style.display !== 'none'
        );
      });

      console.log(`PiP Master: [RESTORED] Enhanced scan found ${videos.length} suitable videos`);

      videos.forEach((video, index) => {
        if (!this.videos.has(video)) {
          console.log(`PiP Master: [RESTORED] Processing video ${index + 1}:`, video.src || video.currentSrc || "no src");
          this.handleVideoFound(video);
        }
      });

      console.log(`PiP Master: [RESTORED] Enhanced scan complete. Total videos: ${this.videos.size}`);
    };
    
    // Force immediate scan
    window.pipMasterInstance.performUniversalVideoScan();
  }

  restoreOriginalMethods() {
    console.log("🔄 Restoring original methods...");
    
    if (window.pipMasterInstance._originalFindVideos) {
      window.pipMasterInstance.performUniversalVideoScan = window.pipMasterInstance._originalFindVideos;
      delete window.pipMasterInstance._originalFindVideos;
      console.log("✅ Restored original video scan method");
    }
    
    if (window.pipMasterInstance._originalIsVideoSuitable) {
      window.pipMasterInstance.isVideoSuitableForPiP = window.pipMasterInstance._originalIsVideoSuitable;
      delete window.pipMasterInstance._originalIsVideoSuitable;
      console.log("✅ Restored original suitability check");
    }
    
    if (window.pipMasterInstance._originalTogglePiP) {
      window.pipMasterInstance.togglePiP = window.pipMasterInstance._originalTogglePiP;
      delete window.pipMasterInstance._originalTogglePiP;
      console.log("✅ Restored original toggle PiP method");
    }
  }

  cleanupOverlays() {
    console.log("🧹 Cleaning up excessive overlays...");
    
    const overlays = document.querySelectorAll('.pip-master-overlay');
    let removed = 0;
    
    overlays.forEach(overlay => {
      const container = overlay.parentElement;
      const video = container ? container.querySelector('video') : null;
      
      if (!video || !document.contains(video) || !this.isVideoVisible(video)) {
        overlay.remove();
        removed++;
      }
    });
    
    console.log(`✅ Removed ${removed} orphaned overlays`);
    
    // Clear overlay tracking
    if (window.pipMasterInstance.overlays) {
      const validOverlays = new Map();
      window.pipMasterInstance.overlays.forEach((overlay, video) => {
        if (document.contains(overlay) && document.contains(video)) {
          validOverlays.set(video, overlay);
        }
      });
      window.pipMasterInstance.overlays = validOverlays;
    }
  }

  resetPerformanceSettings() {
    console.log("⚡ Resetting performance settings...");
    
    if (window.pipMasterInstance.performanceOptimizer) {
      window.pipMasterInstance.performanceOptimizer.scanThrottle = 2000; // Reset to 2 seconds
      window.pipMasterInstance.performanceOptimizer.detectionFrequency = 'normal';
      console.log("✅ Performance settings reset to normal");
    }
  }

  forcePlatformDetection() {
    console.log("🌐 Forcing YouTube platform detection...");
    
    window.pipMasterInstance.platform = 'youtube';
    
    // Setup YouTube-specific detection if available
    if (window.pipMasterInstance.setupYouTubeDetection) {
      window.pipMasterInstance.setupYouTubeDetection();
    }
    
    console.log("✅ Platform forced to YouTube");
  }

  isVideoVisible(video) {
    const rect = video.getBoundingClientRect();
    const style = getComputedStyle(video);
    
    return rect.width > 0 && rect.height > 0 && style.display !== 'none';
  }

  verifyFixes() {
    console.log("\n✅ Verifying fixes...");
    
    // Test video detection
    const videos = document.querySelectorAll('video');
    const trackedVideos = window.pipMasterInstance.videos ? window.pipMasterInstance.videos.size : 0;
    
    console.log(`📹 Videos in DOM: ${videos.length}`);
    console.log(`🎯 Videos tracked: ${trackedVideos}`);
    
    if (videos.length > 0 && trackedVideos > 0) {
      console.log("✅ Video detection working");
    } else if (videos.length > 0) {
      console.warn("⚠️ Videos found but not tracked - may need manual intervention");
    }
    
    // Test overlay creation
    const overlays = document.querySelectorAll('.pip-master-overlay');
    console.log(`🎨 Overlays in DOM: ${overlays.length}`);
    
    // Test PiP functionality
    if (videos.length > 0) {
      const testVideo = videos[0];
      if (window.pipMasterInstance.isVideoSuitableForPiP) {
        const suitable = window.pipMasterInstance.isVideoSuitableForPiP(testVideo);
        console.log(`🧪 First video suitability: ${suitable ? '✅ Suitable' : '❌ Not suitable'}`);
      }
    }
  }

  generateFixReport() {
    console.log("\n📊 FIX REPORT");
    console.log("=============");
    
    console.log(`🔧 Total fixes identified: ${this.fixes.length}`);
    console.log(`✅ Fixes applied successfully: ${this.appliedFixes.length}`);
    console.log(`❌ Fixes failed: ${this.fixes.length - this.appliedFixes.length}`);
    
    if (this.appliedFixes.length > 0) {
      console.log("\n✅ Applied fixes:");
      this.appliedFixes.forEach((fix, index) => {
        console.log(`${index + 1}. ${fix.issue} (${fix.priority} priority)`);
      });
    }
    
    const failedFixes = this.fixes.filter(fix => !this.appliedFixes.includes(fix));
    if (failedFixes.length > 0) {
      console.log("\n❌ Failed fixes:");
      failedFixes.forEach((fix, index) => {
        console.log(`${index + 1}. ${fix.issue} (${fix.priority} priority)`);
      });
    }
    
    // Final test
    console.log("\n🧪 FINAL VERIFICATION:");
    setTimeout(() => {
      this.runFinalTest();
    }, 1000);
  }

  runFinalTest() {
    const videos = document.querySelectorAll('video');
    const trackedVideos = window.pipMasterInstance?.videos?.size || 0;
    const overlays = document.querySelectorAll('.pip-master-overlay');
    
    console.log("Final state:");
    console.log(`📹 Videos: ${videos.length} found, ${trackedVideos} tracked`);
    console.log(`🎨 Overlays: ${overlays.length}`);
    console.log(`🌐 Platform: ${window.pipMasterInstance?.platform || 'unknown'}`);
    
    const success = videos.length > 0 && trackedVideos > 0 && overlays.length > 0;
    
    if (success) {
      console.log("🎉 SUCCESS: YouTube functionality appears to be restored!");
      console.log("👀 Look for overlay buttons on YouTube videos");
      console.log("⌨️ Try Alt+P keyboard shortcut");
    } else {
      console.log("⚠️ PARTIAL SUCCESS: Some issues may remain");
      console.log("💡 Try running manual commands:");
      console.log("   forceYouTubeDetection()");
      console.log("   window.pipMasterInstance.performUniversalVideoScan()");
    }
  }
}

// Manual fix functions for immediate use
window.quickYouTubeFix = function() {
  console.log("⚡ Quick YouTube Fix");
  console.log("===================");
  
  if (!window.pipMasterInstance) {
    console.error("❌ Extension not loaded");
    return false;
  }
  
  // 1. Force platform
  window.pipMasterInstance.platform = 'youtube';
  
  // 2. Clear existing state
  if (window.pipMasterInstance.videos) {
    window.pipMasterInstance.videos.clear();
  }
  
  // 3. Clean overlays
  document.querySelectorAll('.pip-master-overlay').forEach(overlay => overlay.remove());
  
  // 4. Force detection
  const videos = document.querySelectorAll('video');
  console.log(`Found ${videos.length} videos`);
  
  videos.forEach((video, index) => {
    if (video.tagName === 'VIDEO' && !video.disablePictureInPicture) {
      try {
        window.pipMasterInstance.videos.add(video);
        window.pipMasterInstance.createOverlay(video);
        console.log(`✅ Processed video ${index + 1}`);
      } catch (error) {
        console.warn(`Failed to process video ${index + 1}:`, error);
      }
    }
  });
  
  console.log("✅ Quick fix complete");
  return true;
};

window.testYouTubePiP = function() {
  console.log("🧪 Testing YouTube PiP");
  console.log("======================");
  
  const video = document.querySelector('#movie_player video') || document.querySelector('video');
  
  if (!video) {
    console.error("❌ No video found");
    return false;
  }
  
  console.log("Testing video:", video.src || video.currentSrc || 'no src');
  
  video.requestPictureInPicture()
    .then(() => {
      console.log("✅ PiP test successful!");
      setTimeout(() => {
        if (document.pictureInPictureElement) {
          document.exitPictureInPicture();
        }
      }, 3000);
    })
    .catch(error => {
      console.error("❌ PiP test failed:", error.message);
    });
};

// Auto-run the fix
console.log("🚀 Auto-running YouTube regression fix...");
window.youtubeRegressionFix = new YouTubeRegressionFix();

console.log("\n📋 Available Fix Commands:");
console.log("==========================");
console.log("quickYouTubeFix()                    - Quick fix for YouTube");
console.log("testYouTubePiP()                     - Test PiP functionality");
console.log("new YouTubeRegressionFix()           - Run full regression fix");
