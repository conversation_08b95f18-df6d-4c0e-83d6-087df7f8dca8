// PiP Master Content Script
// Handles video detection and PiP functionality on web pages

console.log("PiP Master: Content script starting...");

class PiPMasterContent {
  constructor() {
    console.log("PiP Master: Initializing content script...");
    this.videos = new Set();
    this.activeVideo = null;
    this.pipWindow = null;
    this.settings = {};
    this.overlays = new Map();
    this.isInitialized = false;
    this.autoActivationTimer = null;
    this.lastInteractionTime = Date.now();
    this.platform = this.detectPlatform();
    this.videoObserver = null;
    this.retryAttempts = new Map();
    this.maxRetries = 3;
    this.detectionInterval = null;

    this.init();
  }

  detectPlatform() {
    const hostname = window.location.hostname.toLowerCase();
    console.log("PiP Master: Detecting platform for hostname:", hostname);

    const platforms = {
      youtube: ["youtube.com", "youtu.be", "m.youtube.com"],
      vimeo: ["vimeo.com", "player.vimeo.com"],
      twitch: ["twitch.tv", "www.twitch.tv"],
      netflix: ["netflix.com", "www.netflix.com"],
      hulu: ["hulu.com", "www.hulu.com"],
      disney: ["disneyplus.com", "www.disneyplus.com"],
      amazon: ["amazon.com", "primevideo.com"],
      dailymotion: ["dailymotion.com", "www.dailymotion.com"],
      facebook: ["facebook.com", "www.facebook.com"],
      twitter: ["twitter.com", "x.com"],
      tiktok: ["tiktok.com", "www.tiktok.com"],
    };

    for (const [platform, domains] of Object.entries(platforms)) {
      if (domains.some((domain) => hostname.includes(domain))) {
        console.log("PiP Master: Platform detected:", platform);
        return platform;
      }
    }
    console.log("PiP Master: Using generic platform detection");
    return "generic";
  }

  async init() {
    try {
      console.log(`PiP Master: Initializing on ${this.platform} platform`);

      // Get settings from background
      console.log("PiP Master: Requesting settings from background...");
      const response = await this.sendMessage({ type: "GET_SETTINGS" });
      this.settings = response.data || {};
      console.log("PiP Master: Settings loaded:", Object.keys(this.settings));

      // Set up event listeners
      console.log("PiP Master: Setting up event listeners...");
      this.setupEventListeners();

      // Start enhanced universal video detection
      console.log("PiP Master: Starting video detection...");
      this.startUniversalVideoDetection();

      // YouTube-specific aggressive initialization
      if (this.platform === "youtube") {
        console.log(
          "PiP Master: YouTube detected - running aggressive detection..."
        );
        // Multiple detection attempts for YouTube
        setTimeout(() => {
          console.log("PiP Master: YouTube detection attempt 2...");
          this.performUniversalVideoScan();
        }, 2000);
        setTimeout(() => {
          console.log("PiP Master: YouTube detection attempt 3...");
          this.performUniversalVideoScan();
        }, 5000);
      }

      this.isInitialized = true;
      console.log("PiP Master: Content script initialized successfully");
    } catch (error) {
      console.error("PiP Master: Failed to initialize content script:", error);
      console.error("Error details:", error.message);
      // Retry initialization after delay for dynamic content
      console.log("PiP Master: Retrying initialization in 2 seconds...");
      setTimeout(() => this.init(), 2000);
    }
  }

  setupEventListeners() {
    console.log("PiP Master: Setting up content script event listeners...");

    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      console.log("PiP Master: Content script received message:", message.type);
      this.handleMessage(message, sender, sendResponse);
      return true;
    });

    // Listen for PiP events
    document.addEventListener("enterpictureinpicture", (event) => {
      console.log("PiP Master: PiP entered");
      this.handlePiPEnter(event);
    });

    document.addEventListener("leavepictureinpicture", (event) => {
      console.log("PiP Master: PiP left");
      this.handlePiPLeave(event);
    });

    // Listen for video events
    document.addEventListener(
      "loadedmetadata",
      (event) => {
        if (event.target.tagName === "VIDEO") {
          console.log("PiP Master: Video metadata loaded, processing...");
          this.handleVideoFound(event.target);
        }
      },
      true
    );

    // Observe DOM changes for dynamically added videos
    console.log("PiP Master: Setting up DOM observer...");
    this.observeVideoChanges();

    console.log("PiP Master: Event listeners set up successfully");
  }

  observeVideoChanges() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the added node is a video
            if (node.tagName === "VIDEO") {
              this.handleVideoFound(node);
            }
            // Check for videos within the added node
            const videos = node.querySelectorAll
              ? node.querySelectorAll("video")
              : [];
            videos.forEach((video) => this.handleVideoFound(video));
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  startUniversalVideoDetection() {
    console.log("PiP Master: Starting universal video detection...");

    // Set up platform-specific detection FIRST (YouTube needs immediate setup)
    this.setupPlatformSpecificDetection();

    // Initial comprehensive scan
    this.performUniversalVideoScan();

    // Set up enhanced mutation observer
    this.setupUniversalMutationObserver();

    // Platform-specific intervals (YouTube needs more frequent checking)
    const intervalTime = this.platform === "youtube" ? 3000 : 5000;
    this.detectionInterval = setInterval(() => {
      console.log(
        `PiP Master: Performing periodic video scan (${this.platform})...`
      );
      this.performUniversalVideoScan();
    }, intervalTime);

    // Monitor for navigation changes (SPA support)
    this.setupNavigationMonitoring();
  }

  performUniversalVideoScan() {
    console.log(
      "PiP Master: Starting enhanced universal video scan (Google-style)..."
    );

    // Google-style approach: Simple but effective video detection with filtering
    const videos = Array.from(document.querySelectorAll("video")).filter(
      (video) => {
        // Google's permissive approach - basic checks only
        const rect = video.getBoundingClientRect();
        const style = getComputedStyle(video);

        return (
          // Must be a video element
          video.tagName === "VIDEO" &&
          // Must not be explicitly disabled for PiP
          !video.disablePictureInPicture &&
          // Must have some dimensions (very lenient)
          (rect.width > 0 ||
            rect.height > 0 ||
            video.videoWidth > 0 ||
            video.videoHeight > 0) &&
          // Must not be completely hidden
          style.display !== "none"
        );
      }
    );

    console.log(
      `PiP Master: Enhanced scan found ${videos.length} suitable videos`
    );

    // Process each found video with Google's approach
    videos.forEach((video, index) => {
      console.log(
        `PiP Master: Processing video ${index + 1}:`,
        video.src || video.currentSrc || "no src"
      );
      this.handleVideoFound(video);
    });

    console.log(
      `PiP Master: Enhanced scan completed. Total videos processed: ${videos.length}`
    );
  }

  getPlatformSpecificVideoSelectors() {
    const selectors = {
      youtube: [
        "video",
        "#movie_player video",
        ".html5-video-player video",
        ".video-stream",
        "video.video-stream",
        "#player video",
        ".ytp-html5-video",
        "video[src*='googlevideo']",
        "video[src*='youtube']",
        ".ytp-html5-video-container video",
        "#ytd-player video",
      ],
      vimeo: [
        "video",
        ".vp-video video",
        ".player video",
        ".vp-video-wrapper video",
      ],
      twitch: [
        "video",
        ".video-player video",
        ".player-video video",
        '[data-a-target="video-player"] video',
      ],
      netflix: [
        "video",
        ".VideoContainer video",
        ".watch-video video",
        ".nfp video",
      ],
      hulu: ["video", ".video-player video", ".player-container video"],
      disney: [
        "video",
        ".video-player video",
        ".btm-media-client-element video",
      ],
      amazon: ["video", ".webPlayerContainer video", ".webPlayerElement video"],
      facebook: ["video", "[data-video-id] video", ".videoStage video"],
      twitter: [
        "video",
        '[data-testid="videoPlayer"] video',
        ".PlayableMedia-player video",
      ],
      generic: [
        "video",
        "[data-video]",
        ".video video",
        ".player video",
        ".video-player video",
        ".media-player video",
        ".jwplayer video",
        ".flowplayer video",
        ".video-js video",
      ],
    };

    const platformSelectors = selectors[this.platform] || selectors.generic;
    let videos = [];

    platformSelectors.forEach((selector) => {
      try {
        const elements = document.querySelectorAll(selector);
        videos.push(...elements);
      } catch (error) {
        console.warn(`PiP Master: Selector "${selector}" failed:`, error);
      }
    });

    return videos.filter((video) => video && video.tagName === "VIDEO");
  }

  scanShadowDOMVideos() {
    const videos = [];

    // Find all elements with shadow roots
    const elementsWithShadow = document.querySelectorAll("*");

    elementsWithShadow.forEach((element) => {
      try {
        if (element.shadowRoot) {
          const shadowVideos = element.shadowRoot.querySelectorAll("video");
          videos.push(...shadowVideos);
        }
      } catch (error) {
        // Shadow root access might be restricted
        console.debug(
          "PiP Master: Shadow DOM access restricted for element:",
          element
        );
      }
    });

    return videos;
  }

  scanIframeVideos() {
    const videos = [];

    // Find all iframes
    const iframes = document.querySelectorAll("iframe");

    iframes.forEach((iframe) => {
      try {
        // Only access same-origin iframes
        if (iframe.contentDocument) {
          const iframeVideos = iframe.contentDocument.querySelectorAll("video");
          videos.push(...iframeVideos);
        }
      } catch (error) {
        // Cross-origin iframe access will fail - this is expected
        console.debug(
          "PiP Master: Cross-origin iframe access blocked:",
          iframe.src
        );
      }
    });

    return videos;
  }

  scanCustomVideoPlayers() {
    const videos = [];

    // Common custom video player containers
    const customPlayerSelectors = [
      ".jwplayer",
      ".flowplayer",
      ".video-js",
      ".plyr",
      ".mediaelement",
      ".mejs-container",
      ".vjs-tech",
      "[data-player]",
      "[data-video-player]",
      ".custom-video-player",
    ];

    customPlayerSelectors.forEach((selector) => {
      try {
        const containers = document.querySelectorAll(selector);
        containers.forEach((container) => {
          const containerVideos = container.querySelectorAll("video");
          videos.push(...containerVideos);
        });
      } catch (error) {
        console.warn(
          `PiP Master: Custom player selector "${selector}" failed:`,
          error
        );
      }
    });

    return videos;
  }

  setupUniversalMutationObserver() {
    console.log("PiP Master: Setting up mutation observer...");

    // Enhanced mutation observer for dynamic content
    if (this.videoObserver) {
      this.videoObserver.disconnect();
    }

    this.videoObserver = new MutationObserver((mutations) => {
      let shouldScan = false;
      let foundDirectVideos = false;

      mutations.forEach((mutation) => {
        // Check for added nodes
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Direct video elements
            if (node.tagName === "VIDEO") {
              console.log("PiP Master: Direct video element added to DOM");
              this.handleVideoFound(node);
              foundDirectVideos = true;
            }
            // Containers that might have videos (simplified check)
            else if (node.querySelector) {
              const videos = node.querySelectorAll("video");
              if (videos.length > 0) {
                console.log(
                  `PiP Master: ${videos.length} videos found in added container`
                );
                videos.forEach((video) => this.handleVideoFound(video));
                foundDirectVideos = true;
              }
            }
          }
        });

        // Only check attributes for video elements to reduce overhead
        if (
          mutation.type === "attributes" &&
          mutation.target.tagName === "VIDEO"
        ) {
          console.log("PiP Master: Video element attributes changed");
          shouldScan = true;
        }
      });

      // Perform full scan if needed (debounced) - only if no direct videos found
      if (shouldScan && !foundDirectVideos) {
        clearTimeout(this.scanTimeout);
        this.scanTimeout = setTimeout(() => {
          console.log(
            "PiP Master: Performing delayed scan due to attribute changes"
          );
          this.performUniversalVideoScan();
        }, 1000); // Increased delay to reduce frequency
      }
    });

    // Simplified observer configuration to reduce event listener violations
    this.videoObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false, // Disable attribute observation to reduce violations
    });

    console.log("PiP Master: Mutation observer set up successfully");
  }

  setupPlatformSpecificDetection() {
    // Platform-specific initialization
    console.log(
      `PiP Master: Setting up platform-specific detection for: ${this.platform}`
    );

    switch (this.platform) {
      case "youtube":
        console.log("PiP Master: Initializing YouTube-specific detection...");
        this.setupYouTubeDetection();
        // Force immediate YouTube scan with multiple attempts
        setTimeout(() => {
          console.log("PiP Master: YouTube immediate scan (500ms)...");
          this.performUniversalVideoScan();
        }, 500);
        setTimeout(() => {
          console.log("PiP Master: YouTube immediate scan (2s)...");
          this.performUniversalVideoScan();
        }, 2000);
        break;
      case "vimeo":
        this.setupVimeoDetection();
        break;
      case "twitch":
        this.setupTwitchDetection();
        break;
      case "netflix":
        this.setupNetflixDetection();
        break;
      default:
        this.setupGenericDetection();
    }
  }

  setupNavigationMonitoring() {
    // Monitor for SPA navigation changes
    let lastUrl = window.location.href;

    const checkForNavigation = () => {
      const currentUrl = window.location.href;
      if (currentUrl !== lastUrl) {
        console.log(
          "PiP Master: Navigation detected, rescanning for videos..."
        );
        lastUrl = currentUrl;

        // Clear existing videos and rescan
        this.videos.clear();
        this.overlays.forEach((overlay) => {
          if (overlay.parentElement) {
            overlay.parentElement.removeChild(overlay);
          }
        });
        this.overlays.clear();

        // Rescan after navigation
        setTimeout(() => {
          this.performUniversalVideoScan();
        }, 1000);
      }
    };

    // Monitor for URL changes (SPA navigation)
    setInterval(checkForNavigation, 1000);

    // Listen for popstate events
    window.addEventListener("popstate", checkForNavigation);

    // Override pushState and replaceState for SPA detection
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function (...args) {
      originalPushState.apply(history, args);
      setTimeout(checkForNavigation, 100);
    };

    history.replaceState = function (...args) {
      originalReplaceState.apply(history, args);
      setTimeout(checkForNavigation, 100);
    };
  }

  setupYouTubeDetection() {
    console.log("PiP Master: Setting up simplified YouTube detection");

    // Simplified YouTube video detection - focus on reliability
    const findYouTubeVideo = () => {
      console.log("PiP Master: Scanning for YouTube videos...");

      // Primary selectors in order of reliability
      const selectors = [
        "#movie_player video",           // Main YouTube player
        ".html5-video-player video",     // HTML5 player container
        "video.video-stream",            // Direct video stream
        "#ytd-player video",             // YouTube player component
        "video"                          // Fallback to any video
      ];

      let foundVideo = null;

      for (const selector of selectors) {
        try {
          const videos = document.querySelectorAll(selector);
          for (const video of videos) {
            if (this.isValidYouTubeVideo(video) && !this.videos.has(video)) {
              console.log(`PiP Master: Found valid YouTube video via "${selector}"`);
              foundVideo = video;
              break;
            }
          }
          if (foundVideo) break;
        } catch (error) {
          console.warn(`PiP Master: Selector "${selector}" failed:`, error.message);
        }
      }

      if (foundVideo) {
        this.handleVideoFound(foundVideo);
        return true;
      }

      console.log("PiP Master: No valid YouTube videos found");
      return false;
    };

    // Helper to validate YouTube videos
    this.isValidYouTubeVideo = (video) => {
      if (!video || video.tagName !== "VIDEO") return false;

      // Skip if already processed
      if (this.videos.has(video)) return false;

      // Skip obvious ads
      if (this.isYouTubeAd(video)) return false;

      // Must have some dimensions or be loading
      const rect = video.getBoundingClientRect();
      if (rect.width === 0 && rect.height === 0 && video.videoWidth === 0) {
        return false;
      }

      return true;
    };

    // Simplified ad detection for YouTube
    this.isYouTubeAd = (video) => {
      const moviePlayer = document.querySelector("#movie_player");
      if (moviePlayer && moviePlayer.classList.contains("ad-showing")) {
        return true;
      }

      const adContainer = video.closest(".ad-showing, .video-ads");
      return !!adContainer;
    };

    // Initial scan
    findYouTubeVideo();

    // Set up periodic scanning (less aggressive)
    const scanInterval = setInterval(() => {
      if (!findYouTubeVideo() && this.videos.size === 0) {
        console.log("PiP Master: No videos found, continuing to scan...");
      }
    }, 3000);

    // Handle YouTube navigation
    const handleYouTubeNavigation = () => {
      console.log("PiP Master: YouTube navigation detected, clearing state");

      // Clear existing state
      this.clearAllVideos();

      // Scan for new videos after navigation
      setTimeout(() => findYouTubeVideo(), 1000);
      setTimeout(() => findYouTubeVideo(), 3000);
    };

    // Listen for YouTube navigation events
    document.addEventListener("yt-navigate-finish", handleYouTubeNavigation);
    document.addEventListener("yt-player-updated", () => {
      setTimeout(() => findYouTubeVideo(), 500);
    });

    // Simple mutation observer for new video elements
    const observer = new MutationObserver((mutations) => {
      let hasNewVideo = false;

      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.tagName === "VIDEO" || node.querySelector?.("video")) {
              hasNewVideo = true;
            }
          }
        });
      });

      if (hasNewVideo) {
        setTimeout(() => findYouTubeVideo(), 500);
      }
    });

    // Observe the main YouTube container
    const ytdApp = document.querySelector("ytd-app") || document.body;
    observer.observe(ytdApp, {
      childList: true,
      subtree: true
    });

    console.log("PiP Master: YouTube detection setup complete");
  }

  setupVimeoDetection() {
    console.log("PiP Master: Setting up Vimeo-specific detection");

    // Vimeo player detection
    const checkVimeoVideo = () => {
      const vimeoVideos = document.querySelectorAll(
        ".vp-video video, .player video"
      );
      vimeoVideos.forEach((video) => {
        if (!this.videos.has(video)) {
          this.handleVideoFound(video);
        }
      });
    };

    setInterval(checkVimeoVideo, 1000);
  }

  setupTwitchDetection() {
    console.log("PiP Master: Setting up Twitch-specific detection");

    // Twitch player detection
    const checkTwitchVideo = () => {
      const twitchVideos = document.querySelectorAll(
        '[data-a-target="video-player"] video, .video-player video'
      );
      twitchVideos.forEach((video) => {
        if (!this.videos.has(video)) {
          this.handleVideoFound(video);
        }
      });
    };

    setInterval(checkTwitchVideo, 1000);
  }

  setupNetflixDetection() {
    console.log("PiP Master: Setting up Netflix-specific detection");

    // Netflix player detection (may be limited due to DRM)
    const checkNetflixVideo = () => {
      const netflixVideos = document.querySelectorAll(
        ".VideoContainer video, .nfp video"
      );
      netflixVideos.forEach((video) => {
        if (!this.videos.has(video)) {
          this.handleVideoFound(video);
        }
      });
    };

    setInterval(checkNetflixVideo, 1000);
  }

  setupGenericDetection() {
    console.log("PiP Master: Setting up generic video detection");

    // Generic detection for unknown platforms
    const checkGenericVideos = () => {
      const videos = document.querySelectorAll("video");
      videos.forEach((video) => {
        if (!this.videos.has(video)) {
          this.handleVideoFound(video);
        }
      });
    };

    setInterval(checkGenericVideos, 2000);
  }

  clearAllVideos() {
    console.log("PiP Master: Clearing all tracked videos and overlays");

    // Remove all overlays from DOM
    this.overlays.forEach((overlay, video) => {
      try {
        if (overlay && overlay.parentElement) {
          overlay.parentElement.removeChild(overlay);
        }
      } catch (error) {
        console.warn("PiP Master: Error removing overlay:", error);
      }
    });

    // Clear all tracking
    this.videos.clear();
    this.overlays.clear();
    this.activeVideo = null;

    console.log("PiP Master: All videos and overlays cleared");
  }

  handleVideoFound(video) {
    console.log(
      "PiP Master: handleVideoFound called for video:",
      video.src || video.currentSrc || "no src"
    );

    if (this.videos.has(video)) {
      console.log("PiP Master: Video already tracked, skipping");
      return;
    }

    if (!this.settings.enabled) {
      console.log("PiP Master: Extension disabled, skipping video");
      return;
    }

    // Check if video is suitable for PiP with detailed logging
    const suitabilityResult = this.isVideoSuitableForPiP(video);
    console.log(
      "PiP Master: Video suitability check result:",
      suitabilityResult
    );

    if (!suitabilityResult) {
      console.log("PiP Master: Video not suitable for PiP, skipping");
      return;
    }

    console.log("PiP Master: Adding video to tracking...");
    this.videos.add(video);

    console.log("PiP Master: Creating overlay for video...");
    this.createOverlay(video);

    // Update diagnostics
    if (window.pipMasterDiagnostics) {
      window.pipMasterDiagnostics.videosDetected = this.videos.size;
    }

    console.log(
      "PiP Master: Video successfully added to tracking. Total videos:",
      this.videos.size
    );

    // Add event listeners to video
    video.addEventListener("play", () => {
      console.log("PiP Master: Video play event");
      this.updateOverlayVisibility(video);
      this.handleVideoPlay(video);
    });
    video.addEventListener("pause", () => {
      console.log("PiP Master: Video pause event");
      this.updateOverlayVisibility(video);
    });
    video.addEventListener("loadedmetadata", () => {
      console.log("PiP Master: Video loadedmetadata event");
      this.updateOverlayVisibility(video);
    });
    video.addEventListener("timeupdate", () =>
      this.handleVideoTimeUpdate(video)
    );

    // Track user interactions to determine if auto-activation is appropriate
    video.addEventListener("click", () => {
      this.lastInteractionTime = Date.now();
    });
  }

  isVideoSuitableForPiP(video) {
    try {
      const videoId = video.src || video.currentSrc || "unknown";
      console.log("PiP Master: Checking video suitability for:", videoId);

      // Check if video element is valid
      if (!video || video.tagName !== "VIDEO") {
        console.log("PiP Master: Invalid video element");
        return false;
      }

      // Check if PiP is explicitly disabled
      if (video.disablePictureInPicture) {
        console.log("PiP Master: PiP explicitly disabled on video");
        return false;
      }

      // Check if video is visible (basic check)
      const style = window.getComputedStyle(video);
      if (style.display === "none") {
        console.log("PiP Master: Video is display:none");
        return false;
      }

      // More lenient visibility check
      if (style.visibility === "hidden" && style.opacity === "0") {
        console.log("PiP Master: Video is completely hidden");
        return false;
      }

      // Google-style: Very basic size check (extremely lenient)
      const rect = video.getBoundingClientRect();
      if (rect.width === 0 && rect.height === 0) {
        console.log("PiP Master: [Google-style] Video has no dimensions");
        return false;
      }

      // Google doesn't do complex dimension checks - let the browser handle it
      // Skip all the complex metadata and dimension checks

      // Google doesn't do complex ad detection - only obvious ones
      if (this.isObviousAd(video)) {
        console.log("PiP Master: [Google-style] Video is obviously an ad");
        return false;
      }

      console.log("PiP Master: Video passed suitability check:", {
        platform: this.platform,
        dimensions: `${video.videoWidth || "unknown"}x${
          video.videoHeight || "unknown"
        }`,
        rendered: `${Math.round(rect.width)}x${Math.round(rect.height)}`,
        duration: video.duration || "unknown",
        readyState: video.readyState,
        src: videoId.substring(0, 50) + (videoId.length > 50 ? "..." : ""),
      });

      return true;
    } catch (error) {
      console.error("PiP Master: Error checking video suitability:", error);
      // Be more permissive on errors - allow the video
      console.log("PiP Master: Allowing video despite suitability check error");
      return true;
    }
  }

  platformSpecificVideoCheck(video) {
    switch (this.platform) {
      case "youtube":
        // Less aggressive YouTube ad detection (fixed for false positives)
        console.log("PiP Master: YouTube platform check - checking for ads");

        // Only check for very obvious ad indicators
        const obviousAdSelectors = [".ad-showing", ".video-ads"];

        for (const selector of obviousAdSelectors) {
          const adContainer = video.closest(selector);
          if (adContainer) {
            console.log(
              `PiP Master: Skipping obvious YouTube ad (${selector})`
            );
            return false;
          }
        }

        // Check for YouTube Premium ad indicators (most reliable)
        const ytPlayer = document.querySelector("#movie_player");
        if (ytPlayer && ytPlayer.classList.contains("ad-showing")) {
          console.log("PiP Master: Skipping YouTube ad (player ad-showing)");
          return false;
        }

        // Skip the aggressive container checks that cause false positives
        console.log("PiP Master: YouTube video passed platform check");
        break;

      case "twitch":
        // Skip Twitch ads
        const twitchAdContainer = video.closest('[data-a-target="video-ad"]');
        if (twitchAdContainer) {
          console.log("PiP Master: Skipping Twitch ad video");
          return false;
        }
        break;

      case "netflix":
        // Netflix might have DRM restrictions
        if (video.src && video.src.includes("blob:")) {
          console.log("PiP Master: Netflix blob video detected (may have DRM)");
        }
        break;
    }
    return true;
  }

  isLikelyVideoAd(video) {
    // Less aggressive ad detection (fixed for YouTube false positives)
    console.log("PiP Master: Checking if video is likely an ad");

    // Only check for very obvious ad indicators
    const obviousAdIndicators = [
      "advertisement",
      "preroll",
      "midroll",
      "postroll",
    ];

    // Check video src (only obvious indicators)
    const src = (video.src || video.currentSrc || "").toLowerCase();
    if (obviousAdIndicators.some((indicator) => src.includes(indicator))) {
      console.log("PiP Master: Video detected as ad by src");
      return true;
    }

    // Check only immediate parent for obvious ad indicators
    const parent = video.parentElement;
    if (parent) {
      const className = (parent.className || "").toLowerCase();
      const id = (parent.id || "").toLowerCase();

      if (
        obviousAdIndicators.some(
          (indicator) => className.includes(indicator) || id.includes(indicator)
        )
      ) {
        console.log("PiP Master: Video detected as ad by parent container");
        return true;
      }
    }

    console.log("PiP Master: Video passed ad detection");
    return false;
  }

  isObviousAd(video) {
    // Google-style: Only check for very obvious ads
    console.log("PiP Master: [Google-style] Checking for obvious ads");

    // Check for YouTube's ad-showing class (most reliable)
    const moviePlayer = document.querySelector("#movie_player");
    if (moviePlayer && moviePlayer.classList.contains("ad-showing")) {
      console.log(
        "PiP Master: [Google-style] YouTube ad detected (ad-showing)"
      );
      return true;
    }

    // Check for obvious ad containers
    const adContainer = video.closest(".ad-showing, .video-ads");
    if (adContainer) {
      console.log("PiP Master: [Google-style] Video in obvious ad container");
      return true;
    }

    console.log("PiP Master: [Google-style] No obvious ad indicators found");
    return false;
  }

  createOverlay(video) {
    console.log("PiP Master: Creating simplified overlay for video...");

    if (!this.settings.showOverlay) {
      console.log("PiP Master: Overlay disabled in settings");
      return;
    }

    // Create simplified overlay
    const overlay = document.createElement("div");
    overlay.className = "pip-master-overlay pip-master-visible";
    overlay.setAttribute("data-platform", this.platform);

    // Simplified HTML structure
    overlay.innerHTML = `
      <div class="pip-master-container" title="Click for Picture-in-Picture (Alt+P)">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 7h-8v6h8V7zm2-4H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h18v14z"/>
        </svg>
      </div>
    `;

    // Simple click handler
    overlay.addEventListener("click", (e) => {
      e.stopPropagation();
      e.preventDefault();
      console.log("PiP Master: Overlay clicked, toggling PiP...");
      this.togglePiP(video);
    });

    // Position and insert overlay
    this.insertSimplifiedOverlay(overlay, video);
    this.overlays.set(video, overlay);

    console.log("PiP Master: Simplified overlay created and positioned");
  }

  setupOverlayInteractions(overlay, video) {
    const container = overlay.querySelector(".pip-master-container");
    const indicator = overlay.querySelector(".pip-master-indicator");
    const tooltip = overlay.querySelector(".pip-master-tooltip");

    // Click anywhere on overlay to activate PiP
    overlay.addEventListener("click", (e) => {
      e.stopPropagation();
      e.preventDefault();
      this.togglePiP(video);
    });

    // Enhanced hover effects with tooltip
    let hoverTimeout;

    overlay.addEventListener("mouseenter", () => {
      clearTimeout(hoverTimeout);
      overlay.classList.add("pip-master-hover");

      // Show tooltip after short delay
      hoverTimeout = setTimeout(() => {
        tooltip.style.opacity = "1";
        tooltip.style.transform = "translateY(0)";
      }, 300);
    });

    overlay.addEventListener("mouseleave", () => {
      clearTimeout(hoverTimeout);
      overlay.classList.remove("pip-master-hover");
      tooltip.style.opacity = "0";
      tooltip.style.transform = "translateY(-10px)";
    });

    // Double-click video for quick PiP (alternative activation)
    video.addEventListener("dblclick", (e) => {
      // Only if not already in a player control area
      if (!e.target.closest(".video-controls, .player-controls, .controls")) {
        e.stopPropagation();
        e.preventDefault();
        this.togglePiP(video);
      }
    });

    // Right-click context menu enhancement
    video.addEventListener("contextmenu", (e) => {
      // Add visual hint that PiP is available
      overlay.classList.add("pip-master-context-hint");
      setTimeout(() => {
        overlay.classList.remove("pip-master-context-hint");
      }, 2000);
    });
  }

  insertSimplifiedOverlay(overlay, video) {
    console.log("PiP Master: Inserting simplified overlay...");

    // Find the best container for YouTube
    let container = null;

    if (this.platform === "youtube") {
      // Try YouTube-specific containers first
      container = document.querySelector("#movie_player") ||
                 document.querySelector(".html5-video-player") ||
                 video.closest(".html5-video-player") ||
                 video.parentElement;
    } else {
      container = video.parentElement;
    }

    if (!container) {
      console.warn("PiP Master: No suitable container found, using video parent");
      container = video.parentElement || document.body;
    }

    // Ensure container can position the overlay
    const containerStyle = window.getComputedStyle(container);
    if (containerStyle.position === "static") {
      container.style.position = "relative";
    }

    // Set overlay positioning
    overlay.style.position = "absolute";
    overlay.style.top = "16px";
    overlay.style.right = "16px";
    overlay.style.zIndex = "10000";
    overlay.style.pointerEvents = "auto";

    // Insert overlay
    container.appendChild(overlay);

    console.log("PiP Master: Overlay inserted into container:", container.tagName, container.className);
  }

  insertOverlay(overlay, video) {
    // Legacy method - redirect to simplified version
    this.insertSimplifiedOverlay(overlay, video);
  }

  positionOverlay(overlay, video) {
    const position = this.settings.overlayPosition || "top-right";

    overlay.style.position = "absolute";
    overlay.style.zIndex = "10000";
    overlay.style.opacity = this.settings.opacity || "0.7";
    overlay.style.transition = "opacity 0.2s ease";
    overlay.style.pointerEvents = "auto";

    // Platform-specific positioning adjustments
    let topOffset = 10;
    let rightOffset = 10;
    let bottomOffset = 10;
    let leftOffset = 10;

    if (this.platform === "youtube") {
      // Adjust for YouTube's UI elements
      topOffset = 16; // Avoid YouTube's top controls
      rightOffset = 16; // Avoid YouTube's settings button
      bottomOffset = 50; // Avoid YouTube's bottom controls

      // Check if we're in theater mode or fullscreen
      const ytPlayer = document.querySelector("#movie_player");
      if (ytPlayer) {
        if (ytPlayer.classList.contains("ytp-fullscreen")) {
          topOffset = 24;
          rightOffset = 24;
          bottomOffset = 60;
        } else if (ytPlayer.classList.contains("ytp-large-width-mode")) {
          topOffset = 20;
          rightOffset = 20;
          bottomOffset = 55;
        }
      }
    }

    switch (position) {
      case "top-left":
        overlay.style.top = `${topOffset}px`;
        overlay.style.left = `${leftOffset}px`;
        overlay.style.bottom = "auto";
        overlay.style.right = "auto";
        break;
      case "top-right":
        overlay.style.top = `${topOffset}px`;
        overlay.style.right = `${rightOffset}px`;
        overlay.style.bottom = "auto";
        overlay.style.left = "auto";
        break;
      case "bottom-left":
        overlay.style.bottom = `${bottomOffset}px`;
        overlay.style.left = `${leftOffset}px`;
        overlay.style.top = "auto";
        overlay.style.right = "auto";
        break;
      case "bottom-right":
        overlay.style.bottom = `${bottomOffset}px`;
        overlay.style.right = `${rightOffset}px`;
        overlay.style.top = "auto";
        overlay.style.left = "auto";
        break;
    }

    console.log(
      `PiP Master: Overlay positioned at ${position} with ${this.platform} adjustments`
    );
  }

  updateOverlayVisibility(video) {
    const overlay = this.overlays.get(video);
    if (!overlay) return;

    // Show overlay when video has loaded metadata (more permissive)
    // Allow PiP on paused videos too
    const shouldShow = video.readyState >= 1; // HAVE_METADATA or higher

    if (shouldShow) {
      overlay.style.display = "block";
      // Add smooth entrance animation
      setTimeout(() => {
        overlay.classList.add("pip-master-visible");
      }, 100);
    } else {
      overlay.classList.remove("pip-master-visible");
      setTimeout(() => {
        overlay.style.display = "none";
      }, 300);
    }

    console.log("PiP Master: Overlay visibility updated:", {
      video: video.src || video.currentSrc,
      readyState: video.readyState,
      paused: video.paused,
      shouldShow: shouldShow,
    });
  }

  async togglePiP(video) {
    console.log("PiP Master: Attempting to toggle PiP (simplified)");

    try {
      // If no video provided, find the best one
      if (!video) {
        video = this.findBestVideo();
        if (!video) {
          throw new Error("No suitable video found for Picture-in-Picture");
        }
      }

      const overlay = this.overlays.get(video);
      console.log("PiP Master: Using video:", {
        src: (video.src || video.currentSrc || "no src").substring(0, 50),
        readyState: video.readyState,
        dimensions: `${video.videoWidth}x${video.videoHeight}`,
        paused: video.paused
      });

      // Visual feedback
      if (overlay) {
        overlay.classList.add("pip-master-activating");
      }

      // Toggle PiP state
      if (document.pictureInPictureElement) {
        console.log("PiP Master: Exiting PiP...");
        await document.exitPictureInPicture();
      } else {
        console.log("PiP Master: Requesting PiP...");

        // Simple validation
        if (video.disablePictureInPicture) {
          throw new Error("Picture-in-Picture is disabled on this video");
        }

        await video.requestPictureInPicture();
        this.activeVideo = video;
        console.log("PiP Master: PiP activated successfully!");
        this.showSuccess("Picture-in-Picture activated!");
      }

      // Remove visual feedback
      if (overlay) {
        overlay.classList.remove("pip-master-activating");
      }

    } catch (error) {
      console.error("PiP Master: PiP toggle failed:", error.message);

      // Remove activating state and show error
      const overlay = this.overlays.get(video);
      if (overlay) {
        overlay.classList.remove("pip-master-activating");
        overlay.classList.add("pip-master-error");
        setTimeout(() => overlay.classList.remove("pip-master-error"), 1000);
      }

      // Show user-friendly error
      this.showError(this.getSimpleErrorMessage(error));
    }
  }

  findBestVideo() {
    // Find the best video for PiP
    const videos = Array.from(this.videos);
    if (videos.length === 0) return null;

    // Prefer playing videos
    const playingVideos = videos.filter(v => !v.paused);
    if (playingVideos.length > 0) {
      return playingVideos[0];
    }

    // Otherwise return the first video
    return videos[0];
  }

  getSimpleErrorMessage(error) {
    if (error.message.includes("disabled")) {
      return "Picture-in-Picture is disabled for this video";
    } else if (error.message.includes("policy") || error.name === "NotAllowedError") {
      return "Picture-in-Picture not allowed by website policy";
    } else if (error.name === "InvalidStateError") {
      return "Video not ready for Picture-in-Picture";
    } else if (error.name === "NotSupportedError") {
      return "Picture-in-Picture not supported";
    } else {
      return "Picture-in-Picture not available";
    }
  }

  async performPiPPreflightChecks(video) {
    // Check if PiP is supported
    if (!("pictureInPictureEnabled" in document)) {
      throw new Error("Picture-in-Picture API not supported");
    }

    // Check if PiP is enabled
    if (!document.pictureInPictureEnabled) {
      throw new Error("Picture-in-Picture is disabled by document policy");
    }

    // Check if video element supports PiP
    if (video.disablePictureInPicture) {
      throw new Error("Picture-in-Picture is disabled on this video element");
    }

    // Check video readiness
    if (video.readyState < 1) {
      // Wait for metadata to load
      await this.waitForVideoMetadata(video);
    }

    // Check if video has valid dimensions
    if (video.videoWidth === 0 || video.videoHeight === 0) {
      throw new Error("Video has no dimensions (audio-only content?)");
    }

    // Platform-specific checks
    await this.performPlatformSpecificChecks(video);
  }

  async waitForVideoMetadata(video, timeout = 5000) {
    return new Promise((resolve, reject) => {
      if (video.readyState >= 1) {
        resolve();
        return;
      }

      const timeoutId = setTimeout(() => {
        video.removeEventListener("loadedmetadata", onMetadata);
        reject(new Error("Timeout waiting for video metadata"));
      }, timeout);

      const onMetadata = () => {
        clearTimeout(timeoutId);
        video.removeEventListener("loadedmetadata", onMetadata);
        resolve();
      };

      video.addEventListener("loadedmetadata", onMetadata);
    });
  }

  async performPlatformSpecificChecks(video) {
    switch (this.platform) {
      case "netflix":
        // Netflix often uses DRM-protected content
        if (video.src && video.src.startsWith("blob:")) {
          console.warn(
            "PiP Master: Netflix DRM content detected - PiP may not work"
          );
        }
        break;

      case "youtube":
        // Check if it's a YouTube ad
        if (video.closest(".ad-showing")) {
          throw new Error("Cannot use Picture-in-Picture on advertisements");
        }
        break;

      case "twitch":
        // Check for Twitch-specific restrictions
        if (video.closest('[data-a-target="video-ad"]')) {
          throw new Error("Cannot use Picture-in-Picture on advertisements");
        }
        break;
    }
  }

  getEnhancedErrorMessage(error, video) {
    const platform = this.platform;

    switch (error.name) {
      case "InvalidStateError":
        return `Video is not ready for Picture-in-Picture. Try waiting for the video to load completely.`;

      case "NotSupportedError":
        return `Picture-in-Picture is not supported by your browser. Please update to a newer version.`;

      case "NotAllowedError":
        if (platform === "netflix") {
          return `Netflix blocks Picture-in-Picture due to content protection. This is a platform restriction.`;
        } else if (platform === "youtube") {
          return `YouTube may be blocking Picture-in-Picture. Try refreshing the page or using a different video.`;
        }
        return `Picture-in-Picture is blocked by ${platform} policy or browser settings.`;

      case "SecurityError":
        return `Picture-in-Picture blocked due to security restrictions. This may be due to cross-origin content.`;

      default:
        if (error.message.includes("disabled")) {
          return `Picture-in-Picture is disabled on this video. The website has specifically blocked this feature.`;
        } else if (error.message.includes("policy")) {
          return `Picture-in-Picture is blocked by site policy. ${platform} may not allow this feature.`;
        } else if (error.message.includes("metadata")) {
          return `Video is still loading. Please wait a moment and try again.`;
        } else if (error.message.includes("dimensions")) {
          return `This appears to be audio-only content. Picture-in-Picture requires video content.`;
        }

        return `Picture-in-Picture not available: ${error.message}`;
    }
  }

  getGoogleStyleErrorMessage(error, video) {
    // Google-style: Simple, user-friendly error messages
    console.log(
      "PiP Master: [Google-style] Generating error message for:",
      error.name || error.message
    );

    if (error.message.includes("No suitable video found")) {
      return "No suitable video found for Picture-in-Picture";
    }

    switch (error.name) {
      case "NotAllowedError":
        return "Picture-in-Picture not allowed";
      case "InvalidStateError":
        return "Video not ready for Picture-in-Picture";
      case "NotSupportedError":
        return "Picture-in-Picture not supported";
      default:
        return "Picture-in-Picture not available";
    }
  }

  suggestAlternatives(error, video) {
    // Suggest alternatives based on the error and platform
    if (error.name === "NotAllowedError" && this.platform === "netflix") {
      console.log(
        "PiP Master: Suggestion - Try using browser's built-in PiP feature (right-click on video)"
      );
    } else if (error.message.includes("advertisement")) {
      console.log(
        "PiP Master: Suggestion - Wait for the ad to finish, then try again"
      );
    } else if (video.readyState < 1) {
      console.log("PiP Master: Suggestion - Wait for video to load completely");
    }
  }

  handlePiPEnter(event) {
    console.log("PiP Master: Entered PiP mode");
    this.pipWindow = event.target;

    // Notify background script
    this.sendMessage({
      type: "PIP_STATUS_CHANGED",
      data: { status: "entered", video: this.getVideoInfo(event.target) },
    });
  }

  handlePiPLeave(event) {
    console.log("PiP Master: Left PiP mode");
    this.pipWindow = null;
    this.activeVideo = null;

    // Notify background script
    this.sendMessage({
      type: "PIP_STATUS_CHANGED",
      data: { status: "left", video: this.getVideoInfo(event.target) },
    });
  }

  getVideoInfo(video) {
    return {
      src: video.src || video.currentSrc,
      duration: video.duration,
      currentTime: video.currentTime,
      title: document.title,
      url: window.location.href,
    };
  }

  handleMessage(message, sender, sendResponse) {
    switch (message.type) {
      case "KEYBOARD_COMMAND":
        this.handleKeyboardCommand(message.command);
        sendResponse({ success: true });
        break;

      case "SETTINGS_UPDATED":
        this.settings = message.data;
        this.updateAllOverlays();
        sendResponse({ success: true });
        break;

      case "GET_VIDEO_COUNT":
        sendResponse({
          success: true,
          data: { count: this.videos.size },
        });
        break;

      default:
        sendResponse({ success: false, error: "Unknown message type" });
    }
  }

  handleKeyboardCommand(command) {
    switch (command) {
      case "toggle-pip":
        this.handleTogglePiPCommand();
        break;
      case "increase-opacity":
        this.adjustOpacity(0.1);
        break;
      case "decrease-opacity":
        this.adjustOpacity(-0.1);
        break;
    }
  }

  handleTogglePiPCommand() {
    // Find the best video to use for PiP
    const targetVideo = this.findBestVideoForPiP();
    if (targetVideo) {
      this.togglePiP(targetVideo);
    } else {
      this.showError("No suitable video found for Picture-in-Picture");
    }
  }

  findBestVideoForPiP() {
    // If there's already a PiP video, use it
    if (this.activeVideo && this.videos.has(this.activeVideo)) {
      return this.activeVideo;
    }

    // Find the largest video (playing videos preferred, but not required)
    let bestVideo = null;
    let maxArea = 0;
    let bestPlayingVideo = null;
    let maxPlayingArea = 0;

    for (const video of this.videos) {
      const area = video.videoWidth * video.videoHeight;

      // Track largest overall video
      if (area > maxArea) {
        maxArea = area;
        bestVideo = video;
      }

      // Track largest playing video
      if (!video.paused && !video.ended && area > maxPlayingArea) {
        maxPlayingArea = area;
        bestPlayingVideo = video;
      }
    }

    // Prefer playing video, but fall back to any video
    const selectedVideo = bestPlayingVideo || bestVideo;

    console.log("PiP Master: Selected video for PiP:", {
      video: selectedVideo?.src || selectedVideo?.currentSrc,
      dimensions: selectedVideo
        ? selectedVideo.videoWidth + "x" + selectedVideo.videoHeight
        : "none",
      playing: selectedVideo ? !selectedVideo.paused : false,
      totalVideos: this.videos.size,
    });

    return selectedVideo;
  }

  adjustOpacity(delta) {
    if (!this.pipWindow) return;

    const newOpacity = Math.max(
      0.1,
      Math.min(1, (this.settings.opacity || 0.9) + delta)
    );
    this.settings.opacity = newOpacity;

    // Update settings
    this.sendMessage({
      type: "UPDATE_SETTINGS",
      data: { opacity: newOpacity },
    });
  }

  updateAllOverlays() {
    for (const [video, overlay] of this.overlays) {
      this.positionOverlay(overlay, video);
      this.updateOverlayVisibility(video);
    }
  }

  showError(message) {
    this.showNotification(message, "pip-master-error");
  }

  showSuccess(message) {
    this.showNotification(message, "pip-master-success");
  }

  showNotification(message, className) {
    // Remove any existing notifications
    const existingNotifications = document.querySelectorAll(
      ".pip-master-error, .pip-master-success"
    );
    existingNotifications.forEach((notification) => {
      if (notification.parentElement) {
        notification.parentElement.removeChild(notification);
      }
    });

    // Create new notification
    const notification = document.createElement("div");
    notification.className = className;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Auto-remove after delay
    const delay = className.includes("error") ? 4000 : 2500;
    setTimeout(() => {
      if (notification.parentElement) {
        notification.style.animation =
          "pip-master-slide-out 0.3s ease-in forwards";
        setTimeout(() => {
          if (notification.parentElement) {
            notification.parentElement.removeChild(notification);
          }
        }, 300);
      }
    }, delay);
  }

  handleVideoPlay(video) {
    // Check if auto-activation is enabled and appropriate
    if (this.settings.autoActivate && this.shouldAutoActivate(video)) {
      // Delay auto-activation to avoid interfering with user intent
      this.autoActivationTimer = setTimeout(() => {
        if (!document.pictureInPictureElement && video.currentTime > 5) {
          console.log("PiP Master: Auto-activating PiP for video");
          this.togglePiP(video);
        }
      }, 3000); // 3 second delay
    }
  }

  handleVideoTimeUpdate(video) {
    // Cancel auto-activation if user interacts with video recently
    if (
      this.autoActivationTimer &&
      Date.now() - this.lastInteractionTime < 5000
    ) {
      clearTimeout(this.autoActivationTimer);
      this.autoActivationTimer = null;
    }
  }

  shouldAutoActivate(video) {
    // Only auto-activate if:
    // 1. Video is playing
    // 2. Video is reasonably long (> 30 seconds)
    // 3. User hasn't interacted recently
    // 4. No PiP is currently active
    // 5. Video is the main/largest video on page

    if (video.paused || document.pictureInPictureElement) {
      return false;
    }

    if (video.duration && video.duration < 30) {
      return false; // Too short for auto-activation
    }

    if (Date.now() - this.lastInteractionTime < 10000) {
      return false; // Recent user interaction
    }

    // Check if this is the largest/main video
    const largestVideo = this.findBestVideoForPiP();
    return largestVideo === video;
  }

  async sendMessage(message) {
    return new Promise((resolve, reject) => {
      try {
        chrome.runtime.sendMessage(message, (response) => {
          if (chrome.runtime.lastError) {
            console.error("PiP Master: Runtime error:", chrome.runtime.lastError.message);
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }

          if (response && response.success === false) {
            console.error("PiP Master: Message failed:", response.error);
            reject(new Error(response.error || "Unknown error"));
            return;
          }

          resolve(response || {});
        });
      } catch (error) {
        console.error("PiP Master: Failed to send message:", error);
        reject(error);
      }
    });
  }
}

// Mark content script as loaded for diagnostic purposes
window.pipMasterContentLoaded = true;

// Add diagnostic information to window for debugging
window.pipMasterDiagnostics = {
  version: "1.0.1",
  loaded: true,
  timestamp: new Date().toISOString(),
  url: window.location.href,
  videosDetected: 0,
  pipSupported: "pictureInPictureEnabled" in document,
  pipEnabled: document.pictureInPictureEnabled,
};

// Add debug functions to window for manual testing
window.pipMasterDebug = {
  scanForVideos: () => {
    if (window.pipMasterInstance) {
      console.log("PiP Master: Manual video scan triggered");
      window.pipMasterInstance.performUniversalVideoScan();
    } else {
      console.log("PiP Master: Instance not available");
    }
  },
  getVideoCount: () => {
    if (window.pipMasterInstance) {
      return window.pipMasterInstance.videos.size;
    }
    return 0;
  },
  listVideos: () => {
    if (window.pipMasterInstance) {
      const videos = Array.from(window.pipMasterInstance.videos);
      console.log(
        "PiP Master: Tracked videos:",
        videos.map((v) => ({
          src: v.src || v.currentSrc || "no src",
          readyState: v.readyState,
          dimensions: `${v.videoWidth}x${v.videoHeight}`,
          paused: v.paused,
        }))
      );
      return videos;
    }
    return [];
  },
  testPiP: (videoIndex = 0) => {
    if (window.pipMasterInstance) {
      const videos = Array.from(window.pipMasterInstance.videos);
      if (videos[videoIndex]) {
        console.log("PiP Master: Testing PiP on video", videoIndex);
        window.pipMasterInstance.togglePiP(videos[videoIndex]);
      } else {
        console.log("PiP Master: Video index not found");
      }
    }
  },
};

// Initialize content script when DOM is ready
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", () => {
    console.log("PiP Master: DOM loaded, creating instance...");
    window.pipMasterInstance = new PiPMasterContent();
  });
} else {
  console.log("PiP Master: DOM already ready, creating instance...");
  window.pipMasterInstance = new PiPMasterContent();
}
