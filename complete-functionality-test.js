// Complete Functionality Test Script
// End-to-end verification of all PiP Master features and settings

console.log("🧪 Complete Functionality Test");
console.log("===============================");

// Complete Functionality Tester
class CompleteFunctionalityTest {
  constructor() {
    this.testResults = {
      coreExtension: {},
      settingsPanel: {},
      enhancedFeatures: {},
      userInteractions: {},
      integration: {},
      performance: {},
      overall: {}
    };
    this.runCompleteTest();
  }

  async runCompleteTest() {
    console.log("🚀 Starting complete functionality test...");
    
    await this.testCoreExtension();
    await this.testSettingsPanel();
    await this.testEnhancedFeatures();
    await this.testUserInteractions();
    await this.testIntegration();
    await this.testPerformance();
    
    this.generateCompleteReport();
  }

  async testCoreExtension() {
    console.log("\n1️⃣ Core Extension Test");
    console.log("======================");
    
    const tests = {
      extensionLoaded: !!window.pipMasterInstance,
      videoDetection: false,
      overlayCreation: false,
      pipFunctionality: false
    };
    
    // Test video detection
    if (window.pipMasterInstance) {
      try {
        const videosBefore = window.pipMasterInstance.videos ? window.pipMasterInstance.videos.size : 0;
        window.pipMasterInstance.performUniversalVideoScan();
        const videosAfter = window.pipMasterInstance.videos ? window.pipMasterInstance.videos.size : 0;
        
        tests.videoDetection = videosAfter > 0 || document.querySelectorAll('video').length === 0;
        console.log(`Video detection: ${tests.videoDetection ? '✅' : '❌'} (${videosAfter} videos tracked)`);
      } catch (error) {
        console.error("Video detection test failed:", error);
      }
    }
    
    // Test overlay creation
    const overlays = document.querySelectorAll('.pip-master-overlay');
    tests.overlayCreation = overlays.length > 0 || document.querySelectorAll('video').length === 0;
    console.log(`Overlay creation: ${tests.overlayCreation ? '✅' : '❌'} (${overlays.length} overlays)`);
    
    // Test PiP functionality (if video available)
    const testVideo = document.querySelector('video');
    if (testVideo && window.pipMasterInstance) {
      try {
        const suitable = window.pipMasterInstance.isVideoSuitableForPiP(testVideo);
        tests.pipFunctionality = suitable;
        console.log(`PiP functionality: ${tests.pipFunctionality ? '✅' : '❌'} (video ${suitable ? 'suitable' : 'not suitable'})`);
      } catch (error) {
        console.error("PiP functionality test failed:", error);
      }
    } else {
      tests.pipFunctionality = true; // No video to test
      console.log(`PiP functionality: ✅ (no video to test)`);
    }
    
    this.testResults.coreExtension = tests;
  }

  async testSettingsPanel() {
    console.log("\n2️⃣ Settings Panel Test");
    console.log("=======================");
    
    const tests = {
      panelExists: !!window.pipMasterInstance?.settingsPanel,
      panelCanOpen: false,
      panelCanClose: false,
      formElements: false,
      canSave: false,
      canLoad: false
    };
    
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    
    if (settingsPanel) {
      // Test panel opening
      try {
        settingsPanel.showPanel();
        tests.panelCanOpen = settingsPanel.isVisible;
        console.log(`Panel can open: ${tests.panelCanOpen ? '✅' : '❌'}`);
      } catch (error) {
        console.error("Panel open test failed:", error);
      }
      
      // Test panel closing
      try {
        settingsPanel.hidePanel();
        tests.panelCanClose = !settingsPanel.isVisible;
        console.log(`Panel can close: ${tests.panelCanClose ? '✅' : '❌'}`);
      } catch (error) {
        console.error("Panel close test failed:", error);
      }
      
      // Test form elements
      const requiredElements = [
        'auto-enable', 'overlay-theme', 'high-contrast', 'volume-control'
      ];
      const foundElements = requiredElements.filter(id => document.getElementById(id));
      tests.formElements = foundElements.length === requiredElements.length;
      console.log(`Form elements: ${tests.formElements ? '✅' : '❌'} (${foundElements.length}/${requiredElements.length})`);
      
      // Test saving
      try {
        settingsPanel.saveSettings();
        tests.canSave = true;
        console.log(`Can save: ✅`);
      } catch (error) {
        console.error("Save test failed:", error);
        console.log(`Can save: ❌`);
      }
      
      // Test loading
      try {
        const loaded = settingsPanel.loadSettings();
        tests.canLoad = !!loaded;
        console.log(`Can load: ${tests.canLoad ? '✅' : '❌'}`);
      } catch (error) {
        console.error("Load test failed:", error);
        console.log(`Can load: ❌`);
      }
    } else {
      console.log("❌ Settings panel not available");
    }
    
    this.testResults.settingsPanel = tests;
  }

  async testEnhancedFeatures() {
    console.log("\n3️⃣ Enhanced Features Test");
    console.log("=========================");
    
    const features = {
      smartAutoPiP: window.pipMasterInstance?.smartAutoPiP,
      themeManager: window.pipMasterInstance?.themeManager,
      advancedControls: window.pipMasterInstance?.advancedControls,
      accessibility: window.pipMasterInstance?.accessibility,
      performanceOptimizer: window.pipMasterInstance?.performanceOptimizer,
      sitePreferences: window.pipMasterInstance?.sitePreferences,
      timelineControl: window.pipMasterInstance?.timelineControl
    };
    
    const featureTests = {};
    
    Object.entries(features).forEach(([name, feature]) => {
      const available = !!feature;
      console.log(`${available ? '✅' : '❌'} ${name}: ${available ? 'Available' : 'Missing'}`);
      
      if (available) {
        // Test basic functionality
        try {
          let functional = true;
          
          switch (name) {
            case 'smartAutoPiP':
              functional = typeof feature.enable === 'function' && typeof feature.disable === 'function';
              break;
            case 'themeManager':
              functional = typeof feature.setTheme === 'function' && Array.isArray(feature.getAvailableThemes());
              break;
            case 'accessibility':
              functional = typeof feature.enableHighContrastMode === 'function';
              break;
            case 'performanceOptimizer':
              functional = typeof feature.getPerformanceStats === 'function';
              break;
            case 'sitePreferences':
              functional = typeof feature.getSitePreferences === 'function';
              break;
            case 'timelineControl':
              functional = typeof feature.show === 'function';
              break;
          }
          
          featureTests[name] = { available: true, functional: functional };
        } catch (error) {
          featureTests[name] = { available: true, functional: false, error: error.message };
        }
      } else {
        featureTests[name] = { available: false };
      }
    });
    
    this.testResults.enhancedFeatures = featureTests;
  }

  async testUserInteractions() {
    console.log("\n4️⃣ User Interactions Test");
    console.log("==========================");
    
    const tests = {
      keyboardShortcuts: false,
      mouseInteractions: false,
      settingsChanges: false
    };
    
    // Test keyboard shortcuts
    try {
      // Simulate Alt+P (toggle PiP)
      const keyEvent = new KeyboardEvent('keydown', {
        key: 'p',
        altKey: true,
        bubbles: true
      });
      document.dispatchEvent(keyEvent);
      tests.keyboardShortcuts = true;
      console.log(`Keyboard shortcuts: ✅ (events can be dispatched)`);
    } catch (error) {
      console.error("Keyboard shortcut test failed:", error);
      console.log(`Keyboard shortcuts: ❌`);
    }
    
    // Test mouse interactions on overlays
    const overlays = document.querySelectorAll('.pip-master-overlay');
    if (overlays.length > 0) {
      try {
        const overlay = overlays[0];
        const clickEvent = new MouseEvent('click', { bubbles: true });
        overlay.dispatchEvent(clickEvent);
        tests.mouseInteractions = true;
        console.log(`Mouse interactions: ✅ (overlay click events work)`);
      } catch (error) {
        console.error("Mouse interaction test failed:", error);
        console.log(`Mouse interactions: ❌`);
      }
    } else {
      tests.mouseInteractions = true; // No overlays to test
      console.log(`Mouse interactions: ✅ (no overlays to test)`);
    }
    
    // Test settings changes
    if (window.pipMasterInstance?.settingsPanel) {
      try {
        const settingsPanel = window.pipMasterInstance.settingsPanel;
        const originalTheme = settingsPanel.settings.overlayTheme;
        
        // Change theme
        settingsPanel.settings.overlayTheme = 'neon';
        settingsPanel.applySettings();
        
        // Check if theme manager updated
        const themeChanged = window.pipMasterInstance.themeManager?.currentTheme === 'neon';
        
        // Restore original
        settingsPanel.settings.overlayTheme = originalTheme;
        settingsPanel.applySettings();
        
        tests.settingsChanges = themeChanged;
        console.log(`Settings changes: ${tests.settingsChanges ? '✅' : '❌'}`);
      } catch (error) {
        console.error("Settings change test failed:", error);
        console.log(`Settings changes: ❌`);
      }
    }
    
    this.testResults.userInteractions = tests;
  }

  async testIntegration() {
    console.log("\n5️⃣ Integration Test");
    console.log("===================");
    
    const tests = {
      settingsToFeatures: false,
      featureToFeature: false,
      storageIntegration: false
    };
    
    // Test settings to features integration
    if (window.pipMasterInstance?.settingsPanel && window.pipMasterInstance?.themeManager) {
      try {
        const settingsPanel = window.pipMasterInstance.settingsPanel;
        const themeManager = window.pipMasterInstance.themeManager;
        
        const originalTheme = settingsPanel.settings.overlayTheme;
        settingsPanel.settings.overlayTheme = 'dark';
        settingsPanel.applySettings();
        
        tests.settingsToFeatures = themeManager.currentTheme === 'dark';
        
        // Restore
        settingsPanel.settings.overlayTheme = originalTheme;
        settingsPanel.applySettings();
        
        console.log(`Settings to features: ${tests.settingsToFeatures ? '✅' : '❌'}`);
      } catch (error) {
        console.error("Settings to features test failed:", error);
        console.log(`Settings to features: ❌`);
      }
    }
    
    // Test feature to feature integration
    if (window.pipMasterInstance?.sitePreferences && window.pipMasterInstance?.themeManager) {
      try {
        const sitePrefs = window.pipMasterInstance.sitePreferences;
        sitePrefs.setSitePreference('theme', 'minimal');
        
        const retrievedTheme = sitePrefs.getSitePreferences().theme;
        tests.featureToFeature = retrievedTheme === 'minimal';
        
        console.log(`Feature to feature: ${tests.featureToFeature ? '✅' : '❌'}`);
      } catch (error) {
        console.error("Feature to feature test failed:", error);
        console.log(`Feature to feature: ❌`);
      }
    }
    
    // Test storage integration
    try {
      const testKey = 'pipMaster_test_integration';
      const testValue = { test: true, timestamp: Date.now() };
      
      localStorage.setItem(testKey, JSON.stringify(testValue));
      const retrieved = JSON.parse(localStorage.getItem(testKey));
      localStorage.removeItem(testKey);
      
      tests.storageIntegration = retrieved && retrieved.test === true;
      console.log(`Storage integration: ${tests.storageIntegration ? '✅' : '❌'}`);
    } catch (error) {
      console.error("Storage integration test failed:", error);
      console.log(`Storage integration: ❌`);
    }
    
    this.testResults.integration = tests;
  }

  async testPerformance() {
    console.log("\n6️⃣ Performance Test");
    console.log("===================");
    
    const tests = {
      memoryUsage: false,
      responseTime: false,
      errorRate: false
    };
    
    // Test memory usage
    if ('memory' in performance) {
      try {
        const memory = performance.memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        const limitMB = memory.jsHeapSizeLimit / 1024 / 1024;
        
        tests.memoryUsage = usedMB < limitMB * 0.5; // Less than 50% of limit
        console.log(`Memory usage: ${tests.memoryUsage ? '✅' : '⚠️'} (${Math.round(usedMB)}MB / ${Math.round(limitMB)}MB)`);
      } catch (error) {
        console.error("Memory test failed:", error);
      }
    } else {
      tests.memoryUsage = true; // Can't test, assume OK
      console.log(`Memory usage: ✅ (not measurable)`);
    }
    
    // Test response time
    try {
      const startTime = performance.now();
      
      if (window.pipMasterInstance?.performUniversalVideoScan) {
        window.pipMasterInstance.performUniversalVideoScan();
      }
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      tests.responseTime = responseTime < 100; // Less than 100ms
      console.log(`Response time: ${tests.responseTime ? '✅' : '⚠️'} (${Math.round(responseTime)}ms)`);
    } catch (error) {
      console.error("Response time test failed:", error);
    }
    
    // Test error rate (no errors during tests)
    tests.errorRate = this.testResults.errors ? this.testResults.errors.length === 0 : true;
    console.log(`Error rate: ${tests.errorRate ? '✅' : '❌'}`);
    
    this.testResults.performance = tests;
  }

  generateCompleteReport() {
    console.log("\n📊 COMPLETE FUNCTIONALITY REPORT");
    console.log("=================================");
    
    const { coreExtension, settingsPanel, enhancedFeatures, userInteractions, integration, performance } = this.testResults;
    
    // Calculate scores for each category
    const scores = {
      coreExtension: this.calculateScore(coreExtension),
      settingsPanel: this.calculateScore(settingsPanel),
      enhancedFeatures: this.calculateFeatureScore(enhancedFeatures),
      userInteractions: this.calculateScore(userInteractions),
      integration: this.calculateScore(integration),
      performance: this.calculateScore(performance)
    };
    
    // Calculate overall score
    const totalScore = Object.values(scores).reduce((sum, score) => sum + score.percentage, 0);
    const overallPercentage = Math.round(totalScore / Object.keys(scores).length);
    
    console.log("📋 Category Scores:");
    Object.entries(scores).forEach(([category, score]) => {
      const status = score.percentage >= 80 ? '✅' : score.percentage >= 60 ? '⚠️' : '❌';
      console.log(`${status} ${category}: ${score.passed}/${score.total} (${score.percentage}%)`);
    });
    
    console.log(`\n🏥 Overall Score: ${overallPercentage}%`);
    
    if (overallPercentage >= 90) {
      console.log("🎉 EXCELLENT: All systems functioning optimally");
    } else if (overallPercentage >= 75) {
      console.log("✅ GOOD: Most functionality working well");
    } else if (overallPercentage >= 60) {
      console.log("⚠️ FAIR: Some issues need attention");
    } else {
      console.log("❌ POOR: Significant functionality issues");
    }
    
    // Specific recommendations
    console.log("\n💡 RECOMMENDATIONS:");
    
    if (scores.coreExtension.percentage < 80) {
      console.log("1. Fix core extension issues - run quickYouTubeFix()");
    }
    
    if (scores.settingsPanel.percentage < 80) {
      console.log("2. Fix settings panel - run fixSettingsPanel()");
    }
    
    if (scores.enhancedFeatures.percentage < 80) {
      console.log("3. Initialize missing features - check feature initialization");
    }
    
    if (scores.integration.percentage < 80) {
      console.log("4. Fix feature integration - run testSettingsIntegration()");
    }
    
    if (scores.performance.percentage < 80) {
      console.log("5. Optimize performance - check memory usage and response times");
    }
    
    this.testResults.overall = {
      scores: scores,
      overallPercentage: overallPercentage,
      status: overallPercentage >= 75 ? 'healthy' : overallPercentage >= 60 ? 'needs_attention' : 'critical'
    };
    
    return this.testResults;
  }

  calculateScore(testObject) {
    const values = Object.values(testObject);
    const passed = values.filter(v => v === true).length;
    const total = values.length;
    const percentage = total > 0 ? Math.round((passed / total) * 100) : 0;
    
    return { passed, total, percentage };
  }

  calculateFeatureScore(featuresObject) {
    const features = Object.values(featuresObject);
    const available = features.filter(f => f.available).length;
    const functional = features.filter(f => f.available && f.functional !== false).length;
    const total = features.length;
    const percentage = total > 0 ? Math.round((functional / total) * 100) : 0;
    
    return { passed: functional, total, percentage };
  }
}

// Quick functionality test
window.quickFunctionalityTest = function() {
  console.log("⚡ Quick Functionality Test");
  console.log("===========================");
  
  const checks = {
    extension: !!window.pipMasterInstance,
    settings: !!window.pipMasterInstance?.settingsPanel,
    features: 0,
    overlays: document.querySelectorAll('.pip-master-overlay').length,
    storage: false
  };
  
  // Count features
  const features = ['smartAutoPiP', 'themeManager', 'accessibility', 'performanceOptimizer', 'sitePreferences', 'timelineControl'];
  checks.features = features.filter(f => window.pipMasterInstance?.[f]).length;
  
  // Test storage
  try {
    localStorage.setItem('test', 'test');
    localStorage.removeItem('test');
    checks.storage = true;
  } catch (error) {
    checks.storage = false;
  }
  
  console.log("Quick checks:");
  Object.entries(checks).forEach(([check, result]) => {
    const status = typeof result === 'boolean' ? (result ? '✅' : '❌') : `${result}`;
    console.log(`${status} ${check}: ${result}`);
  });
  
  const score = (checks.extension ? 20 : 0) + 
                (checks.settings ? 20 : 0) + 
                (checks.features / 6 * 40) + 
                (checks.overlays > 0 ? 10 : 0) + 
                (checks.storage ? 10 : 0);
  
  console.log(`\n📊 Quick Score: ${Math.round(score)}%`);
  
  return { score: Math.round(score), checks };
};

// Auto-run quick test
console.log("🚀 Auto-running quick functionality test...");
window.quickFunctionalityTest();

console.log("\n📋 Available Test Commands:");
console.log("===========================");
console.log("quickFunctionalityTest()              - Quick functionality check");
console.log("new CompleteFunctionalityTest()       - Complete comprehensive test");
console.log("pipMasterInstance.settingsPanel.showPanel() - Open settings to test manually");
