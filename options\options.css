/* PiP Master Options Page Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background: #f5f5f5;
  min-height: 100vh;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Header */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo h1 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 4px;
}

.logo p {
  font-size: 16px;
  opacity: 0.9;
}

/* Main Content */
.main {
  padding: 32px;
}

.settings-section {
  margin-bottom: 40px;
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section h2 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #667eea;
  padding-bottom: 8px;
}

.setting-group {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #e9ecef;
}

.setting-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.setting-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  flex: 1;
  position: relative;
  padding-left: 32px;
}

.setting-label input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  left: 0;
  top: 0;
}

.checkmark {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 20px;
  width: 20px;
  background-color: #e9ecef;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.setting-label:hover input ~ .checkmark {
  background-color: #dee2e6;
}

.setting-label input:checked ~ .checkmark {
  background-color: #667eea;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 7px;
  top: 3px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.setting-label input:checked ~ .checkmark:after {
  display: block;
}

.setting-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.setting-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.setting-description {
  font-size: 13px;
  color: #666;
}

.setting-select {
  min-width: 200px;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.setting-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 200px;
}

.slider-container input[type="range"] {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #dee2e6;
  outline: none;
  -webkit-appearance: none;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #667eea;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-container input[type="range"]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #667eea;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-value {
  font-size: 14px;
  color: #666;
  min-width: 40px;
  text-align: right;
  font-weight: 500;
}

/* Keyboard Shortcuts */
.shortcuts-info {
  margin-bottom: 20px;
  padding: 16px;
  background: #e3f2fd;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.shortcuts-info p {
  color: #1565c0;
  font-size: 14px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #e9ecef;
}

.shortcut-item:last-child {
  border-bottom: none;
}

.shortcut-info {
  flex: 1;
}

.shortcut-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 2px;
}

.shortcut-description {
  font-size: 13px;
  color: #666;
}

.shortcut-key {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 8px 16px;
  border-radius: 6px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
  text-align: center;
}

.shortcut-key:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.shortcut-key:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Footer */
.footer {
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 24px 32px;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.footer-info p {
  font-size: 13px;
  color: #666;
  margin-bottom: 2px;
}

.footer-info p:last-child {
  margin-bottom: 0;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.btn:active {
  transform: translateY(0);
}

/* Save Notification */
.save-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #28a745;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
  font-weight: 500;
  z-index: 1000;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
}

.save-notification.show {
  transform: translateX(0);
  opacity: 1;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  body {
    background: #1a1a1a;
    color: #e0e0e0;
  }

  .container {
    background: #2d2d2d;
  }

  .settings-section h2 {
    color: #e0e0e0;
  }

  .setting-group {
    background: #3d3d3d;
  }

  .setting-item {
    border-bottom-color: #4d4d4d;
  }

  .setting-title {
    color: #e0e0e0;
  }

  .setting-description {
    color: #aaa;
  }

  .checkmark {
    background-color: #4d4d4d;
  }

  .setting-label:hover input ~ .checkmark {
    background-color: #5d5d5d;
  }

  .setting-select {
    background: #3d3d3d;
    border-color: #4d4d4d;
    color: #e0e0e0;
  }

  .shortcut-key {
    background: #3d3d3d;
    border-color: #4d4d4d;
    color: #e0e0e0;
  }

  .shortcut-key:hover {
    background: #4d4d4d;
    border-color: #5d5d5d;
  }

  .footer {
    background: #3d3d3d;
    border-top-color: #4d4d4d;
  }

  .footer-info p {
    color: #aaa;
  }
}

/* Accessibility */
.btn:focus,
.setting-select:focus,
.shortcut-key:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.setting-label:focus-within .checkmark {
  box-shadow: 0 0 0 2px #667eea;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.settings-section {
  animation: fadeIn 0.3s ease-out;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
