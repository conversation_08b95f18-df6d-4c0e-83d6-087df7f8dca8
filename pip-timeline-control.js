// PiP Timeline Control Feature
// Adds timeline scrubbing and progress display to PiP window

console.log("⏯️ PiP Timeline Control Feature");
console.log("===============================");

// Timeline Control Manager
class PiPTimelineControl {
  constructor(pipMaster) {
    this.pipMaster = pipMaster;
    this.activeVideo = null;
    this.timelineElement = null;
    this.progressBar = null;
    this.timeDisplay = null;
    this.isDragging = false;
    this.updateInterval = null;
    this.setupTimelineControl();
  }

  setupTimelineControl() {
    // Listen for PiP events
    document.addEventListener("enterpictureinpicture", (event) => {
      this.activeVideo = event.target;
      this.createTimelineOverlay();
      this.startProgressUpdates();
      console.log("⏯️ Timeline control activated for PiP");
    });

    document.addEventListener("leavepictureinpicture", () => {
      this.activeVideo = null;
      this.removeTimelineOverlay();
      this.stopProgressUpdates();
      console.log("⏯️ Timeline control deactivated");
    });

    // Keyboard shortcuts for timeline control
    this.setupKeyboardShortcuts();
  }

  createTimelineOverlay() {
    if (!this.activeVideo || this.timelineElement) return;

    // Create timeline container
    this.timelineElement = document.createElement("div");
    this.timelineElement.id = "pip-timeline-control";
    this.timelineElement.style.cssText = `
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px 15px;
      border-radius: 8px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 12px;
      z-index: 10006;
      min-width: 300px;
      backdrop-filter: blur(10px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: auto;
    `;

    // Create timeline structure
    this.timelineElement.innerHTML = `
      <div style="display: flex; align-items: center; gap: 10px;">
        <div id="pip-current-time" style="min-width: 40px; text-align: center;">0:00</div>
        <div style="flex: 1; position: relative; height: 20px; display: flex; align-items: center;">
          <div id="pip-progress-track" style="
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            cursor: pointer;
            position: relative;
          ">
            <div id="pip-progress-bar" style="
              height: 100%;
              background: #ff4444;
              border-radius: 2px;
              width: 0%;
              transition: width 0.1s ease;
            "></div>
            <div id="pip-progress-handle" style="
              position: absolute;
              top: 50%;
              transform: translate(-50%, -50%);
              width: 12px;
              height: 12px;
              background: #ff4444;
              border-radius: 50%;
              cursor: grab;
              opacity: 0;
              transition: opacity 0.2s ease;
              left: 0%;
            "></div>
          </div>
        </div>
        <div id="pip-duration" style="min-width: 40px; text-align: center;">0:00</div>
      </div>
      <div style="text-align: center; margin-top: 5px; font-size: 10px; opacity: 0.7;">
        Click to seek • Drag to scrub • Hover to show
      </div>
    `;

    document.body.appendChild(this.timelineElement);

    // Get references to elements
    this.progressBar = document.getElementById("pip-progress-bar");
    this.progressTrack = document.getElementById("pip-progress-track");
    this.progressHandle = document.getElementById("pip-progress-handle");
    this.currentTimeDisplay = document.getElementById("pip-current-time");
    this.durationDisplay = document.getElementById("pip-duration");

    // Setup interactions
    this.setupTimelineInteractions();

    // Show timeline on hover near bottom of screen
    this.setupHoverBehavior();

    // Apply current hover preview setting
    if (
      window.pipMasterInstance?.settingsPanel?.settings
        ?.timelinePreviewEnabled === false
    ) {
      this.disableHoverPreview();
    }

    // Initial update
    this.updateTimeline();
  }

  setupTimelineInteractions() {
    if (!this.progressTrack || !this.progressHandle) return;

    // Click to seek
    this.progressTrack.addEventListener("click", (event) => {
      if (this.isDragging) return;
      this.seekToPosition(event);
    });

    // Drag to scrub
    this.progressHandle.addEventListener("mousedown", (event) => {
      event.preventDefault();
      this.isDragging = true;
      this.progressHandle.style.cursor = "grabbing";
      this.progressHandle.style.opacity = "1";

      const handleMouseMove = (moveEvent) => {
        if (this.isDragging) {
          this.seekToPosition(moveEvent);
        }
      };

      const handleMouseUp = () => {
        this.isDragging = false;
        this.progressHandle.style.cursor = "grab";
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };

      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    });

    // Show handle on hover
    this.progressTrack.addEventListener("mouseenter", () => {
      this.progressHandle.style.opacity = "1";
    });

    this.progressTrack.addEventListener("mouseleave", () => {
      if (!this.isDragging) {
        this.progressHandle.style.opacity = "0";
      }
    });
  }

  setupHoverBehavior() {
    this.hoverPreviewEnabled = true; // Default to enabled
    this.setupHoverListeners();
  }

  setupHoverListeners() {
    // Remove existing listeners if any
    if (this.mouseMoveHandler) {
      document.removeEventListener("mousemove", this.mouseMoveHandler);
    }
    if (this.timelineEnterHandler) {
      this.timelineElement?.removeEventListener(
        "mouseenter",
        this.timelineEnterHandler
      );
    }
    if (this.timelineLeaveHandler) {
      this.timelineElement?.removeEventListener(
        "mouseleave",
        this.timelineLeaveHandler
      );
    }

    if (!this.hoverPreviewEnabled) return;

    let hoverTimeout;

    // Show timeline when mouse is near bottom of screen
    this.mouseMoveHandler = (event) => {
      if (!this.timelineElement || !document.pictureInPictureElement) return;

      const windowHeight = window.innerHeight;
      const mouseY = event.clientY;
      const bottomThreshold = windowHeight - 100; // Show when within 100px of bottom

      if (mouseY > bottomThreshold) {
        this.showTimeline();
        clearTimeout(hoverTimeout);
        hoverTimeout = setTimeout(() => {
          this.hideTimeline();
        }, 3000); // Hide after 3 seconds of no movement
      }
    };

    document.addEventListener("mousemove", this.mouseMoveHandler);

    // Always show when hovering over timeline itself
    this.timelineEnterHandler = () => {
      this.showTimeline();
      clearTimeout(hoverTimeout);
    };

    this.timelineLeaveHandler = () => {
      hoverTimeout = setTimeout(() => {
        this.hideTimeline();
      }, 1000);
    };

    if (this.timelineElement) {
      this.timelineElement.addEventListener(
        "mouseenter",
        this.timelineEnterHandler
      );
      this.timelineElement.addEventListener(
        "mouseleave",
        this.timelineLeaveHandler
      );
    }
  }

  enableHoverPreview() {
    console.log("⏯️ Enabling timeline hover preview");
    this.hoverPreviewEnabled = true;
    this.setupHoverListeners();
  }

  disableHoverPreview() {
    console.log("⏯️ Disabling timeline hover preview");
    this.hoverPreviewEnabled = false;
    this.setupHoverListeners();

    // Hide timeline if currently visible due to hover
    if (this.timelineElement && this.timelineElement.style.opacity === "1") {
      this.hideTimeline();
    }
  }

  showTimeline() {
    if (this.timelineElement) {
      this.timelineElement.style.opacity = "1";
    }
  }

  hideTimeline() {
    if (this.timelineElement && !this.isDragging) {
      this.timelineElement.style.opacity = "0.3";
    }
  }

  seekToPosition(event) {
    if (!this.activeVideo || !this.progressTrack) return;

    const rect = this.progressTrack.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = Math.max(0, Math.min(1, clickX / rect.width));

    if (this.activeVideo.duration) {
      const newTime = percentage * this.activeVideo.duration;
      this.activeVideo.currentTime = newTime;

      // Update progress immediately for responsive feedback
      this.updateProgress(percentage);

      // Show seek feedback
      this.showSeekFeedback(newTime);
    }
  }

  updateProgress(percentage) {
    if (this.progressBar && this.progressHandle) {
      const percent = percentage * 100 + "%";
      this.progressBar.style.width = percent;
      this.progressHandle.style.left = percent;
    }
  }

  showSeekFeedback(time) {
    const feedback = document.createElement("div");
    feedback.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      z-index: 10007;
      pointer-events: none;
    `;
    feedback.textContent = this.formatTime(time);

    document.body.appendChild(feedback);

    setTimeout(() => {
      if (feedback.parentElement) {
        feedback.parentElement.removeChild(feedback);
      }
    }, 1000);
  }

  setupKeyboardShortcuts() {
    document.addEventListener("keydown", (event) => {
      // Handle Alt+T for timeline toggle (works even when PiP is not active)
      if (event.altKey && event.key.toLowerCase() === "t") {
        event.preventDefault();
        if (this.timelineElement) {
          this.toggle();
          console.log("⏯️ Timeline toggled via Alt+T");
        }
        return;
      }

      if (!document.pictureInPictureElement || !this.activeVideo) return;

      // Only handle if no input is focused
      if (
        event.target.tagName === "INPUT" ||
        event.target.tagName === "TEXTAREA"
      )
        return;

      switch (event.key) {
        case "ArrowLeft":
          if (event.shiftKey) {
            event.preventDefault();
            this.skipTime(-30); // Shift + Left: -30 seconds
          } else if (event.ctrlKey) {
            event.preventDefault();
            this.skipTime(-10); // Ctrl + Left: -10 seconds (existing)
          } else {
            event.preventDefault();
            this.skipTime(-5); // Left: -5 seconds
          }
          break;

        case "ArrowRight":
          if (event.shiftKey) {
            event.preventDefault();
            this.skipTime(30); // Shift + Right: +30 seconds
          } else if (event.ctrlKey) {
            event.preventDefault();
            this.skipTime(10); // Ctrl + Right: +10 seconds (existing)
          } else {
            event.preventDefault();
            this.skipTime(5); // Right: +5 seconds
          }
          break;

        case "Home":
          event.preventDefault();
          this.seekToBeginning();
          break;

        case "End":
          event.preventDefault();
          this.seekToEnd();
          break;

        case "0":
        case "1":
        case "2":
        case "3":
        case "4":
        case "5":
        case "6":
        case "7":
        case "8":
        case "9":
          event.preventDefault();
          this.seekToPercentage(parseInt(event.key) * 10);
          break;
      }
    });
  }

  skipTime(seconds) {
    if (!this.activeVideo) return;

    const newTime = Math.max(
      0,
      Math.min(
        this.activeVideo.duration || 0,
        this.activeVideo.currentTime + seconds
      )
    );
    this.activeVideo.currentTime = newTime;
    this.showSeekFeedback(newTime);
  }

  seekToBeginning() {
    if (!this.activeVideo) return;
    this.activeVideo.currentTime = 0;
    this.showSeekFeedback(0);
  }

  seekToEnd() {
    if (!this.activeVideo || !this.activeVideo.duration) return;
    this.activeVideo.currentTime = this.activeVideo.duration - 1; // 1 second before end
    this.showSeekFeedback(this.activeVideo.currentTime);
  }

  seekToPercentage(percentage) {
    if (!this.activeVideo || !this.activeVideo.duration) return;

    const newTime = (percentage / 100) * this.activeVideo.duration;
    this.activeVideo.currentTime = newTime;
    this.showSeekFeedback(newTime);
  }

  startProgressUpdates() {
    this.stopProgressUpdates(); // Clear any existing interval

    this.updateInterval = setInterval(() => {
      this.updateTimeline();
    }, 100); // Update every 100ms for smooth progress
  }

  stopProgressUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  updateTimeline() {
    if (!this.activeVideo || this.isDragging) return;

    const currentTime = this.activeVideo.currentTime || 0;
    const duration = this.activeVideo.duration || 0;
    const percentage = duration > 0 ? currentTime / duration : 0;

    // Update progress bar
    this.updateProgress(percentage);

    // Update time displays
    if (this.currentTimeDisplay) {
      this.currentTimeDisplay.textContent = this.formatTime(currentTime);
    }

    if (this.durationDisplay) {
      this.durationDisplay.textContent = this.formatTime(duration);
    }
  }

  formatTime(seconds) {
    if (!seconds || isNaN(seconds)) return "0:00";

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, "0")}:${secs
        .toString()
        .padStart(2, "0")}`;
    } else {
      return `${minutes}:${secs.toString().padStart(2, "0")}`;
    }
  }

  removeTimelineOverlay() {
    if (this.timelineElement) {
      this.timelineElement.remove();
      this.timelineElement = null;
      this.progressBar = null;
      this.progressTrack = null;
      this.progressHandle = null;
      this.currentTimeDisplay = null;
      this.durationDisplay = null;
    }
  }

  // Public methods for external control
  show() {
    this.showTimeline();
  }

  hide() {
    this.hideTimeline();
  }

  toggle() {
    if (this.timelineElement) {
      const isVisible = this.timelineElement.style.opacity === "1";
      if (isVisible) {
        this.hideTimeline();
      } else {
        this.showTimeline();
      }
    }
  }

  getTimelineInfo() {
    if (!this.activeVideo) return null;

    return {
      currentTime: this.activeVideo.currentTime,
      duration: this.activeVideo.duration,
      percentage:
        this.activeVideo.duration > 0
          ? (this.activeVideo.currentTime / this.activeVideo.duration) * 100
          : 0,
      formattedCurrent: this.formatTime(this.activeVideo.currentTime),
      formattedDuration: this.formatTime(this.activeVideo.duration),
    };
  }
}

// Initialize timeline control
window.initializeTimelineControl = function () {
  console.log("⏯️ Initializing PiP Timeline Control");

  if (!window.pipMasterInstance) {
    console.error("❌ PiP Master instance not available");
    return false;
  }

  // Initialize Timeline Control
  window.pipMasterInstance.timelineControl = new PiPTimelineControl(
    window.pipMasterInstance
  );

  console.log("✅ Timeline control initialized");
  console.log("\n⌨️ Timeline Keyboard Shortcuts:");
  console.log("Alt + T: Toggle timeline visibility");
  console.log("Left/Right Arrow: ±5 seconds");
  console.log("Ctrl + Left/Right: ±10 seconds");
  console.log("Shift + Left/Right: ±30 seconds");
  console.log("0-9 Keys: Jump to 0%-90%");
  console.log("Home: Jump to beginning");
  console.log("End: Jump to end");
  console.log("\n🖱️ Mouse Controls:");
  console.log("Click timeline: Seek to position");
  console.log("Drag handle: Scrub through video");
  console.log("Hover near bottom: Show timeline (if enabled in settings)");

  return true;
};

// Auto-initialize if PiP Master is available
if (window.pipMasterInstance) {
  window.initializeTimelineControl();
} else {
  setTimeout(() => {
    if (window.pipMasterInstance) {
      window.initializeTimelineControl();
    }
  }, 2000);
}

console.log("\n📋 Timeline Control Commands:");
console.log("=============================");
console.log(
  "initializeTimelineControl()                     - Initialize timeline control"
);
console.log("pipMasterInstance.timelineControl.show()        - Show timeline");
console.log("pipMasterInstance.timelineControl.hide()        - Hide timeline");
console.log(
  "pipMasterInstance.timelineControl.toggle()      - Toggle timeline"
);
console.log(
  "pipMasterInstance.timelineControl.getTimelineInfo() - Get timeline info"
);
