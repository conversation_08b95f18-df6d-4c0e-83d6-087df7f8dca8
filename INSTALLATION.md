# PiP Master Installation Guide

## 📋 Prerequisites

- Google Chrome browser (version 88 or later)
- Basic understanding of Chrome extensions

## 🚀 Installation Methods

### Method 1: Manual Installation (Recommended for Development)

1. **Download the Extension**

   - Clone this repository or download as ZIP
   - Extract to a folder on your computer

2. **Generate Icons** (Required)

   - Open `icons/generate-icons.html` in your browser
   - Click the download buttons to save `icon16.png`, `icon48.png`, and `icon128.png`
   - Place these files in the `icons/` folder

3. **Load in Chrome**

   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top-right corner)
   - Click "Load unpacked"
   - Select the PiP Master folder
   - The extension should now appear in your extensions list

4. **Verify Installation**
   - Look for the PiP Master icon in your Chrome toolbar
   - If not visible, click the puzzle piece icon and pin PiP Master

### Method 2: Chrome Web Store (Coming Soon)

The extension will be available on the Chrome Web Store once it's published.

## 🧪 Testing the Extension

1. **Open Test Page**

   - Open `test-page.html` in Chrome
   - This page contains sample videos for testing

2. **Test Basic Functionality**

   - Play any video on the test page
   - Look for the PiP overlay button (usually top-right corner)
   - Click the button or press `Alt+P` to activate Picture-in-Picture

3. **Test Settings**
   - Click the extension icon in the toolbar
   - Try changing settings like overlay position and opacity
   - Open the full settings page for advanced options

## 🔧 Configuration

### Quick Settings (Extension Popup)

- Click the extension icon for quick access to:
  - Enable/disable toggle
  - Overlay visibility
  - Snap to corners
  - Opacity control
  - Overlay position

### Advanced Settings

- Right-click the extension icon → "Options"
- Or click "Settings" in the popup
- Configure:
  - Theme preferences
  - Keyboard shortcuts
  - Video detection sensitivity
  - Advanced behavior options

## 🎯 Usage Tips

### Keyboard Shortcuts

- `Alt+P` - Toggle Picture-in-Picture
- `Alt+.` - Increase opacity
- `Alt+,` - Decrease opacity

### Best Practices

1. **Video Compatibility**: Works best with HTML5 videos
2. **Site Permissions**: Some sites may block PiP for DRM content
3. **Performance**: Disable on sites where not needed to save resources
4. **Multiple Videos**: Extension automatically detects the best video to use

## 🐛 Troubleshooting

### Common Issues

**PiP button doesn't appear**

- Check if extension is enabled
- Verify video is playing and has sufficient size
- Try refreshing the page
- Check if overlay is enabled in settings

**Keyboard shortcuts don't work**

- Ensure the webpage has focus (click on it first)
- Check for conflicts with other extensions
- Verify shortcuts in extension settings

**Settings don't save**

- Check Chrome storage permissions
- Try disabling and re-enabling the extension
- Clear extension data and reconfigure

**PiP doesn't work on specific sites**

- Some streaming services block PiP for copyright reasons
- Try on different video platforms
- Check browser console for error messages

### Debug Mode

1. Open Chrome DevTools (`F12`)
2. Check Console tab for PiP Master messages
3. Look for errors or warnings
4. Report issues with console output

## 🔒 Permissions Explained

The extension requires these permissions:

- **Storage**: Save your settings and preferences
- **Active Tab**: Detect videos on the current page
- **Scripting**: Inject PiP functionality into web pages
- **Host Permissions**: Access all websites to detect videos universally

## 📱 Browser Compatibility

### Supported Browsers

- ✅ Google Chrome (88+)
- ✅ Microsoft Edge (88+)
- ✅ Brave Browser
- ✅ Other Chromium-based browsers

### Unsupported Browsers

- ❌ Firefox (different extension format)
- ❌ Safari (different extension system)
- ❌ Internet Explorer (outdated)

## 🔄 Updates

### Manual Updates

1. Download the latest version
2. Replace the old extension folder
3. Reload the extension in `chrome://extensions/`

### Automatic Updates (Chrome Web Store)

- Updates will be automatic once published to the store
- Check extension details for version information

## 🗑️ Uninstallation

1. Go to `chrome://extensions/`
2. Find PiP Master
3. Click "Remove"
4. Confirm removal

All settings and data will be automatically cleaned up.

## 📞 Support

If you encounter issues:

1. Check this troubleshooting guide
2. Review the [README.md](README.md) for additional information
3. Create an issue on GitHub with:
   - Chrome version
   - Extension version
   - Steps to reproduce
   - Console error messages (if any)

## 🎉 Success!

Once installed, you should see:

- PiP Master icon in your toolbar
- Overlay buttons on videos
- Working keyboard shortcuts
- Accessible settings panel

Enjoy enhanced Picture-in-Picture functionality across all your favorite video sites!
