# 🔧 PiP Master Chrome Extension - Complete Troubleshooting Guide

## 📋 **Quick Diagnostic Checklist**

Before diving into specific issues, run this quick check:

- [ ] Chrome version 88+ (check `chrome://version/`)
- [ ] Extension files in correct folder structure
- [ ] All three icon files present and named correctly
- [ ] Developer mode enabled in `chrome://extensions/`
- [ ] No red error messages in extension list
- [ ] Console shows PiP Master initialization messages

---

## 1. 🚫 **Extension Loading Issues**

### **Symptoms:**
- Extension doesn't appear in `chrome://extensions/`
- Red error messages when loading
- "Could not load manifest" errors
- Extension loads but shows as disabled

### **Step-by-Step Diagnosis:**

#### **A. Verify File Structure**
```
PIP/
├── manifest.json ✓
├── background.js ✓
├── content/
│   ├── content.js ✓
│   └── content.css ✓
├── popup/
│   ├── popup.html ✓
│   ├── popup.js ✓
│   └── popup.css ✓
├── options/
│   ├── options.html ✓
│   ├── options.js ✓
│   └── options.css ✓
└── icons/
    ├── icon16.png ✓
    ├── icon48.png ✓
    └── icon128.png ✓
```

#### **B. Validate manifest.json**
1. Open `manifest.json` in a text editor
2. Check for syntax errors (missing commas, brackets)
3. Verify required fields are present:

```json
{
  "manifest_version": 3,
  "name": "PiP Master",
  "version": "1.0.0",
  "description": "...",
  "permissions": ["storage", "activeTab", "scripting"],
  "host_permissions": ["https://*/*", "http://*/*"],
  "background": {"service_worker": "background.js"},
  "content_scripts": [...],
  "action": {...},
  "icons": {...},
  "commands": {...}
}
```

#### **C. Common Manifest Errors & Fixes**

**Error: "Invalid value for 'commands[X].default'"**
```json
// ❌ Wrong
"suggested_key": {"default": "Alt+Plus"}

// ✅ Correct
"suggested_key": {"default": "Alt+Period"}
```

**Error: "Could not load icon"**
- Ensure icon files exist in `icons/` folder
- Check file names are exactly: `icon16.png`, `icon48.png`, `icon128.png`
- Verify files are valid PNG format

**Error: "Background script not found"**
- Verify `background.js` exists in root folder
- Check file name spelling and case sensitivity

#### **D. Loading Process**
1. Open Chrome → `chrome://extensions/`
2. Enable "Developer mode" (top-right toggle)
3. Click "Load unpacked"
4. Select the `PIP` folder (not a subfolder)
5. Extension should appear without errors

**Expected Result:** Extension appears with PiP Master icon and no error messages.

---

## 2. 🎨 **Icon Generation Problems**

### **Symptoms:**
- Extension shows default puzzle piece icon
- "Could not load icon" errors
- Icons appear blurry or incorrect size

### **Step-by-Step Icon Generation:**

#### **A. Generate Icons**
1. Open `icons/create-placeholder-icons.html` in Chrome
2. Wait for icons to render (should see 3 preview icons)
3. Click "📦 Download All Icons (Recommended)"
4. Or click individual download buttons

#### **B. Save Icons Correctly**
1. **Important:** Save with exact filenames:
   - `icon16.png` (not `icon16 (1).png` or `Icon16.PNG`)
   - `icon48.png`
   - `icon128.png`
2. Place in `icons/` folder of extension
3. Verify file sizes:
   - icon16.png: ~1-3 KB
   - icon48.png: ~2-5 KB
   - icon128.png: ~5-15 KB

#### **C. Verify Icon Installation**
1. Reload extension in `chrome://extensions/`
2. Extension should show custom PiP icon (not puzzle piece)
3. Right-click extension icon → should show "PiP Master" name

**Expected Result:** Custom blue gradient PiP icons visible in toolbar and extension management.

---

## 3. ⌨️ **Keyboard Shortcuts**

### **Symptoms:**
- Shortcuts don't work when pressed
- Wrong shortcuts shown in popup
- Conflicts with other extensions

### **Step-by-Step Verification:**

#### **A. Check Shortcut Registration**
1. Go to `chrome://extensions/shortcuts`
2. Find "PiP Master" in the list
3. Verify shortcuts are assigned:
   - Toggle Picture-in-Picture: `Alt+P`
   - Increase PiP window opacity: `Alt+.` (Period)
   - Decrease PiP window opacity: `Alt+,` (Comma)

#### **B. Test Shortcuts**
1. Open a video site (YouTube, Netflix)
2. Play a video
3. Press `Alt+P` → Should activate PiP
4. While in PiP, press `Alt+.` → Should increase opacity
5. Press `Alt+,` → Should decrease opacity

#### **C. Resolve Conflicts**
1. In `chrome://extensions/shortcuts`, check for conflicts
2. If shortcuts show "Not set", manually assign them:
   - Click in shortcut field
   - Press desired key combination
   - Click outside to save

#### **D. Debug Shortcut Issues**
1. Open DevTools (`F12`) on any page
2. Go to Console tab
3. Press shortcuts and look for messages:
   ```
   PiP Master: Command "toggle-pip" triggered
   ```

**Expected Result:** All three shortcuts work and show console messages when pressed.

---

## 4. 🎥 **Video Detection Issues**

### **Symptoms:**
- No PiP overlay buttons appear on videos
- Buttons appear but don't work
- Only works on some sites but not others

### **Step-by-Step Diagnosis:**

#### **A. Test Video Detection**
1. Open `test-page.html` (included with extension)
2. Play any video on the test page
3. Look for PiP overlay button (usually top-right corner)
4. If working, test on real sites (YouTube, Netflix)

#### **B. Check Content Script Injection**
1. Open DevTools (`F12`) on video page
2. Go to Console tab
3. Look for initialization message:
   ```
   PiP Master: Content script initialized
   ```
4. If missing, content script isn't loading

#### **C. Debug Video Detection**
1. In Console, type:
   ```javascript
   document.querySelectorAll('video').length
   ```
2. Should return number > 0 if videos present
3. Check if videos are suitable:
   ```javascript
   Array.from(document.querySelectorAll('video')).map(v => ({
     width: v.videoWidth,
     height: v.videoHeight,
     duration: v.duration,
     paused: v.paused
   }))
   ```

#### **D. Force Content Script Reload**
1. Go to `chrome://extensions/`
2. Click refresh button on PiP Master extension
3. Reload the video page
4. Check console for initialization messages

#### **E. Check Extension Popup Status**
1. Click PiP Master extension icon
2. Check "Videos found" count
3. Should show number of detected videos
4. If 0, video detection is failing

**Expected Result:** Overlay buttons appear on videos, popup shows correct video count.

---

## 5. 🖼️ **Picture-in-Picture Functionality**

### **Symptoms:**
- PiP button appears but clicking does nothing
- Error messages when trying to activate PiP
- PiP works on some sites but not others

### **Step-by-Step Debugging:**

#### **A. Test Browser PiP Support**
1. Open any video site
2. Right-click on video
3. Look for "Picture in picture" in context menu
4. If missing, browser doesn't support PiP

#### **B. Check Site Restrictions**
Some sites block PiP for DRM content:
- **Netflix**: May block on some content
- **Disney+**: Often blocks PiP
- **YouTube**: Usually works
- **Local videos**: Should always work

#### **C. Debug PiP API Calls**
1. Open DevTools Console
2. Try manual PiP activation:
   ```javascript
   const video = document.querySelector('video');
   video.requestPictureInPicture()
     .then(() => console.log('PiP activated'))
     .catch(err => console.error('PiP failed:', err));
   ```

#### **D. Check Error Messages**
Common PiP errors and solutions:

**"PictureInPictureError: Document is not allowed to use Picture-in-Picture"**
- Site has disabled PiP via permissions policy
- Try different site or video

**"InvalidStateError: Invalid video state"**
- Video not loaded or has no video track
- Wait for video to load metadata

**"NotSupportedError: Picture-in-Picture is not supported"**
- Browser doesn't support PiP
- Update Chrome to latest version

#### **E. Test Extension PiP Logic**
1. Click extension icon → popup should open
2. Click "Toggle PiP" button in popup
3. Should activate PiP or show error message
4. Check console for detailed error info

**Expected Result:** PiP activates successfully on supported videos and sites.

---

## 6. 💾 **Settings and Storage Issues**

### **Symptoms:**
- Settings don't save between sessions
- Default settings not loading
- Changes in options page don't persist

### **Step-by-Step Diagnosis:**

#### **A. Test Settings Storage**
1. Open DevTools Console
2. Check current settings:
   ```javascript
   chrome.storage.sync.get('pipMasterSettings', (result) => {
     console.log('Current settings:', result);
   });
   ```

#### **B. Verify Settings Persistence**
1. Open extension popup
2. Change a setting (e.g., overlay position)
3. Close popup and reopen
4. Setting should be preserved

#### **C. Test Options Page**
1. Right-click extension icon → "Options"
2. Change settings and click "Save Settings"
3. Should see "Settings saved successfully!" message
4. Reload options page → settings should persist

#### **D. Clear and Reset Settings**
If settings are corrupted:
1. In DevTools Console:
   ```javascript
   chrome.storage.sync.clear(() => {
     console.log('Settings cleared');
   });
   ```
2. Reload extension
3. Should restore default settings

#### **E. Check Storage Permissions**
1. Verify manifest.json includes:
   ```json
   "permissions": ["storage"]
   ```
2. If missing, add and reload extension

**Expected Result:** Settings save automatically and persist between browser sessions.

---

## 7. 🐛 **Console Errors and JavaScript Issues**

### **Symptoms:**
- Extension partially works
- Unexpected behavior
- Features stop working after some time

### **Step-by-Step Error Diagnosis:**

#### **A. Check Extension Console**
1. Go to `chrome://extensions/`
2. Find PiP Master extension
3. Click "Inspect views: service worker"
4. Check Console tab for errors

#### **B. Check Content Script Console**
1. Open any webpage with videos
2. Open DevTools (`F12`)
3. Look for errors in Console tab
4. Filter by "PiP Master" or extension ID

#### **C. Common JavaScript Errors**

**"Cannot read property 'addListener' of undefined"**
- Chrome APIs not available
- Check manifest permissions

**"Extension context invalidated"**
- Extension was reloaded/updated
- Refresh the page

**"Uncaught TypeError: chrome.runtime.sendMessage is not a function"**
- Content script trying to use unavailable API
- Check content script context

**"Failed to execute 'requestPictureInPicture' on 'HTMLVideoElement'"**
- Video element not ready
- Add proper error handling

#### **D. Enable Verbose Logging**
Add to content script for debugging:
```javascript
console.log('PiP Master: Video found:', video);
console.log('PiP Master: Settings loaded:', settings);
```

#### **E. Test in Incognito Mode**
1. Enable extension in incognito mode
2. Test functionality
3. If works in incognito but not normal mode, clear browser data

**Expected Result:** No JavaScript errors in console, all features work smoothly.

---

## 🎯 **Expected Working State**

When PiP Master is properly installed and functioning:

### **✅ Visual Indicators:**
- Custom PiP icon in Chrome toolbar (not puzzle piece)
- Extension appears in `chrome://extensions/` without errors
- PiP overlay buttons appear on videos when playing

### **✅ Functional Tests:**
- `Alt+P` activates/deactivates Picture-in-Picture
- `Alt+.` and `Alt+,` adjust opacity during PiP
- Extension popup shows correct video count
- Settings save and persist between sessions
- Works on test page and major video sites

### **✅ Console Messages:**
```
PiP Master: Content script initialized
PiP Master: Video found: <video element>
PiP Master: Settings loaded: {enabled: true, ...}
PiP Master: Command "toggle-pip" triggered
PiP Master: Entered PiP mode
```

### **✅ No Error Messages:**
- No red errors in `chrome://extensions/`
- No JavaScript errors in browser console
- No manifest validation errors

---

## 🆘 **Still Having Issues?**

If problems persist after following this guide:

1. **Check Chrome Version**: Ensure Chrome 88+ (`chrome://version/`)
2. **Try Fresh Install**: Delete extension folder, re-download, and reinstall
3. **Test in Clean Profile**: Create new Chrome profile and test extension
4. **Disable Other Extensions**: Temporarily disable other extensions to check for conflicts
5. **Check System Requirements**: Ensure your OS supports Picture-in-Picture

### **Report Issues:**
When reporting problems, include:
- Chrome version
- Operating system
- Exact error messages
- Console output
- Steps to reproduce
- Which sites/videos you tested

This comprehensive troubleshooting guide should resolve most issues with the PiP Master extension!
