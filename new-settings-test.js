// New Settings Test Script
// Test the newly added Audio Control and Timeline Preview settings

console.log("🧪 Testing New Settings: Audio Control & Timeline Preview");
console.log("=========================================================");

// Test New Settings Functionality
class NewSettingsTest {
  constructor() {
    this.testResults = {
      audioControl: {},
      timelinePreview: {},
      integration: {},
      persistence: {}
    };
    this.runNewSettingsTest();
  }

  runNewSettingsTest() {
    console.log("🚀 Testing newly added settings...");
    
    this.testAudioControlSetting();
    this.testTimelinePreviewSetting();
    this.testSettingsIntegration();
    this.testSettingsPersistence();
    this.generateTestReport();
  }

  testAudioControlSetting() {
    console.log("\n1️⃣ Audio Control Setting Test");
    console.log("==============================");
    
    const tests = {
      settingExists: false,
      defaultValue: false,
      formElement: false,
      saveLoad: false,
      functionality: false
    };
    
    // Check if setting exists in default settings
    if (window.pipMasterInstance?.settingsPanel) {
      const defaultSettings = window.pipMasterInstance.settingsPanel.getDefaultSettings();
      tests.settingExists = 'audioControlEnabled' in defaultSettings;
      tests.defaultValue = defaultSettings.audioControlEnabled === true;
      
      console.log(`Setting exists: ${tests.settingExists ? '✅' : '❌'}`);
      console.log(`Default value (true): ${tests.defaultValue ? '✅' : '❌'}`);
    }
    
    // Check if form element exists
    const audioControlElement = document.getElementById('audio-control-enabled');
    tests.formElement = !!audioControlElement;
    console.log(`Form element exists: ${tests.formElement ? '✅' : '❌'}`);
    
    if (audioControlElement) {
      console.log(`Form element checked: ${audioControlElement.checked ? '✅' : '❌'}`);
    }
    
    // Test save/load functionality
    if (window.pipMasterInstance?.settingsPanel) {
      try {
        const settingsPanel = window.pipMasterInstance.settingsPanel;
        const originalValue = settingsPanel.settings.audioControlEnabled;
        
        // Change setting
        settingsPanel.settings.audioControlEnabled = false;
        settingsPanel.saveSettings();
        
        // Load settings
        const loadedSettings = settingsPanel.loadSettings();
        tests.saveLoad = loadedSettings.audioControlEnabled === false;
        
        // Restore original
        settingsPanel.settings.audioControlEnabled = originalValue;
        settingsPanel.saveSettings();
        
        console.log(`Save/Load functionality: ${tests.saveLoad ? '✅' : '❌'}`);
      } catch (error) {
        console.error("Save/Load test failed:", error);
      }
    }
    
    // Test audio control functionality
    try {
      if (window.pipMasterInstance?.settingsPanel) {
        const settingsPanel = window.pipMasterInstance.settingsPanel;
        
        // Test enabling audio control
        settingsPanel.settings.audioControlEnabled = true;
        settingsPanel.applyAudioControlSettings();
        
        // Test disabling audio control
        settingsPanel.settings.audioControlEnabled = false;
        settingsPanel.applyAudioControlSettings();
        
        tests.functionality = true;
        console.log(`Audio control functionality: ✅`);
      }
    } catch (error) {
      console.error("Audio control functionality test failed:", error);
      console.log(`Audio control functionality: ❌`);
    }
    
    this.testResults.audioControl = tests;
  }

  testTimelinePreviewSetting() {
    console.log("\n2️⃣ Timeline Preview Setting Test");
    console.log("=================================");
    
    const tests = {
      settingExists: false,
      defaultValue: false,
      formElement: false,
      saveLoad: false,
      functionality: false
    };
    
    // Check if setting exists in default settings
    if (window.pipMasterInstance?.settingsPanel) {
      const defaultSettings = window.pipMasterInstance.settingsPanel.getDefaultSettings();
      tests.settingExists = 'timelinePreviewEnabled' in defaultSettings;
      tests.defaultValue = defaultSettings.timelinePreviewEnabled === true;
      
      console.log(`Setting exists: ${tests.settingExists ? '✅' : '❌'}`);
      console.log(`Default value (true): ${tests.defaultValue ? '✅' : '❌'}`);
    }
    
    // Check if form element exists
    const timelinePreviewElement = document.getElementById('timeline-preview-enabled');
    tests.formElement = !!timelinePreviewElement;
    console.log(`Form element exists: ${tests.formElement ? '✅' : '❌'}`);
    
    if (timelinePreviewElement) {
      console.log(`Form element checked: ${timelinePreviewElement.checked ? '✅' : '❌'}`);
    }
    
    // Test save/load functionality
    if (window.pipMasterInstance?.settingsPanel) {
      try {
        const settingsPanel = window.pipMasterInstance.settingsPanel;
        const originalValue = settingsPanel.settings.timelinePreviewEnabled;
        
        // Change setting
        settingsPanel.settings.timelinePreviewEnabled = false;
        settingsPanel.saveSettings();
        
        // Load settings
        const loadedSettings = settingsPanel.loadSettings();
        tests.saveLoad = loadedSettings.timelinePreviewEnabled === false;
        
        // Restore original
        settingsPanel.settings.timelinePreviewEnabled = originalValue;
        settingsPanel.saveSettings();
        
        console.log(`Save/Load functionality: ${tests.saveLoad ? '✅' : '❌'}`);
      } catch (error) {
        console.error("Save/Load test failed:", error);
      }
    }
    
    // Test timeline preview functionality
    try {
      if (window.pipMasterInstance?.settingsPanel && window.pipMasterInstance?.timelineControl) {
        const settingsPanel = window.pipMasterInstance.settingsPanel;
        const timelineControl = window.pipMasterInstance.timelineControl;
        
        // Test enabling timeline preview
        settingsPanel.settings.timelinePreviewEnabled = true;
        settingsPanel.applyTimelinePreviewSettings();
        
        // Check if timeline control has the methods
        const hasEnableMethod = typeof timelineControl.enableHoverPreview === 'function';
        const hasDisableMethod = typeof timelineControl.disableHoverPreview === 'function';
        
        tests.functionality = hasEnableMethod && hasDisableMethod;
        console.log(`Timeline preview functionality: ${tests.functionality ? '✅' : '❌'}`);
        
        if (tests.functionality) {
          // Test disabling timeline preview
          settingsPanel.settings.timelinePreviewEnabled = false;
          settingsPanel.applyTimelinePreviewSettings();
          console.log(`Timeline preview disable test: ✅`);
        }
      }
    } catch (error) {
      console.error("Timeline preview functionality test failed:", error);
      console.log(`Timeline preview functionality: ❌`);
    }
    
    this.testResults.timelinePreview = tests;
  }

  testSettingsIntegration() {
    console.log("\n3️⃣ Settings Integration Test");
    console.log("=============================");
    
    const tests = {
      panelHTML: false,
      saveMethod: false,
      updateMethod: false,
      applyMethod: false
    };
    
    // Test if settings are included in panel HTML
    if (window.pipMasterInstance?.settingsPanel?.panel) {
      const panelHTML = window.pipMasterInstance.settingsPanel.panel.innerHTML;
      tests.panelHTML = panelHTML.includes('audio-control-enabled') && 
                       panelHTML.includes('timeline-preview-enabled');
      console.log(`Settings in panel HTML: ${tests.panelHTML ? '✅' : '❌'}`);
    }
    
    // Test if settings are included in save method
    try {
      if (window.pipMasterInstance?.settingsPanel) {
        const settingsPanel = window.pipMasterInstance.settingsPanel;
        
        // Mock form elements for testing
        const mockAudioElement = { checked: true };
        const mockTimelineElement = { checked: false };
        
        // Temporarily replace querySelector to return our mock elements
        const originalQuerySelector = settingsPanel.panel.querySelector;
        settingsPanel.panel.querySelector = function(selector) {
          if (selector === '#audio-control-enabled') return mockAudioElement;
          if (selector === '#timeline-preview-enabled') return mockTimelineElement;
          return originalQuerySelector.call(this, selector);
        };
        
        // Test save method
        settingsPanel.saveCurrentSettings();
        tests.saveMethod = settingsPanel.settings.audioControlEnabled === true &&
                          settingsPanel.settings.timelinePreviewEnabled === false;
        
        // Restore original querySelector
        settingsPanel.panel.querySelector = originalQuerySelector;
        
        console.log(`Save method integration: ${tests.saveMethod ? '✅' : '❌'}`);
      }
    } catch (error) {
      console.error("Save method test failed:", error);
      console.log(`Save method integration: ❌`);
    }
    
    // Test update method
    try {
      if (window.pipMasterInstance?.settingsPanel) {
        const settingsPanel = window.pipMasterInstance.settingsPanel;
        
        // Set test values
        settingsPanel.settings.audioControlEnabled = true;
        settingsPanel.settings.timelinePreviewEnabled = false;
        
        // Test update method (this would update form elements)
        settingsPanel.updatePanelFromSettings();
        tests.updateMethod = true;
        
        console.log(`Update method integration: ${tests.updateMethod ? '✅' : '❌'}`);
      }
    } catch (error) {
      console.error("Update method test failed:", error);
      console.log(`Update method integration: ❌`);
    }
    
    // Test apply method
    try {
      if (window.pipMasterInstance?.settingsPanel) {
        const settingsPanel = window.pipMasterInstance.settingsPanel;
        settingsPanel.applySettings();
        tests.applyMethod = true;
        
        console.log(`Apply method integration: ${tests.applyMethod ? '✅' : '❌'}`);
      }
    } catch (error) {
      console.error("Apply method test failed:", error);
      console.log(`Apply method integration: ❌`);
    }
    
    this.testResults.integration = tests;
  }

  testSettingsPersistence() {
    console.log("\n4️⃣ Settings Persistence Test");
    console.log("=============================");
    
    const tests = {
      localStorage: false,
      crossSession: false
    };
    
    // Test localStorage persistence
    try {
      const testSettings = {
        audioControlEnabled: false,
        timelinePreviewEnabled: true,
        overlayTheme: 'neon'
      };
      
      // Save test settings
      localStorage.setItem('pipMaster_enhancedSettings', JSON.stringify(testSettings));
      
      // Load and verify
      const loaded = JSON.parse(localStorage.getItem('pipMaster_enhancedSettings'));
      tests.localStorage = loaded.audioControlEnabled === false && 
                          loaded.timelinePreviewEnabled === true;
      
      console.log(`localStorage persistence: ${tests.localStorage ? '✅' : '❌'}`);
      
      // Clean up
      localStorage.removeItem('pipMaster_enhancedSettings');
    } catch (error) {
      console.error("localStorage test failed:", error);
      console.log(`localStorage persistence: ❌`);
    }
    
    // Test cross-session simulation
    try {
      if (window.pipMasterInstance?.settingsPanel) {
        const settingsPanel = window.pipMasterInstance.settingsPanel;
        
        // Save current settings
        settingsPanel.settings.audioControlEnabled = true;
        settingsPanel.settings.timelinePreviewEnabled = false;
        settingsPanel.saveSettings();
        
        // Simulate loading settings (like on extension restart)
        const loadedSettings = settingsPanel.loadSettings();
        tests.crossSession = loadedSettings.audioControlEnabled === true &&
                            loadedSettings.timelinePreviewEnabled === false;
        
        console.log(`Cross-session persistence: ${tests.crossSession ? '✅' : '❌'}`);
      }
    } catch (error) {
      console.error("Cross-session test failed:", error);
      console.log(`Cross-session persistence: ❌`);
    }
    
    this.testResults.persistence = tests;
  }

  generateTestReport() {
    console.log("\n📊 NEW SETTINGS TEST REPORT");
    console.log("============================");
    
    const { audioControl, timelinePreview, integration, persistence } = this.testResults;
    
    // Calculate scores
    const audioScore = this.calculateScore(audioControl);
    const timelineScore = this.calculateScore(timelinePreview);
    const integrationScore = this.calculateScore(integration);
    const persistenceScore = this.calculateScore(persistence);
    
    const overallScore = Math.round((audioScore.percentage + timelineScore.percentage + 
                                   integrationScore.percentage + persistenceScore.percentage) / 4);
    
    console.log("📋 Test Results:");
    console.log(`🔊 Audio Control Setting: ${audioScore.passed}/${audioScore.total} (${audioScore.percentage}%)`);
    console.log(`⏯️ Timeline Preview Setting: ${timelineScore.passed}/${timelineScore.total} (${timelineScore.percentage}%)`);
    console.log(`🔗 Settings Integration: ${integrationScore.passed}/${integrationScore.total} (${integrationScore.percentage}%)`);
    console.log(`💾 Settings Persistence: ${persistenceScore.passed}/${persistenceScore.total} (${persistenceScore.percentage}%)`);
    
    console.log(`\n🏥 Overall Score: ${overallScore}%`);
    
    if (overallScore >= 90) {
      console.log("🎉 EXCELLENT: New settings fully functional!");
    } else if (overallScore >= 75) {
      console.log("✅ GOOD: New settings mostly working");
    } else if (overallScore >= 60) {
      console.log("⚠️ PARTIAL: Some new settings issues");
    } else {
      console.log("❌ POOR: New settings need fixes");
    }
    
    // Specific recommendations
    console.log("\n💡 RECOMMENDATIONS:");
    
    if (audioScore.percentage < 80) {
      console.log("1. Fix audio control setting implementation");
    }
    
    if (timelineScore.percentage < 80) {
      console.log("2. Fix timeline preview setting implementation");
    }
    
    if (integrationScore.percentage < 80) {
      console.log("3. Fix settings panel integration");
    }
    
    if (persistenceScore.percentage < 80) {
      console.log("4. Fix settings persistence");
    }
    
    if (overallScore >= 80) {
      console.log("✅ New settings are ready for use!");
      console.log("\n🎮 Test the new features:");
      console.log("1. Open settings panel (Alt+S)");
      console.log("2. Toggle 'Enable audio in Picture-in-Picture mode'");
      console.log("3. Toggle 'Enable timeline preview on hover'");
      console.log("4. Save settings and test functionality");
    }
    
    return this.testResults;
  }

  calculateScore(testObject) {
    const values = Object.values(testObject);
    const passed = values.filter(v => v === true).length;
    const total = values.length;
    const percentage = total > 0 ? Math.round((passed / total) * 100) : 0;
    
    return { passed, total, percentage };
  }
}

// Quick test function
window.testNewSettings = function() {
  console.log("⚡ Quick New Settings Test");
  console.log("=========================");
  
  const checks = {
    audioControlSetting: false,
    timelinePreviewSetting: false,
    audioFormElement: false,
    timelineFormElement: false,
    settingsPanel: false
  };
  
  // Check if settings exist
  if (window.pipMasterInstance?.settingsPanel) {
    const defaultSettings = window.pipMasterInstance.settingsPanel.getDefaultSettings();
    checks.audioControlSetting = 'audioControlEnabled' in defaultSettings;
    checks.timelinePreviewSetting = 'timelinePreviewEnabled' in defaultSettings;
    checks.settingsPanel = true;
  }
  
  // Check form elements
  checks.audioFormElement = !!document.getElementById('audio-control-enabled');
  checks.timelineFormElement = !!document.getElementById('timeline-preview-enabled');
  
  console.log("Quick checks:");
  Object.entries(checks).forEach(([check, result]) => {
    console.log(`${result ? '✅' : '❌'} ${check}: ${result}`);
  });
  
  const score = Object.values(checks).filter(Boolean).length;
  const percentage = Math.round((score / Object.keys(checks).length) * 100);
  
  console.log(`\n📊 Quick Score: ${score}/${Object.keys(checks).length} (${percentage}%)`);
  
  if (percentage >= 80) {
    console.log("🎉 New settings appear to be working!");
  } else {
    console.log("⚠️ Some new settings may need attention");
  }
  
  return { percentage, checks };
};

// Auto-run quick test
console.log("🚀 Auto-running quick new settings test...");
window.testNewSettings();

console.log("\n📋 Available Test Commands:");
console.log("===========================");
console.log("testNewSettings()                    - Quick test of new settings");
console.log("new NewSettingsTest()                - Comprehensive test of new settings");
console.log("pipMasterInstance.settingsPanel.showPanel() - Open settings to test manually");
