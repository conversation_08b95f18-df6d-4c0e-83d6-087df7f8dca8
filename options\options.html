<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PiP Master Settings</title>
  <link rel="stylesheet" href="options.css">
</head>
<body>
  <div class="container">
    <!-- Header -->
    <header class="header">
      <div class="logo">
        <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 7h-8v6h8V7zm2-4H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h18v14z"/>
        </svg>
        <div>
          <h1>PiP Master</h1>
          <p>Advanced Picture-in-Picture Settings</p>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="main">
      <!-- General Settings -->
      <section class="settings-section">
        <h2>General Settings</h2>
        
        <div class="setting-group">
          <div class="setting-item">
            <label class="setting-label">
              <input type="checkbox" id="enabled" checked>
              <span class="checkmark"></span>
              <div class="setting-info">
                <span class="setting-title">Enable PiP Master</span>
                <span class="setting-description">Turn on/off the extension functionality</span>
              </div>
            </label>
          </div>

          <div class="setting-item">
            <label class="setting-label">
              <input type="checkbox" id="showOverlay" checked>
              <span class="checkmark"></span>
              <div class="setting-info">
                <span class="setting-title">Show overlay buttons</span>
                <span class="setting-description">Display PiP toggle buttons on videos</span>
              </div>
            </label>
          </div>

          <div class="setting-item">
            <label class="setting-label">
              <input type="checkbox" id="alwaysOnTop" checked>
              <span class="checkmark"></span>
              <div class="setting-info">
                <span class="setting-title">Always on top</span>
                <span class="setting-description">Keep PiP window above other windows</span>
              </div>
            </label>
          </div>
        </div>
      </section>

      <!-- Appearance Settings -->
      <section class="settings-section">
        <h2>Appearance</h2>
        
        <div class="setting-group">
          <div class="setting-item">
            <div class="setting-info">
              <span class="setting-title">Theme</span>
              <span class="setting-description">Choose your preferred theme</span>
            </div>
            <select id="theme" class="setting-select">
              <option value="auto">Auto (System)</option>
              <option value="light">Light</option>
              <option value="dark">Dark</option>
            </select>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <span class="setting-title">Overlay Position</span>
              <span class="setting-description">Default position for PiP toggle buttons</span>
            </div>
            <select id="overlayPosition" class="setting-select">
              <option value="top-left">Top Left</option>
              <option value="top-right">Top Right</option>
              <option value="bottom-left">Bottom Left</option>
              <option value="bottom-right">Bottom Right</option>
            </select>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <span class="setting-title">Opacity</span>
              <span class="setting-description">Default opacity for PiP windows</span>
            </div>
            <div class="slider-container">
              <input type="range" id="opacity" min="0.1" max="1" step="0.1" value="0.9">
              <span id="opacityValue" class="slider-value">90%</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Behavior Settings -->
      <section class="settings-section">
        <h2>Behavior</h2>
        
        <div class="setting-group">
          <div class="setting-item">
            <label class="setting-label">
              <input type="checkbox" id="snapToCorners" checked>
              <span class="checkmark"></span>
              <div class="setting-info">
                <span class="setting-title">Snap to corners</span>
                <span class="setting-description">Automatically snap PiP window to screen corners</span>
              </div>
            </label>
          </div>

          <div class="setting-item">
            <label class="setting-label">
              <input type="checkbox" id="resizable" checked>
              <span class="checkmark"></span>
              <div class="setting-info">
                <span class="setting-title">Resizable windows</span>
                <span class="setting-description">Allow resizing PiP windows by dragging corners</span>
              </div>
            </label>
          </div>

          <div class="setting-item">
            <label class="setting-label">
              <input type="checkbox" id="audioControls" checked>
              <span class="checkmark"></span>
              <div class="setting-info">
                <span class="setting-title">Audio controls</span>
                <span class="setting-description">Show volume controls in PiP window</span>
              </div>
            </label>
          </div>
        </div>
      </section>

      <!-- Keyboard Shortcuts -->
      <section class="settings-section">
        <h2>Keyboard Shortcuts</h2>
        
        <div class="setting-group">
          <div class="shortcuts-info">
            <p>Customize keyboard shortcuts for quick access to PiP features. Click on a shortcut to change it.</p>
          </div>

          <div class="shortcut-item">
            <div class="shortcut-info">
              <span class="shortcut-title">Toggle Picture-in-Picture</span>
              <span class="shortcut-description">Activate/deactivate PiP mode for the current video</span>
            </div>
            <button class="shortcut-key" data-command="toggle-pip">Alt + P</button>
          </div>

          <div class="shortcut-item">
            <div class="shortcut-info">
              <span class="shortcut-title">Increase Opacity</span>
              <span class="shortcut-description">Make PiP window more opaque</span>
            </div>
            <button class="shortcut-key" data-command="increase-opacity">Alt + Plus</button>
          </div>

          <div class="shortcut-item">
            <div class="shortcut-info">
              <span class="shortcut-title">Decrease Opacity</span>
              <span class="shortcut-description">Make PiP window more transparent</span>
            </div>
            <button class="shortcut-key" data-command="decrease-opacity">Alt + Minus</button>
          </div>
        </div>
      </section>

      <!-- Advanced Settings -->
      <section class="settings-section">
        <h2>Advanced</h2>
        
        <div class="setting-group">
          <div class="setting-item">
            <div class="setting-info">
              <span class="setting-title">Video Detection Sensitivity</span>
              <span class="setting-description">How aggressively to detect videos on pages</span>
            </div>
            <select id="detectionSensitivity" class="setting-select">
              <option value="low">Low (Only obvious videos)</option>
              <option value="medium" selected>Medium (Recommended)</option>
              <option value="high">High (All possible videos)</option>
            </select>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <span class="setting-title">Minimum Video Size</span>
              <span class="setting-description">Minimum video dimensions to show PiP button</span>
            </div>
            <select id="minVideoSize" class="setting-select">
              <option value="50">50x50 pixels</option>
              <option value="100" selected>100x100 pixels</option>
              <option value="200">200x200 pixels</option>
              <option value="300">300x300 pixels</option>
            </select>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
      <div class="footer-content">
        <div class="footer-info">
          <p>PiP Master v1.0.0</p>
          <p>Made with ❤️ for better video watching</p>
        </div>
        <div class="footer-actions">
          <button id="resetBtn" class="btn btn-secondary">Reset to Defaults</button>
          <button id="saveBtn" class="btn btn-primary">Save Settings</button>
        </div>
      </div>
    </footer>
  </div>

  <!-- Save notification -->
  <div id="saveNotification" class="save-notification">
    Settings saved successfully!
  </div>

  <script src="options.js"></script>
</body>
</html>
