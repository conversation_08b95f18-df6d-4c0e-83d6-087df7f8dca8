// YouTube Manual Fix Script
// Quick fix for YouTube detection issues

console.log("🔧 YouTube Manual Fix Script");
console.log("============================");

// Check if we're on YouTube
if (!window.location.hostname.includes('youtube.com')) {
  console.error("❌ Not on YouTube! Navigate to youtube.com first.");
  return;
}

// Check if extension is loaded
if (!window.pipMasterContentLoaded) {
  console.error("❌ PiP Master extension not loaded!");
  console.log("1. Go to chrome://extensions/");
  console.log("2. Make sure PiP Master is enabled");
  console.log("3. Reload the extension");
  console.log("4. Refresh this page");
  return;
}

console.log("✅ Extension loaded, applying YouTube fixes...");

// Fix 1: Force platform detection
if (window.pipMasterInstance) {
  console.log("🔧 Fix 1: Setting platform to YouTube...");
  window.pipMasterInstance.platform = 'youtube';
  console.log("✅ Platform set to YouTube");
}

// Fix 2: Force YouTube detection setup
if (window.pipMasterInstance && window.pipMasterInstance.setupYouTubeDetection) {
  console.log("🔧 Fix 2: Setting up YouTube detection...");
  window.pipMasterInstance.setupYouTubeDetection();
  console.log("✅ YouTube detection setup complete");
}

// Fix 3: Aggressive video scanning
console.log("🔧 Fix 3: Scanning for YouTube videos...");

const youtubeSelectors = [
  "#movie_player video",
  ".html5-video-player video",
  ".video-stream",
  "video.video-stream",
  "video"
];

let foundVideos = [];
youtubeSelectors.forEach(selector => {
  try {
    const videos = document.querySelectorAll(selector);
    videos.forEach(video => {
      if (!foundVideos.includes(video)) {
        foundVideos.push(video);
      }
    });
  } catch (error) {
    console.warn(`Selector ${selector} failed:`, error);
  }
});

console.log(`✅ Found ${foundVideos.length} videos`);

// Fix 4: Force video processing
if (foundVideos.length > 0 && window.pipMasterInstance) {
  console.log("🔧 Fix 4: Processing videos...");
  
  foundVideos.forEach((video, index) => {
    console.log(`Processing video ${index + 1}...`);
    try {
      // Add to videos set directly if needed
      if (!window.pipMasterInstance.videos.has(video)) {
        window.pipMasterInstance.handleVideoFound(video);
      }
    } catch (error) {
      console.warn(`Error processing video ${index + 1}:`, error);
    }
  });
  
  console.log("✅ Video processing complete");
}

// Fix 5: Force overlay creation
setTimeout(() => {
  console.log("🔧 Fix 5: Checking overlays...");
  
  const overlays = document.querySelectorAll('.pip-master-overlay');
  console.log(`Found ${overlays.length} overlays`);
  
  if (overlays.length === 0 && foundVideos.length > 0 && window.pipMasterInstance) {
    console.log("Creating overlays manually...");
    foundVideos.forEach((video, index) => {
      try {
        if (window.pipMasterInstance.createOverlay) {
          window.pipMasterInstance.createOverlay(video);
          console.log(`✅ Overlay created for video ${index + 1}`);
        }
      } catch (error) {
        console.warn(`Error creating overlay for video ${index + 1}:`, error);
      }
    });
  }
}, 1000);

// Fix 6: Test functionality
setTimeout(() => {
  console.log("🔧 Fix 6: Testing functionality...");
  
  const trackedVideos = window.pipMasterInstance?.videos.size || 0;
  const overlayCount = document.querySelectorAll('.pip-master-overlay').length;
  
  console.log(`✅ Videos tracked: ${trackedVideos}`);
  console.log(`✅ Overlays present: ${overlayCount}`);
  
  if (trackedVideos > 0) {
    console.log("🎉 SUCCESS! Extension should now work on YouTube");
    console.log("Look for overlay buttons on videos or try Alt+P");
  } else {
    console.log("❌ Still having issues. Try these commands:");
    console.log("window.pipMasterDebug.scanForVideos()");
    console.log("window.pipMasterDebug.testPiP(0)");
  }
}, 2000);

// Provide manual commands
console.log("\n📋 Manual Commands (if fixes don't work):");
console.log("=========================================");
console.log("");
console.log("// Force video scan");
console.log("window.pipMasterDebug.scanForVideos()");
console.log("");
console.log("// Check video count");
console.log("window.pipMasterDebug.getVideoCount()");
console.log("");
console.log("// List tracked videos");
console.log("window.pipMasterDebug.listVideos()");
console.log("");
console.log("// Test PiP on first video");
console.log("window.pipMasterDebug.testPiP(0)");
console.log("");
console.log("// Manual video processing");
console.log("const video = document.querySelector('#movie_player video');");
console.log("if (video) window.pipMasterInstance.handleVideoFound(video);");
console.log("");
console.log("// Check for overlays");
console.log("document.querySelectorAll('.pip-master-overlay').length");

console.log("\n⏳ Applying fixes... (2 seconds)");
