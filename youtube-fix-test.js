// YouTube Fix Test Script for PiP Master
// Run this on YouTube to diagnose and fix detection issues

console.log("🔧 YouTube Fix Test Script for PiP Master");
console.log("==========================================");

// Step 1: Basic YouTube Detection
console.log("\n1. Basic YouTube Environment Check:");
const isYouTube = window.location.hostname.includes('youtube.com');
const isVideoPage = window.location.pathname.includes('/watch');
console.log("✓ On YouTube:", isYouTube);
console.log("✓ On video page:", isVideoPage);
console.log("✓ URL:", window.location.href);

// Step 2: Extension Loading Check
console.log("\n2. Extension Loading Check:");
console.log("✓ Extension loaded:", !!window.pipMasterContentLoaded);
console.log("✓ Instance available:", !!window.pipMasterInstance);
console.log("✓ Debug functions:", !!window.pipMasterDebug);

if (!window.pipMasterContentLoaded) {
  console.error("❌ Extension not loaded! Check chrome://extensions/");
  console.log("Stop here and fix extension loading first.");
  return;
}

// Step 3: Platform Detection Check
console.log("\n3. Platform Detection Check:");
const detectedPlatform = window.pipMasterInstance?.platform;
console.log("✓ Detected platform:", detectedPlatform);

if (detectedPlatform !== 'youtube') {
  console.warn("⚠️ Platform not detected as YouTube!");
  console.log("Forcing platform to YouTube...");
  if (window.pipMasterInstance) {
    window.pipMasterInstance.platform = 'youtube';
    console.log("✓ Platform forced to YouTube");
  }
}

// Step 4: Comprehensive YouTube Video Detection
console.log("\n4. Comprehensive YouTube Video Detection:");

// All possible YouTube selectors
const allYouTubeSelectors = [
  "#movie_player video",
  ".html5-video-player video",
  ".video-stream",
  "video.video-stream",
  "#player video",
  ".ytp-html5-video",
  "video[src*='googlevideo']",
  "video[src*='youtube']",
  ".ytp-html5-video-container video",
  "#ytd-player video",
  "ytd-player video",
  ".player-container video",
  "video" // Basic fallback
];

let allFoundVideos = [];
let workingSelectors = [];

allYouTubeSelectors.forEach((selector, index) => {
  try {
    const videos = document.querySelectorAll(selector);
    console.log(`  ${index + 1}. "${selector}": ${videos.length} videos`);
    
    if (videos.length > 0) {
      workingSelectors.push(selector);
      videos.forEach(video => {
        if (!allFoundVideos.includes(video)) {
          allFoundVideos.push(video);
        }
      });
    }
  } catch (error) {
    console.warn(`  ${index + 1}. "${selector}": ERROR - ${error.message}`);
  }
});

console.log(`✓ Working selectors: ${workingSelectors.length}`);
console.log(`✓ Total unique videos found: ${allFoundVideos.length}`);

// Step 5: Analyze Found Videos
console.log("\n5. Video Analysis:");
if (allFoundVideos.length === 0) {
  console.error("❌ No videos found! This is the main issue.");
  console.log("Possible causes:");
  console.log("- Not on a video page");
  console.log("- Video still loading");
  console.log("- YouTube changed their structure");
  console.log("- Ad blocker interference");
} else {
  allFoundVideos.forEach((video, index) => {
    console.log(`\nVideo ${index + 1}:`);
    console.log("  Element:", video);
    console.log("  Source:", video.src || video.currentSrc || "no src");
    console.log("  Ready state:", video.readyState);
    console.log("  Dimensions:", `${video.videoWidth || '?'}x${video.videoHeight || '?'}`);
    console.log("  Duration:", video.duration || 'unknown');
    console.log("  Paused:", video.paused);
    console.log("  Ended:", video.ended);
    console.log("  PiP disabled:", video.disablePictureInPicture);
    
    // Check parent containers
    const moviePlayer = video.closest('#movie_player');
    const htmlPlayer = video.closest('.html5-video-player');
    console.log("  In #movie_player:", !!moviePlayer);
    console.log("  In .html5-video-player:", !!htmlPlayer);
    
    // Check for ad indicators
    const adContainer = video.closest('.ad-showing, .video-ads, [class*="ad-"]');
    console.log("  In ad container:", !!adContainer);
  });
}

// Step 6: Force Extension Detection
console.log("\n6. Force Extension Detection:");
if (window.pipMasterInstance && allFoundVideos.length > 0) {
  console.log("Forcing extension to detect videos...");
  
  // Clear existing tracking
  const originalSize = window.pipMasterInstance.videos.size;
  console.log("Videos tracked before:", originalSize);
  
  // Force YouTube setup
  if (window.pipMasterInstance.setupYouTubeDetection) {
    console.log("Running setupYouTubeDetection...");
    window.pipMasterInstance.setupYouTubeDetection();
  }
  
  // Force universal scan
  console.log("Running performUniversalVideoScan...");
  window.pipMasterInstance.performUniversalVideoScan();
  
  // Manually process each video
  console.log("Manually processing each video...");
  allFoundVideos.forEach((video, index) => {
    console.log(`Processing video ${index + 1}...`);
    try {
      window.pipMasterInstance.handleVideoFound(video);
    } catch (error) {
      console.error(`Error processing video ${index + 1}:`, error);
    }
  });
  
  setTimeout(() => {
    const newSize = window.pipMasterInstance.videos.size;
    console.log("Videos tracked after:", newSize);
    console.log("New videos added:", newSize - originalSize);
    
    if (newSize > originalSize) {
      console.log("✅ Success! Extension now tracking videos.");
    } else {
      console.log("❌ Extension still not tracking videos.");
      console.log("Check console for handleVideoFound errors.");
    }
  }, 1000);
}

// Step 7: Check for Overlays
console.log("\n7. Overlay Detection:");
setTimeout(() => {
  const overlays = document.querySelectorAll('.pip-master-overlay');
  console.log("✓ Overlays found:", overlays.length);
  
  if (overlays.length === 0) {
    console.warn("⚠️ No overlays found!");
    console.log("This means videos aren't being processed or overlay creation failed.");
  } else {
    overlays.forEach((overlay, i) => {
      const style = getComputedStyle(overlay);
      console.log(`Overlay ${i + 1}:`, {
        display: style.display,
        visibility: style.visibility,
        opacity: style.opacity,
        position: style.position,
        zIndex: style.zIndex
      });
    });
  }
}, 2000);

// Step 8: Test PiP Functionality
console.log("\n8. PiP Functionality Test:");
setTimeout(() => {
  if (allFoundVideos.length > 0) {
    const testVideo = allFoundVideos[0];
    console.log("Testing PiP on first video...");
    
    testVideo.requestPictureInPicture()
      .then(() => {
        console.log("✅ PiP test successful!");
        setTimeout(() => {
          if (document.pictureInPictureElement) {
            document.exitPictureInPicture();
            console.log("✓ PiP exited");
          }
        }, 2000);
      })
      .catch(error => {
        console.error("❌ PiP test failed:", error.message);
        
        // Specific YouTube error analysis
        if (error.message.includes('policy')) {
          console.log("💡 YouTube policy restriction - try different video");
        } else if (error.message.includes('disabled')) {
          console.log("💡 PiP disabled on this video");
        } else if (error.message.includes('state')) {
          console.log("💡 Video not ready - wait for it to load");
        } else if (error.message.includes('NotAllowed')) {
          console.log("💡 Browser or site policy blocking PiP");
        }
      });
  }
}, 3000);

// Step 9: YouTube-Specific Commands
console.log("\n9. YouTube Debug Commands:");
console.log("Run these commands for further debugging:");
console.log("");
console.log("// Force video detection");
console.log("window.pipMasterDebug.scanForVideos()");
console.log("");
console.log("// Check tracked videos");
console.log("window.pipMasterDebug.getVideoCount()");
console.log("window.pipMasterDebug.listVideos()");
console.log("");
console.log("// Test PiP on specific video");
console.log("window.pipMasterDebug.testPiP(0)");
console.log("");
console.log("// Check YouTube player state");
console.log("document.querySelector('#movie_player')?.className");
console.log("");
console.log("// Force YouTube detection");
console.log("window.pipMasterInstance?.setupYouTubeDetection()");

// Summary
setTimeout(() => {
  console.log("\n📊 YouTube Fix Test Summary:");
  console.log("============================");
  console.log("YouTube page:", isYouTube ? "✅" : "❌");
  console.log("Video page:", isVideoPage ? "✅" : "❌");
  console.log("Extension loaded:", window.pipMasterContentLoaded ? "✅" : "❌");
  console.log("Platform detected:", detectedPlatform || "❌");
  console.log("Videos found:", allFoundVideos.length);
  console.log("Working selectors:", workingSelectors.length);
  console.log("Videos tracked:", window.pipMasterInstance?.videos.size || 0);
  console.log("Overlays present:", document.querySelectorAll('.pip-master-overlay').length);
  
  console.log("\n🔧 Next Steps:");
  if (allFoundVideos.length === 0) {
    console.log("1. ❌ NO VIDEOS FOUND - Main issue!");
    console.log("   - Make sure you're on a YouTube video page");
    console.log("   - Try refreshing the page");
    console.log("   - Check if video is still loading");
  } else if (window.pipMasterInstance?.videos.size === 0) {
    console.log("1. ⚠️ VIDEOS FOUND BUT NOT TRACKED");
    console.log("   - Check console for handleVideoFound errors");
    console.log("   - Videos might be failing suitability checks");
    console.log("   - Try: window.pipMasterDebug.scanForVideos()");
  } else {
    console.log("1. ✅ VIDEOS DETECTED AND TRACKED");
    console.log("   - Look for overlay buttons on videos");
    console.log("   - Try Alt+P keyboard shortcut");
    console.log("   - Extension should be working!");
  }
}, 4000);

console.log("\n⏳ Running YouTube fix tests... (4 seconds)");
console.log("Watch the console for detailed results...");
