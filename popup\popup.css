/* PiP Master Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background: #ffffff;
  width: 320px;
  min-height: 400px;
}

.popup-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Header */
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo h1 {
  font-size: 18px;
  font-weight: 600;
}

.version {
  font-size: 12px;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
}

/* Main Content */
.popup-main {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* Quick Actions */
.quick-actions {
  margin-bottom: 20px;
}

.action-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background: #4285f4;
  color: white;
}

.action-btn.primary:hover {
  background: #3367d6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
}

.action-btn:active {
  transform: translateY(0);
}

/* Status Section */
.status-section {
  margin-bottom: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  color: #666;
  font-size: 13px;
}

.status-value {
  font-weight: 500;
  font-size: 13px;
}

/* Quick Settings */
.quick-settings {
  margin-bottom: 20px;
}

.quick-settings h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.setting-item {
  margin-bottom: 12px;
}

.setting-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  position: relative;
  padding-left: 28px;
}

.setting-label input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  left: 0;
  top: 0;
}

.checkmark {
  position: absolute;
  left: 0;
  top: 2px;
  height: 18px;
  width: 18px;
  background-color: #eee;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.setting-label:hover input ~ .checkmark {
  background-color: #ddd;
}

.setting-label input:checked ~ .checkmark {
  background-color: #4285f4;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 6px;
  top: 3px;
  width: 5px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.setting-label input:checked ~ .checkmark:after {
  display: block;
}

.setting-label-text {
  display: block;
  font-size: 14px;
  margin-bottom: 6px;
  color: #333;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider-container input[type="range"] {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #ddd;
  outline: none;
  -webkit-appearance: none;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4285f4;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-container input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4285f4;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-value {
  font-size: 12px;
  color: #666;
  min-width: 30px;
  text-align: right;
}

.setting-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  cursor: pointer;
}

.setting-select:focus {
  outline: none;
  border-color: #4285f4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

/* Shortcuts Section */
.shortcuts-section {
  margin-bottom: 20px;
}

.shortcuts-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.shortcut-item:last-child {
  border-bottom: none;
}

.shortcut-key {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: #f1f3f4;
  padding: 4px 8px;
  border-radius: 4px;
  color: #333;
}

.shortcut-desc {
  font-size: 13px;
  color: #666;
}

/* Footer */
.popup-footer {
  display: flex;
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #eee;
}

.footer-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 13px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.footer-btn:hover {
  background: #e8eaed;
  color: #333;
}

.footer-btn + .footer-btn {
  margin-left: 8px;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  body {
    background: #1a1a1a;
    color: #e0e0e0;
  }

  .status-section {
    background: #2d2d2d;
  }

  .status-label {
    color: #aaa;
  }

  .checkmark {
    background-color: #444;
  }

  .setting-label:hover input ~ .checkmark {
    background-color: #555;
  }

  .setting-select {
    background: #2d2d2d;
    border-color: #444;
    color: #e0e0e0;
  }

  .shortcut-key {
    background: #2d2d2d;
    color: #e0e0e0;
  }

  .shortcut-desc {
    color: #aaa;
  }

  .popup-footer {
    background: #2d2d2d;
    border-top-color: #444;
  }

  .footer-btn {
    color: #aaa;
  }

  .footer-btn:hover {
    background: #3d3d3d;
    color: #e0e0e0;
  }
}

/* Accessibility */
.action-btn:focus,
.setting-select:focus,
.footer-btn:focus {
  outline: 2px solid #4285f4;
  outline-offset: 2px;
}

.setting-label:focus-within .checkmark {
  box-shadow: 0 0 0 2px #4285f4;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.popup-container {
  animation: fadeIn 0.2s ease-out;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
