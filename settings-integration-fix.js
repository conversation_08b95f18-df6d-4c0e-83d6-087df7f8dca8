// Settings Integration Fix Script
// Targeted fixes for missing settings and broken integration

console.log("🔧 Settings Integration Fix");
console.log("===========================");

// Settings Integration Fixer
class SettingsIntegrationFixer {
  constructor() {
    this.runFixes();
  }

  runFixes() {
    console.log("🚀 Applying settings integration fixes...");
    
    this.fixSettingsObject();
    this.fixSettingsPanelHTML();
    this.addMissingMethods();
    this.fixTimelineIntegration();
    this.updateFormHandling();
    this.testIntegration();
  }

  fixSettingsObject() {
    console.log("\n1️⃣ Fixing Settings Object");
    console.log("==========================");
    
    if (!window.pipMasterInstance?.settingsPanel) {
      console.error("❌ Settings panel not available");
      return;
    }
    
    const settingsPanel = window.pipMasterInstance.settingsPanel;
    
    // Add missing settings to the settings object
    if (!('audioControlEnabled' in settingsPanel.settings)) {
      settingsPanel.settings.audioControlEnabled = true;
      console.log("✅ Added audioControlEnabled setting (default: true)");
    }
    
    if (!('timelinePreviewEnabled' in settingsPanel.settings)) {
      settingsPanel.settings.timelinePreviewEnabled = true;
      console.log("✅ Added timelinePreviewEnabled setting (default: true)");
    }
    
    // Update getDefaultSettings method
    const originalGetDefaultSettings = settingsPanel.getDefaultSettings;
    settingsPanel.getDefaultSettings = function() {
      const defaults = originalGetDefaultSettings ? originalGetDefaultSettings.call(this) : {
        autoEnable: false,
        autoExitOnTabReturn: true,
        overlayTheme: 'default',
        lowPowerMode: false,
        scanFrequency: 'normal',
        highContrast: false,
        reducedMotion: false,
        screenReaderMode: false,
        volumeControlEnabled: true,
        speedControlEnabled: true,
        skipControlEnabled: true,
        rememberSitePreferences: true,
        autoApplySiteSettings: true
      };
      
      // Add new settings
      defaults.audioControlEnabled = true;
      defaults.timelinePreviewEnabled = true;
      
      return defaults;
    };
    
    console.log("✅ Updated getDefaultSettings method");
  }

  fixSettingsPanelHTML() {
    console.log("\n2️⃣ Fixing Settings Panel HTML");
    console.log("==============================");
    
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    if (!settingsPanel) return;
    
    // Override generatePanelHTML to include new settings
    const originalGeneratePanelHTML = settingsPanel.generatePanelHTML;
    
    settingsPanel.generatePanelHTML = function() {
      return `
        <div style="
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: white;
          border-radius: 12px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
          padding: 24px;
          width: 480px;
          max-height: 80vh;
          overflow-y: auto;
          z-index: 10005;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        ">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h2 style="margin: 0; color: #333; font-size: 20px;">⚙️ PiP Master Settings</h2>
            <button id="pip-settings-close" style="
              background: none;
              border: none;
              font-size: 24px;
              cursor: pointer;
              color: #666;
              padding: 0;
              width: 32px;
              height: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 50%;
            ">×</button>
          </div>

          <!-- Smart Auto-PiP Section -->
          <div class="settings-section" style="margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">🔄 Smart Auto-PiP</h3>
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="auto-enable" ${this.settings.autoEnable ? 'checked' : ''} style="margin-right: 8px;">
              Enable automatic PiP on tab switch
            </label>
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="auto-exit" ${this.settings.autoExitOnTabReturn ? 'checked' : ''} style="margin-right: 8px;">
              Auto-exit PiP when returning to tab
            </label>
          </div>

          <!-- Themes Section -->
          <div class="settings-section" style="margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">🎨 Overlay Themes</h3>
            <select id="overlay-theme" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
              <option value="default" ${this.settings.overlayTheme === 'default' ? 'selected' : ''}>Default</option>
              <option value="minimal" ${this.settings.overlayTheme === 'minimal' ? 'selected' : ''}>Minimal</option>
              <option value="neon" ${this.settings.overlayTheme === 'neon' ? 'selected' : ''}>Neon</option>
              <option value="dark" ${this.settings.overlayTheme === 'dark' ? 'selected' : ''}>Dark Pro</option>
              <option value="youtube" ${this.settings.overlayTheme === 'youtube' ? 'selected' : ''}>YouTube Style</option>
            </select>
          </div>

          <!-- Performance Section -->
          <div class="settings-section" style="margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">⚡ Performance</h3>
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="low-power" ${this.settings.lowPowerMode ? 'checked' : ''} style="margin-right: 8px;">
              Low power mode (battery saving)
            </label>
            <div style="margin-bottom: 8px;">
              <label style="display: block; margin-bottom: 4px; color: #666; font-size: 14px;">Scan frequency:</label>
              <select id="scan-frequency" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="minimal" ${this.settings.scanFrequency === 'minimal' ? 'selected' : ''}>Minimal</option>
                <option value="normal" ${this.settings.scanFrequency === 'normal' ? 'selected' : ''}>Normal</option>
                <option value="aggressive" ${this.settings.scanFrequency === 'aggressive' ? 'selected' : ''}>Aggressive</option>
              </select>
            </div>
          </div>

          <!-- Accessibility Section -->
          <div class="settings-section" style="margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">♿ Accessibility</h3>
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="high-contrast" ${this.settings.highContrast ? 'checked' : ''} style="margin-right: 8px;">
              High contrast mode
            </label>
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="reduced-motion" ${this.settings.reducedMotion ? 'checked' : ''} style="margin-right: 8px;">
              Reduced motion
            </label>
          </div>

          <!-- Advanced Controls Section -->
          <div class="settings-section" style="margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">🎮 PiP Window Controls</h3>
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="volume-control" ${this.settings.volumeControlEnabled ? 'checked' : ''} style="margin-right: 8px;">
              Volume control (Ctrl + ↑/↓)
            </label>
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="speed-control" ${this.settings.speedControlEnabled ? 'checked' : ''} style="margin-right: 8px;">
              Playback speed control (Shift + &lt;/&gt;)
            </label>
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="skip-control" ${this.settings.skipControlEnabled ? 'checked' : ''} style="margin-right: 8px;">
              Skip controls (Ctrl + ←/→)
            </label>
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="audio-control-enabled" ${this.settings.audioControlEnabled ? 'checked' : ''} style="margin-right: 8px;">
              Enable audio in Picture-in-Picture mode
            </label>
          </div>

          <!-- Timeline Controls Section -->
          <div class="settings-section" style="margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">⏯️ Timeline Controls</h3>
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="timeline-preview-enabled" ${this.settings.timelinePreviewEnabled ? 'checked' : ''} style="margin-right: 8px;">
              Enable timeline preview on hover
            </label>
            <div style="font-size: 12px; color: #666; margin-left: 24px; line-height: 1.4;">
              When enabled, timeline controls appear when hovering near the bottom of the PiP window
            </div>
          </div>

          <!-- Site Preferences Section -->
          <div class="settings-section" style="margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">🌐 Site Preferences</h3>
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="remember-sites" ${this.settings.rememberSitePreferences ? 'checked' : ''} style="margin-right: 8px;">
              Remember site-specific preferences
            </label>
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="auto-apply-sites" ${this.settings.autoApplySiteSettings ? 'checked' : ''} style="margin-right: 8px;">
              Auto-apply site settings
            </label>
          </div>

          <!-- Action Buttons -->
          <div style="display: flex; gap: 12px; margin-top: 24px;">
            <button id="pip-settings-save" style="
              flex: 1;
              background: #007bff;
              color: white;
              border: none;
              padding: 12px 24px;
              border-radius: 6px;
              cursor: pointer;
              font-size: 14px;
              font-weight: 500;
            ">Save Settings</button>
            <button id="pip-settings-reset" style="
              background: #6c757d;
              color: white;
              border: none;
              padding: 12px 24px;
              border-radius: 6px;
              cursor: pointer;
              font-size: 14px;
            ">Reset to Defaults</button>
          </div>

          <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #eee; font-size: 12px; color: #666; text-align: center;">
            PiP Master Enhanced v2.0 • Press Alt+S to toggle this panel
          </div>
        </div>
      `;
    };
    
    console.log("✅ Updated generatePanelHTML method with new settings");
  }

  addMissingMethods() {
    console.log("\n3️⃣ Adding Missing Methods");
    console.log("==========================");
    
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    if (!settingsPanel) return;
    
    // Add applyAudioControlSettings method
    if (!settingsPanel.applyAudioControlSettings) {
      settingsPanel.applyAudioControlSettings = function() {
        if (this.settings.audioControlEnabled) {
          console.log("🔊 Audio Control: ENABLED - Audio will be preserved during PiP transitions");
          this.setupAudioControlListeners(true);
        } else {
          console.log("🔇 Audio Control: DISABLED - Audio will be muted during PiP mode");
          this.setupAudioControlListeners(false);
        }
      };
      console.log("✅ Added applyAudioControlSettings method");
    }
    
    // Add setupAudioControlListeners method
    if (!settingsPanel.setupAudioControlListeners) {
      settingsPanel.setupAudioControlListeners = function(enableAudio) {
        // Remove existing listeners
        document.removeEventListener('enterpictureinpicture', this.pipEnterAudioHandler);
        document.removeEventListener('leavepictureinpicture', this.pipLeaveAudioHandler);

        if (enableAudio) {
          this.pipEnterAudioHandler = (event) => {
            const video = event.target;
            video._pipMasterOriginalVolume = video.volume;
            video._pipMasterOriginalMuted = video.muted;
            console.log("🔊 Audio preserved in PiP mode");
          };

          this.pipLeaveAudioHandler = (event) => {
            const video = event.target;
            if (video._pipMasterOriginalVolume !== undefined) {
              video.volume = video._pipMasterOriginalVolume;
              video.muted = video._pipMasterOriginalMuted;
              delete video._pipMasterOriginalVolume;
              delete video._pipMasterOriginalMuted;
              console.log("🔊 Audio state restored after PiP");
            }
          };
        } else {
          this.pipEnterAudioHandler = (event) => {
            const video = event.target;
            video._pipMasterOriginalVolume = video.volume;
            video._pipMasterOriginalMuted = video.muted;
            video.muted = true;
            console.log("🔇 Audio muted in PiP mode");
          };

          this.pipLeaveAudioHandler = (event) => {
            const video = event.target;
            if (video._pipMasterOriginalVolume !== undefined) {
              video.volume = video._pipMasterOriginalVolume;
              video.muted = video._pipMasterOriginalMuted;
              delete video._pipMasterOriginalVolume;
              delete video._pipMasterOriginalMuted;
              console.log("🔊 Audio restored after PiP");
            }
          };
        }

        document.addEventListener('enterpictureinpicture', this.pipEnterAudioHandler);
        document.addEventListener('leavepictureinpicture', this.pipLeaveAudioHandler);
      };
      console.log("✅ Added setupAudioControlListeners method");
    }
    
    // Add applyTimelinePreviewSettings method
    if (!settingsPanel.applyTimelinePreviewSettings) {
      settingsPanel.applyTimelinePreviewSettings = function() {
        if (this.pipMaster.timelineControl) {
          if (this.settings.timelinePreviewEnabled) {
            if (this.pipMaster.timelineControl.enableHoverPreview) {
              this.pipMaster.timelineControl.enableHoverPreview();
              console.log("⏯️ Timeline preview on hover enabled");
            }
          } else {
            if (this.pipMaster.timelineControl.disableHoverPreview) {
              this.pipMaster.timelineControl.disableHoverPreview();
              console.log("⏯️ Timeline preview on hover disabled");
            }
          }
        }
      };
      console.log("✅ Added applyTimelinePreviewSettings method");
    }
    
    // Update applySettings method to include new settings
    const originalApplySettings = settingsPanel.applySettings;
    settingsPanel.applySettings = function() {
      // Call original apply settings
      if (originalApplySettings) {
        originalApplySettings.call(this);
      }
      
      // Apply new settings
      this.applyAudioControlSettings();
      this.applyTimelinePreviewSettings();
      
      console.log("✅ All settings applied (including new audio and timeline settings)");
    };
    console.log("✅ Updated applySettings method");
  }

  fixTimelineIntegration() {
    console.log("\n4️⃣ Fixing Timeline Integration");
    console.log("===============================");
    
    const timelineControl = window.pipMasterInstance?.timelineControl;
    if (!timelineControl) {
      console.log("⚠️ Timeline control not available - initializing if possible");
      if (window.initializeTimelineControl) {
        window.initializeTimelineControl();
        console.log("✅ Timeline control initialized");
      }
      return;
    }
    
    // Add enableHoverPreview method if missing
    if (!timelineControl.enableHoverPreview) {
      timelineControl.enableHoverPreview = function() {
        console.log("⏯️ Enabling timeline hover preview");
        this.hoverPreviewEnabled = true;
        if (this.setupHoverListeners) {
          this.setupHoverListeners();
        }
      };
      console.log("✅ Added enableHoverPreview method");
    }
    
    // Add disableHoverPreview method if missing
    if (!timelineControl.disableHoverPreview) {
      timelineControl.disableHoverPreview = function() {
        console.log("⏯️ Disabling timeline hover preview");
        this.hoverPreviewEnabled = false;
        if (this.setupHoverListeners) {
          this.setupHoverListeners();
        }
        if (this.timelineElement && this.timelineElement.style.opacity === '1') {
          this.hideTimeline();
        }
      };
      console.log("✅ Added disableHoverPreview method");
    }
  }

  updateFormHandling() {
    console.log("\n5️⃣ Updating Form Handling");
    console.log("===========================");
    
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    if (!settingsPanel) return;
    
    // Update saveCurrentSettings method
    const originalSaveCurrentSettings = settingsPanel.saveCurrentSettings;
    settingsPanel.saveCurrentSettings = function() {
      if (originalSaveCurrentSettings) {
        originalSaveCurrentSettings.call(this);
      }
      
      // Save new settings
      const audioElement = this.panel.querySelector("#audio-control-enabled");
      const timelineElement = this.panel.querySelector("#timeline-preview-enabled");
      
      if (audioElement) {
        this.settings.audioControlEnabled = audioElement.checked;
      }
      if (timelineElement) {
        this.settings.timelinePreviewEnabled = timelineElement.checked;
      }
      
      console.log("✅ New settings saved from form");
    };
    
    // Update updatePanelFromSettings method
    const originalUpdatePanelFromSettings = settingsPanel.updatePanelFromSettings;
    settingsPanel.updatePanelFromSettings = function() {
      if (originalUpdatePanelFromSettings) {
        originalUpdatePanelFromSettings.call(this);
      }
      
      // Update new form elements
      const audioElement = this.panel.querySelector("#audio-control-enabled");
      const timelineElement = this.panel.querySelector("#timeline-preview-enabled");
      
      if (audioElement) {
        audioElement.checked = this.settings.audioControlEnabled;
      }
      if (timelineElement) {
        timelineElement.checked = this.settings.timelinePreviewEnabled;
      }
      
      console.log("✅ New form elements updated from settings");
    };
    
    console.log("✅ Updated form handling methods");
  }

  testIntegration() {
    console.log("\n6️⃣ Testing Integration");
    console.log("=======================");
    
    try {
      // Recreate settings panel with new elements
      const existingPanel = document.getElementById('pip-master-settings-panel');
      if (existingPanel) {
        existingPanel.remove();
      }
      
      window.pipMasterInstance.settingsPanel.createSettingsPanel();
      console.log("✅ Settings panel recreated");
      
      // Test opening panel
      window.pipMasterInstance.settingsPanel.showPanel();
      
      // Check for new elements
      const audioElement = document.getElementById('audio-control-enabled');
      const timelineElement = document.getElementById('timeline-preview-enabled');
      
      console.log(`Audio control element: ${audioElement ? '✅ Found' : '❌ Missing'}`);
      console.log(`Timeline preview element: ${timelineElement ? '✅ Found' : '❌ Missing'}`);
      
      if (audioElement && timelineElement) {
        console.log("🎉 New settings are now visible in the panel!");
      }
      
      window.pipMasterInstance.settingsPanel.hidePanel();
      
    } catch (error) {
      console.error("❌ Integration test failed:", error);
    }
  }
}

// Quick fix function
window.quickFixNewSettings = function() {
  console.log("⚡ Quick Fix for New Settings");
  console.log("=============================");
  
  new SettingsIntegrationFixer();
  
  // Also restore YouTube functionality
  if (window.restoreYouTubeFunctionality) {
    window.restoreYouTubeFunctionality();
  }
  
  console.log("\n🎉 Quick fix complete!");
  console.log("Try opening settings panel with Alt+S");
};

// Auto-run the fix
console.log("🚀 Auto-running settings integration fix...");
window.settingsIntegrationFixer = new SettingsIntegrationFixer();

console.log("\n📋 Available Commands:");
console.log("======================");
console.log("quickFixNewSettings()         - Apply all fixes quickly");
console.log("new SettingsIntegrationFixer() - Run comprehensive fix");
