// PiP Master Enhanced - Complete Integration Script
// Loads and integrates all enhanced features

console.log("🚀 PiP Master Enhanced - Complete Integration");
console.log("==============================================");

// Enhanced Feature Loader
class EnhancedFeatureLoader {
  constructor() {
    this.features = [];
    this.loadOrder = [
      'enhanced-features.js',
      'site-preferences.js', 
      'performance-optimizations.js',
      'settings-panel.js'
    ];
    this.initializationCallbacks = [];
  }

  async loadAllFeatures() {
    console.log("📦 Loading enhanced features...");
    
    try {
      // Wait for PiP Master to be ready
      await this.waitForPipMaster();
      
      // Load all enhancement scripts
      await this.loadEnhancementScripts();
      
      // Initialize all features
      await this.initializeAllFeatures();
      
      // Setup integration
      this.setupFeatureIntegration();
      
      console.log("🎉 All enhanced features loaded successfully!");
      this.showWelcomeMessage();
      
    } catch (error) {
      console.error("❌ Failed to load enhanced features:", error);
    }
  }

  async waitForPipMaster() {
    return new Promise((resolve) => {
      if (window.pipMasterInstance) {
        resolve();
        return;
      }
      
      const checkInterval = setInterval(() => {
        if (window.pipMasterInstance) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
      
      // Timeout after 10 seconds
      setTimeout(() => {
        clearInterval(checkInterval);
        if (!window.pipMasterInstance) {
          console.warn("⚠️ PiP Master not found, creating mock instance for testing");
          window.pipMasterInstance = { settings: {} };
        }
        resolve();
      }, 10000);
    });
  }

  async loadEnhancementScripts() {
    // In a real extension, these would be loaded via chrome.runtime.getURL
    // For testing, we assume they're already loaded
    console.log("✅ Enhancement scripts ready");
  }

  async initializeAllFeatures() {
    console.log("🔧 Initializing enhanced features...");
    
    // Initialize features in order
    const initFunctions = [
      () => window.initializeEnhancedFeatures?.(),
      () => window.initializeSiteFeatures?.(),
      () => window.initializeAdvancedFeatures?.(),
      () => window.initializeSettingsPanel?.()
    ];
    
    for (const initFn of initFunctions) {
      try {
        if (initFn) {
          await initFn();
        }
      } catch (error) {
        console.warn("Feature initialization failed:", error);
      }
    }
  }

  setupFeatureIntegration() {
    console.log("🔗 Setting up feature integration...");
    
    // Integrate Smart Auto-PiP with Site Preferences
    this.integrateAutoPiPWithSitePrefs();
    
    // Integrate Performance Optimizer with Settings
    this.integratePerformanceWithSettings();
    
    // Integrate Accessibility with System Preferences
    this.integrateAccessibilityFeatures();
    
    // Setup global keyboard shortcuts
    this.setupGlobalShortcuts();
    
    // Setup feature status monitoring
    this.setupStatusMonitoring();
  }

  integrateAutoPiPWithSitePrefs() {
    if (window.pipMasterInstance.smartAutoPiP && window.pipMasterInstance.sitePreferences) {
      // Auto-enable Smart PiP for frequently used sites
      const sitePrefs = window.pipMasterInstance.sitePreferences.getSitePreferences();
      if (sitePrefs.usageCount > 3) {
        window.pipMasterInstance.smartAutoPiP.enable();
        console.log("🎯 Auto-enabled Smart PiP based on site usage");
      }
    }
  }

  integratePerformanceWithSettings() {
    if (window.pipMasterInstance.performanceOptimizer && window.pipMasterInstance.settingsPanel) {
      // Apply performance settings
      const settings = window.pipMasterInstance.settingsPanel.settings;
      if (settings.lowPowerMode) {
        window.pipMasterInstance.performanceOptimizer.enableLowPowerMode();
      }
    }
  }

  integrateAccessibilityFeatures() {
    if (window.pipMasterInstance.accessibility) {
      // Auto-apply system accessibility preferences
      window.pipMasterInstance.accessibility.detectSystemPreferences();
    }
  }

  setupGlobalShortcuts() {
    document.addEventListener('keydown', (event) => {
      // Global shortcuts that work across all features
      if (event.altKey && event.key === 'h') {
        event.preventDefault();
        this.showHelpDialog();
      }
      
      if (event.altKey && event.key === 'i') {
        event.preventDefault();
        this.showFeatureInfo();
      }
    });
  }

  setupStatusMonitoring() {
    // Monitor feature health and performance
    setInterval(() => {
      this.checkFeatureHealth();
    }, 30000); // Every 30 seconds
  }

  checkFeatureHealth() {
    const health = {
      smartAutoPiP: !!window.pipMasterInstance.smartAutoPiP,
      themeManager: !!window.pipMasterInstance.themeManager,
      accessibility: !!window.pipMasterInstance.accessibility,
      performanceOptimizer: !!window.pipMasterInstance.performanceOptimizer,
      sitePreferences: !!window.pipMasterInstance.sitePreferences,
      settingsPanel: !!window.pipMasterInstance.settingsPanel
    };
    
    const healthyFeatures = Object.values(health).filter(Boolean).length;
    const totalFeatures = Object.keys(health).length;
    
    if (healthyFeatures < totalFeatures) {
      console.warn(`⚠️ Feature health: ${healthyFeatures}/${totalFeatures} features active`);
    }
  }

  showWelcomeMessage() {
    const message = document.createElement('div');
    message.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      z-index: 10004;
      max-width: 300px;
      animation: slideIn 0.3s ease-out;
    `;
    
    message.innerHTML = `
      <div style="font-weight: bold; margin-bottom: 8px;">🎉 PiP Master Enhanced</div>
      <div style="font-size: 12px; opacity: 0.9; line-height: 1.4;">
        All enhanced features loaded!<br>
        Press <strong>Alt + S</strong> for settings<br>
        Press <strong>Alt + H</strong> for help
      </div>
    `;
    
    // Add animation
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(message);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (message.parentElement) {
        message.style.animation = 'slideIn 0.3s ease-out reverse';
        setTimeout(() => {
          if (message.parentElement) {
            message.parentElement.removeChild(message);
          }
        }, 300);
      }
    }, 5000);
  }

  showHelpDialog() {
    const helpDialog = document.createElement('div');
    helpDialog.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      z-index: 10005;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 500px;
      max-height: 80vh;
      overflow-y: auto;
    `;
    
    helpDialog.innerHTML = `
      <div style="padding: 20px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 15px;">
          <h2 style="margin: 0; color: #333;">🎬 PiP Master Enhanced - Help</h2>
          <button onclick="this.closest('div').parentElement.remove()" style="background: none; border: none; font-size: 20px; cursor: pointer;">×</button>
        </div>
        
        <div style="font-size: 14px; line-height: 1.6; color: #333;">
          <h3 style="color: #444; margin-top: 0;">🔄 Smart Auto-PiP</h3>
          <p>Automatically activates Picture-in-Picture when you switch tabs. Perfect for multitasking!</p>
          
          <h3 style="color: #444;">🎮 Advanced PiP Controls</h3>
          <ul style="margin: 0; padding-left: 20px;">
            <li><strong>Ctrl + ↑/↓:</strong> Volume control</li>
            <li><strong>Shift + &lt;/&gt;:</strong> Playback speed</li>
            <li><strong>Ctrl + ←/→:</strong> Skip 10 seconds</li>
            <li><strong>Ctrl + =:</strong> Reset speed to 1x</li>
            <li><strong>Space:</strong> Play/pause</li>
          </ul>
          
          <h3 style="color: #444;">🎨 Themes & Customization</h3>
          <p>Choose from 5 overlay themes: Default, Minimal, Neon, Dark Pro, and YouTube Style.</p>
          
          <h3 style="color: #444;">⚡ Performance Features</h3>
          <p>Automatic battery optimization, intelligent scanning, and memory management.</p>
          
          <h3 style="color: #444;">♿ Accessibility</h3>
          <p>High contrast mode, reduced motion, screen reader support, and keyboard navigation.</p>
          
          <h3 style="color: #444;">🌐 Site Preferences</h3>
          <p>Remembers your preferences for each website and applies them automatically.</p>
          
          <h3 style="color: #444;">⌨️ Keyboard Shortcuts</h3>
          <ul style="margin: 0; padding-left: 20px;">
            <li><strong>Alt + P:</strong> Toggle Picture-in-Picture</li>
            <li><strong>Alt + S:</strong> Open Settings</li>
            <li><strong>Alt + H:</strong> Show this help</li>
            <li><strong>Alt + I:</strong> Show feature info</li>
            <li><strong>Escape:</strong> Close dialogs/Exit PiP</li>
          </ul>
        </div>
      </div>
    `;
    
    document.body.appendChild(helpDialog);
  }

  showFeatureInfo() {
    const features = {
      'Smart Auto-PiP': window.pipMasterInstance.smartAutoPiP ? '✅' : '❌',
      'Theme Manager': window.pipMasterInstance.themeManager ? '✅' : '❌',
      'Advanced Controls': window.pipMasterInstance.advancedControls ? '✅' : '❌',
      'Site Preferences': window.pipMasterInstance.sitePreferences ? '✅' : '❌',
      'Accessibility': window.pipMasterInstance.accessibility ? '✅' : '❌',
      'Performance Optimizer': window.pipMasterInstance.performanceOptimizer ? '✅' : '❌',
      'Cross-Platform Detector': window.pipMasterInstance.crossPlatformDetector ? '✅' : '❌',
      'Settings Panel': window.pipMasterInstance.settingsPanel ? '✅' : '❌'
    };
    
    const info = Object.entries(features)
      .map(([name, status]) => `${status} ${name}`)
      .join('\n');
    
    console.log("🎯 PiP Master Enhanced - Feature Status:");
    console.log("========================================");
    console.log(info);
    
    // Also show performance stats if available
    if (window.pipMasterInstance.performanceOptimizer) {
      console.log("\n⚡ Performance Stats:");
      console.log(window.pipMasterInstance.performanceOptimizer.getPerformanceStats());
    }
    
    // Show platform info if available
    if (window.pipMasterInstance.crossPlatformDetector) {
      console.log("\n🌐 Platform Info:");
      console.log(window.pipMasterInstance.crossPlatformDetector.getPlatformInfo());
    }
  }

  getFeatureStatus() {
    return {
      loaded: true,
      features: {
        smartAutoPiP: !!window.pipMasterInstance.smartAutoPiP,
        themeManager: !!window.pipMasterInstance.themeManager,
        advancedControls: !!window.pipMasterInstance.advancedControls,
        sitePreferences: !!window.pipMasterInstance.sitePreferences,
        accessibility: !!window.pipMasterInstance.accessibility,
        performanceOptimizer: !!window.pipMasterInstance.performanceOptimizer,
        crossPlatformDetector: !!window.pipMasterInstance.crossPlatformDetector,
        settingsPanel: !!window.pipMasterInstance.settingsPanel
      },
      version: '2.0.0-enhanced'
    };
  }
}

// Initialize enhanced features
window.pipMasterEnhanced = new EnhancedFeatureLoader();

// Auto-load all features
window.pipMasterEnhanced.loadAllFeatures();

// Global commands for easy access
window.showPipMasterHelp = () => window.pipMasterEnhanced.showHelpDialog();
window.showPipMasterInfo = () => window.pipMasterEnhanced.showFeatureInfo();
window.getPipMasterStatus = () => window.pipMasterEnhanced.getFeatureStatus();

console.log("\n🎯 PiP Master Enhanced - Global Commands:");
console.log("=========================================");
console.log("showPipMasterHelp()     - Show help dialog");
console.log("showPipMasterInfo()     - Show feature status");
console.log("getPipMasterStatus()    - Get detailed status");
console.log("");
console.log("⌨️ Keyboard Shortcuts:");
console.log("Alt + P: Toggle PiP");
console.log("Alt + S: Settings");
console.log("Alt + H: Help");
console.log("Alt + I: Feature Info");
