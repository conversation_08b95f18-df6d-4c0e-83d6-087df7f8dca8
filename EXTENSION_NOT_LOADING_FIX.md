# PiP Master Extension Not Loading - Fix Guide

## 🚨 Error: `Cannot read properties of undefined (reading 'scanForVideos')`

This error means the extension isn't loading properly. Follow these steps to fix it:

## Step 1: Check Extension Loading

1. **Open Chrome Extensions Page**
   - Go to `chrome://extensions/`
   - Or click the puzzle piece icon → "Manage extensions"

2. **Find PiP Master Extension**
   - Look for "PiP Master" in the list
   - Check if it's **enabled** (toggle should be blue/on)

3. **Check for Errors**
   - Look for red "Errors" button on the extension card
   - If present, click it to see error details

## Step 2: Reload the Extension

1. **Click the Reload Button** 🔄 on the PiP Master extension card
2. **Wait 2-3 seconds** for reload to complete
3. **Go back to your test page**
4. **Refresh the page** (Ctrl+R or F5)

## Step 3: Run Loading Test

1. **Open DevTools Console** (F12)
2. **Copy and paste** the contents of `extension-loading-test.js`
3. **Press Enter** to run the test
4. **Check the results** - it will tell you exactly what's wrong

## Step 4: Common Issues and Fixes

### Issue: Extension Card Shows Errors

**Fix:**
1. Click "Errors" button to see details
2. Common errors:
   - **Manifest errors**: Check `manifest.json` syntax
   - **File not found**: Ensure all files exist
   - **Permission errors**: Check manifest permissions

### Issue: Extension Loads but Content Script Fails

**Symptoms:**
- Extension appears in chrome://extensions/
- No errors shown
- But `window.pipMasterContentLoaded` is undefined

**Fix:**
1. Check if `content/content.js` file exists
2. Look for JavaScript errors in console
3. Try the debug version:
   - Rename `manifest-debug.json` to `manifest.json`
   - Reload extension

### Issue: Extension Works on Some Sites but Not Others

**Symptoms:**
- Works on test page but not YouTube
- Works on HTTP but not HTTPS

**Fix:**
1. Check `host_permissions` in manifest
2. Ensure `<all_urls>` is included
3. Some sites may block extensions

## Step 5: Manual Extension Installation

If the extension won't load at all:

1. **Remove the extension** from chrome://extensions/
2. **Close all Chrome windows**
3. **Restart Chrome**
4. **Go to chrome://extensions/**
5. **Enable Developer Mode** (top-right toggle)
6. **Click "Load unpacked"**
7. **Select the PiP folder** (`c:\git_projects\PIP`)

## Step 6: Use Debug Version

If the main version doesn't work, try the simplified debug version:

1. **In the PiP folder**, rename files:
   - `manifest.json` → `manifest-original.json`
   - `manifest-debug.json` → `manifest.json`
2. **Reload the extension**
3. **Test again**

## Step 7: Check File Structure

Ensure these files exist in your PiP folder:

```
PIP/
├── manifest.json
├── background.js
├── content/
│   ├── content.js
│   └── content.css
├── popup/
│   ├── popup.html
│   └── popup.js
└── options/
    └── options.html
```

## Step 8: Console Commands for Testing

Once the extension loads, you should be able to run:

```javascript
// Check if extension loaded
window.pipMasterContentLoaded

// Check instance
window.pipMasterInstance

// Check debug functions
window.pipMasterDebug

// Manual video scan
window.pipMasterDebug.scanForVideos()

// Get video count
window.pipMasterDebug.getVideoCount()
```

## Step 9: Browser Compatibility

Ensure you're using a compatible browser:

- **Chrome 88+** (required for PiP API)
- **Edge 88+** (Chromium-based)
- **Opera 74+** (Chromium-based)

Check your version: `chrome://version/`

## Step 10: Clean Installation

If nothing else works, try a clean installation:

1. **Remove extension** completely
2. **Delete browser cache**: Settings → Privacy → Clear browsing data
3. **Restart Chrome**
4. **Re-install extension** from scratch
5. **Test on a simple page first** (like the test page)

## Quick Diagnostic Commands

Run these in console to diagnose:

```javascript
// 1. Check Chrome APIs
typeof chrome !== 'undefined'

// 2. Check extension ID
chrome.runtime.id

// 3. Check content script
window.pipMasterContentLoaded

// 4. Check for errors
chrome.runtime.lastError

// 5. Test message passing
chrome.runtime.sendMessage({type: 'GET_SETTINGS'}, console.log)
```

## Expected Console Output

When working correctly, you should see:

```
PiP Master: Background script starting...
PiP Master: Content script starting...
PiP Master: Initializing content script...
PiP Master: DOM already ready, creating instance...
```

## Still Not Working?

If the extension still won't load:

1. **Check Chrome version** - need 88+
2. **Try incognito mode** - rules out conflicts
3. **Disable other extensions** - test for conflicts
4. **Check corporate/school restrictions** - some block extensions
5. **Try on a different computer** - hardware/OS issues

## Error-Specific Solutions

### "Extension is not enabled"
- Go to chrome://extensions/ and enable it

### "This extension may have been corrupted"
- Remove and reinstall the extension

### "Package is invalid"
- Check manifest.json syntax
- Ensure all required files exist

### "Could not load content script"
- Check content/content.js exists
- Look for JavaScript syntax errors

### "Permissions error"
- Check manifest permissions
- Ensure host_permissions includes target sites

## Contact Information

If you're still having issues after following this guide:

1. **Note your Chrome version**: `chrome://version/`
2. **Note any error messages** from chrome://extensions/
3. **Include console output** from the loading test script
4. **Specify which step failed**

The extension should load successfully after following these steps.
