// Settings Functionality Fix Script
// Targeted fixes for settings panel and feature integration issues

console.log("🔧 Settings Functionality Fix Script");
console.log("====================================");

// Settings Fix Manager
class SettingsFunctionalityFix {
  constructor() {
    this.fixes = [];
    this.appliedFixes = [];
    this.runSettingsFixes();
  }

  runSettingsFixes() {
    console.log("🚀 Analyzing and fixing settings issues...");
    
    this.analyzeSettingsIssues();
    this.applySettingsFixes();
    this.verifySettingsFixes();
    this.generateFixReport();
  }

  analyzeSettingsIssues() {
    console.log("\n🔍 Analyzing settings issues...");
    
    // Check if settings panel exists
    if (!window.pipMasterInstance?.settingsPanel) {
      this.fixes.push({
        priority: 'high',
        issue: 'Settings panel not initialized',
        fix: 'initializeSettingsPanel',
        description: 'Settings panel instance missing'
      });
    }
    
    // Check if settings panel DOM element exists
    const panelElement = document.getElementById('pip-master-settings-panel');
    if (window.pipMasterInstance?.settingsPanel && !panelElement) {
      this.fixes.push({
        priority: 'high',
        issue: 'Settings panel DOM missing',
        fix: 'recreateSettingsPanel',
        description: 'Panel instance exists but DOM element missing'
      });
    }
    
    // Check form elements
    if (panelElement) {
      const requiredElements = [
        'auto-enable', 'auto-exit', 'overlay-theme', 'low-power',
        'scan-frequency', 'high-contrast', 'reduced-motion',
        'volume-control', 'speed-control', 'skip-control',
        'remember-sites', 'auto-apply-sites'
      ];
      
      const missingElements = requiredElements.filter(id => !document.getElementById(id));
      if (missingElements.length > 0) {
        this.fixes.push({
          priority: 'medium',
          issue: 'Form elements missing',
          fix: 'recreateFormElements',
          description: `Missing elements: ${missingElements.join(', ')}`
        });
      }
    }
    
    // Check localStorage functionality
    try {
      localStorage.setItem('pipMaster_test', 'test');
      localStorage.removeItem('pipMaster_test');
    } catch (error) {
      this.fixes.push({
        priority: 'high',
        issue: 'localStorage not accessible',
        fix: 'setupAlternativeStorage',
        description: 'Cannot access localStorage for settings persistence'
      });
    }
    
    // Check feature integration
    const features = ['smartAutoPiP', 'themeManager', 'accessibility', 'performanceOptimizer'];
    const missingFeatures = features.filter(feature => !window.pipMasterInstance?.[feature]);
    
    if (missingFeatures.length > 0) {
      this.fixes.push({
        priority: 'medium',
        issue: 'Features not integrated',
        fix: 'initializeMissingFeatures',
        description: `Missing features: ${missingFeatures.join(', ')}`
      });
    }
    
    // Check event listeners
    if (window.pipMasterInstance?.settingsPanel?.panel) {
      const saveButton = document.getElementById('pip-settings-save');
      const closeButton = document.getElementById('pip-settings-close');
      
      if (saveButton && !this.hasEventListener(saveButton, 'click')) {
        this.fixes.push({
          priority: 'medium',
          issue: 'Save button not functional',
          fix: 'attachEventListeners',
          description: 'Save button missing click handler'
        });
      }
      
      if (closeButton && !this.hasEventListener(closeButton, 'click')) {
        this.fixes.push({
          priority: 'medium',
          issue: 'Close button not functional',
          fix: 'attachEventListeners',
          description: 'Close button missing click handler'
        });
      }
    }
  }

  applySettingsFixes() {
    console.log("\n🔧 Applying settings fixes...");
    
    // Sort fixes by priority
    const sortedFixes = this.fixes.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
    
    sortedFixes.forEach(fix => {
      console.log(`🔧 Fixing: ${fix.issue}`);
      try {
        this[fix.fix]();
        this.appliedFixes.push(fix);
        console.log(`✅ Fixed: ${fix.issue}`);
      } catch (error) {
        console.error(`❌ Failed to fix ${fix.issue}:`, error);
      }
    });
  }

  // Fix implementations
  initializeSettingsPanel() {
    console.log("⚙️ Initializing settings panel...");
    
    if (!window.pipMasterInstance) {
      console.error("❌ PiP Master instance not available");
      return;
    }
    
    // Re-run settings panel initialization
    if (window.initializeSettingsPanel) {
      window.initializeSettingsPanel();
    } else {
      // Manual initialization
      const SettingsPanelManager = window.SettingsPanelManager;
      if (SettingsPanelManager) {
        window.pipMasterInstance.settingsPanel = new SettingsPanelManager(window.pipMasterInstance);
      }
    }
  }

  recreateSettingsPanel() {
    console.log("🔄 Recreating settings panel DOM...");
    
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    if (!settingsPanel) return;
    
    // Remove existing panel if any
    const existingPanel = document.getElementById('pip-master-settings-panel');
    if (existingPanel) {
      existingPanel.remove();
    }
    
    // Recreate panel
    settingsPanel.createSettingsPanel();
  }

  recreateFormElements() {
    console.log("📝 Recreating form elements...");
    
    const panel = document.getElementById('pip-master-settings-panel');
    if (!panel) return;
    
    // Remove and recreate panel content
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    if (settingsPanel) {
      panel.innerHTML = settingsPanel.generatePanelHTML();
      settingsPanel.setupEventListeners();
    }
  }

  setupAlternativeStorage() {
    console.log("💾 Setting up alternative storage...");
    
    // Use chrome.storage as fallback
    if (typeof chrome !== 'undefined' && chrome.storage) {
      const settingsPanel = window.pipMasterInstance?.settingsPanel;
      if (settingsPanel) {
        // Override save method to use chrome.storage
        settingsPanel.saveSettings = function() {
          try {
            chrome.storage.local.set({
              'pipMaster_enhancedSettings': JSON.stringify(this.settings)
            });
            console.log("✅ Settings saved to chrome.storage");
          } catch (error) {
            console.warn("Failed to save to chrome.storage:", error);
          }
        };
        
        // Override load method
        settingsPanel.loadSettings = function() {
          try {
            chrome.storage.local.get(['pipMaster_enhancedSettings'], (result) => {
              if (result.pipMaster_enhancedSettings) {
                this.settings = JSON.parse(result.pipMaster_enhancedSettings);
              }
            });
          } catch (error) {
            console.warn("Failed to load from chrome.storage:", error);
            return this.getDefaultSettings();
          }
        };
      }
    } else {
      // Use in-memory storage as last resort
      window.pipMasterSettings = {};
      console.log("⚠️ Using in-memory storage (settings won't persist)");
    }
  }

  initializeMissingFeatures() {
    console.log("🔗 Initializing missing features...");
    
    // Initialize enhanced features if missing
    if (window.initializeEnhancedFeatures && !window.pipMasterInstance.smartAutoPiP) {
      window.initializeEnhancedFeatures();
    }
    
    // Initialize site features if missing
    if (window.initializeSiteFeatures && !window.pipMasterInstance.sitePreferences) {
      window.initializeSiteFeatures();
    }
    
    // Initialize advanced features if missing
    if (window.initializeAdvancedFeatures && !window.pipMasterInstance.performanceOptimizer) {
      window.initializeAdvancedFeatures();
    }
    
    // Initialize timeline control if missing
    if (window.initializeTimelineControl && !window.pipMasterInstance.timelineControl) {
      window.initializeTimelineControl();
    }
  }

  attachEventListeners() {
    console.log("🔗 Attaching event listeners...");
    
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    if (!settingsPanel) return;
    
    // Re-setup event listeners
    settingsPanel.setupEventListeners();
    
    // Ensure keyboard shortcuts are working
    settingsPanel.setupKeyboardShortcuts();
  }

  hasEventListener(element, eventType) {
    // Simple check for event listeners
    return element && (
      element[`on${eventType}`] !== null ||
      element.getAttribute(`on${eventType}`) !== null
    );
  }

  verifySettingsFixes() {
    console.log("\n✅ Verifying settings fixes...");
    
    const verificationResults = {
      settingsPanel: !!window.pipMasterInstance?.settingsPanel,
      panelDOM: !!document.getElementById('pip-master-settings-panel'),
      canOpen: false,
      canSave: false,
      localStorage: false,
      features: 0
    };
    
    // Test panel opening
    try {
      if (window.pipMasterInstance?.settingsPanel) {
        window.pipMasterInstance.settingsPanel.showPanel();
        verificationResults.canOpen = window.pipMasterInstance.settingsPanel.isVisible;
        window.pipMasterInstance.settingsPanel.hidePanel();
      }
    } catch (error) {
      console.warn("Panel open verification failed:", error);
    }
    
    // Test saving
    try {
      if (window.pipMasterInstance?.settingsPanel) {
        window.pipMasterInstance.settingsPanel.saveSettings();
        verificationResults.canSave = true;
      }
    } catch (error) {
      console.warn("Save verification failed:", error);
    }
    
    // Test localStorage
    try {
      localStorage.setItem('pipMaster_test', 'test');
      localStorage.removeItem('pipMaster_test');
      verificationResults.localStorage = true;
    } catch (error) {
      console.warn("localStorage verification failed:", error);
    }
    
    // Count available features
    const features = ['smartAutoPiP', 'themeManager', 'accessibility', 'performanceOptimizer', 'sitePreferences', 'timelineControl'];
    verificationResults.features = features.filter(feature => window.pipMasterInstance?.[feature]).length;
    
    console.log("Verification results:");
    Object.entries(verificationResults).forEach(([test, result]) => {
      const status = typeof result === 'boolean' ? (result ? '✅' : '❌') : `${result}/${features.length}`;
      console.log(`${status} ${test}: ${result}`);
    });
    
    return verificationResults;
  }

  generateFixReport() {
    console.log("\n📊 SETTINGS FIX REPORT");
    console.log("======================");
    
    console.log(`🔧 Total fixes identified: ${this.fixes.length}`);
    console.log(`✅ Fixes applied successfully: ${this.appliedFixes.length}`);
    console.log(`❌ Fixes failed: ${this.fixes.length - this.appliedFixes.length}`);
    
    if (this.appliedFixes.length > 0) {
      console.log("\n✅ Applied fixes:");
      this.appliedFixes.forEach((fix, index) => {
        console.log(`${index + 1}. ${fix.issue} (${fix.priority} priority)`);
      });
    }
    
    const failedFixes = this.fixes.filter(fix => !this.appliedFixes.includes(fix));
    if (failedFixes.length > 0) {
      console.log("\n❌ Failed fixes:");
      failedFixes.forEach((fix, index) => {
        console.log(`${index + 1}. ${fix.issue} (${fix.priority} priority)`);
      });
    }
    
    // Run verification
    const verification = this.verifySettingsFixes();
    const successRate = Object.values(verification).filter(v => v === true || (typeof v === 'number' && v > 0)).length;
    const totalTests = Object.keys(verification).length;
    
    console.log(`\n📊 Success Rate: ${successRate}/${totalTests} (${Math.round(successRate/totalTests*100)}%)`);
    
    if (successRate === totalTests) {
      console.log("🎉 All settings functionality restored!");
    } else if (successRate >= totalTests * 0.8) {
      console.log("✅ Most settings functionality working");
    } else {
      console.log("⚠️ Some settings issues remain");
    }
  }
}

// Manual fix functions
window.fixSettingsPanel = function() {
  console.log("⚡ Quick Settings Panel Fix");
  console.log("===========================");
  
  // Force re-initialization
  if (window.initializeSettingsPanel) {
    window.initializeSettingsPanel();
    console.log("✅ Settings panel re-initialized");
  }
  
  // Ensure panel is in DOM
  if (window.pipMasterInstance?.settingsPanel && !document.getElementById('pip-master-settings-panel')) {
    window.pipMasterInstance.settingsPanel.createSettingsPanel();
    console.log("✅ Settings panel DOM recreated");
  }
  
  // Test functionality
  try {
    window.pipMasterInstance.settingsPanel.showPanel();
    console.log("✅ Panel can open");
    window.pipMasterInstance.settingsPanel.hidePanel();
    console.log("✅ Panel can close");
  } catch (error) {
    console.error("❌ Panel functionality test failed:", error);
  }
};

window.resetAllSettings = function() {
  console.log("🔄 Resetting All Settings");
  console.log("=========================");
  
  try {
    if (window.pipMasterInstance?.settingsPanel) {
      window.pipMasterInstance.settingsPanel.resetToDefaults();
      console.log("✅ Settings reset to defaults");
    }
    
    // Clear localStorage
    localStorage.removeItem('pipMaster_enhancedSettings');
    localStorage.removeItem('pipMaster_sitePreferences');
    console.log("✅ Local storage cleared");
    
    // Re-apply default settings
    if (window.pipMasterInstance?.settingsPanel) {
      window.pipMasterInstance.settingsPanel.applySettings();
      console.log("✅ Default settings applied");
    }
    
  } catch (error) {
    console.error("❌ Settings reset failed:", error);
  }
};

window.testSettingsIntegration = function() {
  console.log("🧪 Testing Settings Integration");
  console.log("===============================");
  
  const tests = {};
  
  // Test theme integration
  try {
    if (window.pipMasterInstance?.themeManager) {
      window.pipMasterInstance.themeManager.setTheme('neon');
      tests.themeIntegration = window.pipMasterInstance.themeManager.currentTheme === 'neon';
      window.pipMasterInstance.themeManager.setTheme('default');
    }
  } catch (error) {
    tests.themeIntegration = false;
  }
  
  // Test accessibility integration
  try {
    if (window.pipMasterInstance?.accessibility) {
      window.pipMasterInstance.accessibility.enableHighContrastMode();
      tests.accessibilityIntegration = !!document.getElementById('pip-master-high-contrast');
      window.pipMasterInstance.accessibility.disableHighContrastMode();
    }
  } catch (error) {
    tests.accessibilityIntegration = false;
  }
  
  // Test performance integration
  try {
    if (window.pipMasterInstance?.performanceOptimizer) {
      const stats = window.pipMasterInstance.performanceOptimizer.getPerformanceStats();
      tests.performanceIntegration = !!stats;
    }
  } catch (error) {
    tests.performanceIntegration = false;
  }
  
  console.log("Integration test results:");
  Object.entries(tests).forEach(([test, result]) => {
    console.log(`${result ? '✅' : '❌'} ${test}: ${result}`);
  });
  
  return tests;
};

// Auto-run settings fixes
console.log("🚀 Auto-running settings functionality fixes...");
window.settingsFunctionalityFix = new SettingsFunctionalityFix();

console.log("\n📋 Available Fix Commands:");
console.log("==========================");
console.log("fixSettingsPanel()                    - Quick settings panel fix");
console.log("resetAllSettings()                    - Reset all settings to defaults");
console.log("testSettingsIntegration()             - Test feature integration");
console.log("new SettingsFunctionalityFix()        - Run comprehensive fixes");
