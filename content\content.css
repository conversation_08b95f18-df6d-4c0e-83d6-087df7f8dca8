/* PiP Master Enhanced Content Styles */

.pip-master-overlay {
  position: absolute;
  z-index: 10000;
  pointer-events: auto;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(0.8);
}

.pip-master-overlay.pip-master-visible {
  opacity: 1;
  transform: scale(1);
}

.pip-master-container {
  position: relative;
  background: rgba(0, 0, 0, 0.75);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  min-width: 40px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pip-master-container:hover {
  background: rgba(66, 133, 244, 0.9);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(66, 133, 244, 0.4);
}

.pip-master-container:active {
  transform: scale(0.95);
}

.pip-master-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.2s ease;
}

.pip-master-indicator svg {
  width: 16px;
  height: 16px;
  fill: currentColor;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.pip-master-hover .pip-master-indicator svg {
  transform: scale(1.1);
}

.pip-master-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-10px);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  margin-bottom: 8px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.pip-master-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
}

.pip-master-shortcut {
  font-size: 10px;
  opacity: 0.8;
  margin-top: 2px;
  padding: 2px 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  text-align: center;
}

/* Error notification styles */
.pip-master-error {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  background: #f44336 !important;
  color: white !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  z-index: 10001 !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3) !important;
  animation: pip-master-slide-in 0.3s ease-out !important;
}

/* Enhanced visual states and animations */
.pip-master-context-hint {
  animation: pip-master-pulse 0.6s ease-in-out;
}

.pip-master-overlay.pip-master-activating .pip-master-container {
  background: rgba(76, 175, 80, 0.9) !important;
  border-color: rgba(255, 255, 255, 0.6);
  animation: pip-master-activate 0.4s ease-out;
}

.pip-master-overlay.pip-master-error .pip-master-container {
  background: rgba(244, 67, 54, 0.9) !important;
  border-color: rgba(255, 255, 255, 0.6);
  animation: pip-master-shake 0.4s ease-in-out;
}

/* Success notification */
.pip-master-success {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  background: linear-gradient(135deg, #4caf50, #388e3c) !important;
  color: white !important;
  padding: 16px 20px !important;
  border-radius: 12px !important;
  z-index: 10001 !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  box-shadow: 0 8px 24px rgba(76, 175, 80, 0.3) !important;
  animation: pip-master-slide-in 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(8px) !important;
  max-width: 300px !important;
}

/* Responsive design for different screen sizes */
@media (max-width: 768px) {
  .pip-master-container {
    min-width: 36px;
    min-height: 36px;
    padding: 6px;
  }

  .pip-master-indicator svg {
    width: 14px;
    height: 14px;
  }

  .pip-master-tooltip {
    font-size: 11px;
    padding: 6px 10px;
  }

  .pip-master-error,
  .pip-master-success {
    top: 10px !important;
    right: 10px !important;
    left: 10px !important;
    max-width: none !important;
  }
}

@keyframes pip-master-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pip-master-pulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 6px 24px rgba(66, 133, 244, 0.5);
  }
}

@keyframes pip-master-activate {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.15);
  }
  100% {
    transform: scale(1.05);
  }
}

@keyframes pip-master-shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-4px);
  }
  75% {
    transform: translateX(4px);
  }
}

@keyframes pip-master-slide-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .pip-master-button {
    background: rgba(255, 255, 255, 0.15);
    color: white;
  }

  .pip-master-button:hover {
    background: rgba(255, 255, 255, 0.25);
  }
}

/* Light theme support */
@media (prefers-color-scheme: light) {
  .pip-master-button {
    background: rgba(0, 0, 0, 0.7);
    color: white;
  }

  .pip-master-button:hover {
    background: rgba(0, 0, 0, 0.85);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .pip-master-button {
    background: black;
    border: 2px solid white;
    color: white;
  }

  .pip-master-button:hover {
    background: white;
    color: black;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .pip-master-button,
  .pip-master-overlay,
  .pip-master-error {
    transition: none !important;
    animation: none !important;
  }

  .pip-master-button:hover {
    transform: none;
  }

  .pip-master-button:active {
    transform: none;
  }
}

/* Focus styles for accessibility */
.pip-master-button:focus {
  outline: 2px solid #4285f4;
  outline-offset: 2px;
}

.pip-master-button:focus:not(:focus-visible) {
  outline: none;
}

/* Ensure overlay doesn't interfere with video controls */
.pip-master-overlay {
  pointer-events: none;
}

.pip-master-overlay * {
  pointer-events: auto;
}

/* Platform-specific adjustments */
.pip-master-overlay[data-platform="youtube"] {
  /* Ensure overlay is above YouTube's UI elements */
  z-index: 10001 !important;
}

.pip-master-overlay[data-platform="youtube"] .pip-master-container {
  /* YouTube-specific styling for better visibility */
  background: rgba(0, 0, 0, 0.85) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5) !important;
}

.pip-master-overlay[data-platform="youtube"] .pip-master-container:hover {
  background: rgba(255, 0, 0, 0.9) !important; /* YouTube red */
  border-color: rgba(255, 255, 255, 0.6) !important;
  transform: scale(1.1) !important;
}

.pip-master-overlay[data-platform="netflix"] {
  /* Netflix-specific adjustments if needed */
}

.pip-master-overlay[data-platform="twitch"] {
  /* Twitch-specific adjustments if needed */
}

/* Ensure overlay is always visible and clickable */
.pip-master-overlay {
  pointer-events: auto !important;
  user-select: none !important;
}

.pip-master-overlay * {
  pointer-events: auto !important;
}

/* Fix for YouTube's fullscreen mode */
.ytp-fullscreen .pip-master-overlay {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 10002 !important;
}
