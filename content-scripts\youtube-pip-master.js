// PiP Master v2.0.0 - YouTube Integration
class YouTubePipMaster {
    constructor() {
        this.isInitialized = false;
        this.videos = new Map();
        this.settings = {
            autoActivate: false,
            theme: 'dark',
            audioBoost: false,
            timelineVisible: true
        };
        this.init();
    }

    async init() {
        console.log('🚀 Initializing PiP Master v2.0.0 for YouTube...');
        
        // Wait for YouTube to load
        await this.waitForYouTube();
        
        // Load user settings
        await this.loadSettings();
        
        // Setup core functionality
        this.setupVideoDetection();
        this.setupMutationObserver();
        this.setupKeyboardShortcuts();
        this.setupEnhancedFeatures();
        this.injectStyles();
        
        this.isInitialized = true;
        console.log('✅ PiP Master initialized successfully');
    }

    waitForYouTube() {
        return new Promise((resolve) => {
            const checkYouTube = () => {
                if (document.querySelector('ytd-app') || document.querySelector('#movie_player')) {
                    resolve();
                } else {
                    setTimeout(checkYouTube, 100);
                }
            };
            checkYouTube();
        });
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get(['pipSettings']);
            if (result.pipSettings) {
                this.settings = { ...this.settings, ...result.pipSettings };
            }
        } catch (error) {
            console.warn('Could not load settings:', error);
        }
    }

    setupVideoDetection() {
        // Detect existing videos
        this.scanForVideos();
        
        // Setup periodic scanning for dynamic content
        setInterval(() => this.scanForVideos(), 2000);
    }

    scanForVideos() {
        const videoSelectors = [
            'video',
            '.html5-video-player video',
            '#movie_player video',
            'ytd-player video'
        ];

        videoSelectors.forEach(selector => {
            const videos = document.querySelectorAll(selector);
            videos.forEach(video => {
                if (!this.videos.has(video)) {
                    this.processNewVideo(video);
                }
            });
        });
    }

    processNewVideo(video) {
        console.log('📹 New video detected:', video);
        
        const videoData = {
            element: video,
            pipButton: null,
            controls: null,
            isInPip: false
        };

        this.videos.set(video, videoData);
        this.createPipControls(video, videoData);
        this.setupVideoEventListeners(video, videoData);
    }

    createPipControls(video, videoData) {
        const playerContainer = this.findPlayerContainer(video);
        if (!playerContainer) return;

        // Create main PiP button
        const pipButton = this.createPipButton(video);
        videoData.pipButton = pipButton;

        // Create enhanced controls panel
        const controlsPanel = this.createControlsPanel(video, videoData);
        videoData.controls = controlsPanel;

        // Insert into DOM
        playerContainer.appendChild(pipButton);
        playerContainer.appendChild(controlsPanel);
    }

    findPlayerContainer(video) {
        const containers = [
            '.html5-video-player',
            '#movie_player',
            'ytd-player',
            '.video-stream'
        ];

        for (const selector of containers) {
            const container = video.closest(selector);
            if (container) {
                container.style.position = 'relative';
                return container;
            }
        }
        return null;
    }

    createPipButton(video) {
        const button = document.createElement('button');
        button.className = 'pip-master-button';
        button.innerHTML = `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 7h-8v6h8V7zm2-4H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h18v14z"/>
            </svg>
            <span>PiP</span>
        `;
        
        button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.togglePip(video);
        });

        return button;
    }

    createControlsPanel(video, videoData) {
        const panel = document.createElement('div');
        panel.className = 'pip-master-controls';
        panel.innerHTML = `
            <div class="pip-controls-header">
                <span>PiP Master v2.0.0</span>
                <button class="pip-close-btn">×</button>
            </div>
            <div class="pip-controls-body">
                <div class="pip-control-group">
                    <label>
                        <input type="checkbox" class="auto-pip-toggle" ${this.settings.autoActivate ? 'checked' : ''}>
                        Smart Auto-PiP
                    </label>
                </div>
                <div class="pip-control-group">
                    <label>Audio Boost</label>
                    <input type="range" class="audio-boost-slider" min="0" max="200" value="100">
                </div>
                <div class="pip-control-group">
                    <label>Timeline Controls</label>
                    <div class="timeline-controls">
                        <button class="timeline-btn" data-action="rewind">⏪</button>
                        <button class="timeline-btn" data-action="play-pause">⏯️</button>
                        <button class="timeline-btn" data-action="forward">⏩</button>
                    </div>
                </div>
                <div class="pip-control-group">
                    <label>Theme</label>
                    <select class="theme-selector">
                        <option value="dark" ${this.settings.theme === 'dark' ? 'selected' : ''}>Dark</option>
                        <option value="light" ${this.settings.theme === 'light' ? 'selected' : ''}>Light</option>
                        <option value="youtube" ${this.settings.theme === 'youtube' ? 'selected' : ''}>YouTube</option>
                    </select>
                </div>
            </div>
        `;

        this.setupControlsEventListeners(panel, video, videoData);
        return panel;
    }

    setupControlsEventListeners(panel, video, videoData) {
        // Close button
        panel.querySelector('.pip-close-btn').addEventListener('click', () => {
            panel.style.display = 'none';
        });

        // Auto-PiP toggle
        panel.querySelector('.auto-pip-toggle').addEventListener('change', (e) => {
            this.settings.autoActivate = e.target.checked;
            this.saveSettings();
        });

        // Audio boost
        panel.querySelector('.audio-boost-slider').addEventListener('input', (e) => {
            const volume = e.target.value / 100;
            video.volume = Math.min(volume, 1);
        });

        // Timeline controls
        panel.querySelectorAll('.timeline-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                this.handleTimelineAction(video, action);
            });
        });

        // Theme selector
        panel.querySelector('.theme-selector').addEventListener('change', (e) => {
            this.settings.theme = e.target.value;
            this.applyTheme(e.target.value);
            this.saveSettings();
        });
    }

    handleTimelineAction(video, action) {
        switch (action) {
            case 'rewind':
                video.currentTime = Math.max(0, video.currentTime - 10);
                break;
            case 'play-pause':
                video.paused ? video.play() : video.pause();
                break;
            case 'forward':
                video.currentTime = Math.min(video.duration, video.currentTime + 10);
                break;
        }
    }

    setupVideoEventListeners(video, videoData) {
        // PiP state changes
        video.addEventListener('enterpictureinpicture', () => {
            videoData.isInPip = true;
            videoData.pipButton.classList.add('active');
            console.log('📺 Entered PiP mode');
        });

        video.addEventListener('leavepictureinpicture', () => {
            videoData.isInPip = false;
            videoData.pipButton.classList.remove('active');
            console.log('📺 Left PiP mode');
        });

        // Auto-PiP functionality
        if (this.settings.autoActivate) {
            this.setupAutoPip(video, videoData);
        }
    }

    setupAutoPip(video, videoData) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.intersectionRatio < 0.5 && !video.paused && !videoData.isInPip) {
                    // Video is mostly out of view and playing
                    setTimeout(() => {
                        if (entry.intersectionRatio < 0.5 && !video.paused) {
                            this.activatePip(video);
                        }
                    }, 1000); // 1 second delay
                }
            });
        }, { threshold: [0.5] });

        observer.observe(video);
    }

    togglePip(video) {
        const videoData = this.videos.get(video);
        if (!videoData) return;

        if (videoData.isInPip) {
            document.exitPictureInPicture();
        } else {
            this.activatePip(video);
        }
    }

    activatePip(video) {
        if (video.requestPictureInPicture) {
            video.requestPictureInPicture().catch(error => {
                console.error('PiP activation failed:', error);
                this.showNotification('PiP activation failed. Please try again.');
            });
        } else {
            this.showNotification('Picture-in-Picture not supported in this browser.');
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only activate on YouTube pages
            if (!window.location.hostname.includes('youtube.com')) return;
            
            if (e.altKey && !e.ctrlKey && !e.shiftKey) {
                switch (e.key.toLowerCase()) {
                    case 'p':
                        e.preventDefault();
                        this.handleKeyboardPip();
                        break;
                    case 's':
                        e.preventDefault();
                        this.toggleSettingsPanel();
                        break;
                    case 't':
                        e.preventDefault();
                        this.toggleTimelineControls();
                        break;
                }
            }
        });

        window.pipKeyboardListeners = true;
        console.log('⌨️ Keyboard shortcuts activated');
    }

    handleKeyboardPip() {
        const activeVideo = this.getActiveVideo();
        if (activeVideo) {
            this.togglePip(activeVideo);
        }
    }

    getActiveVideo() {
        // Find the currently playing or focused video
        for (const [video, data] of this.videos) {
            if (!video.paused || video === document.activeElement) {
                return video;
            }
        }
        // Return the first video if none are playing
        return this.videos.keys().next().value;
    }

    toggleSettingsPanel() {
        const activeVideo = this.getActiveVideo();
        if (activeVideo) {
            const videoData = this.videos.get(activeVideo);
            if (videoData && videoData.controls) {
                const panel = videoData.controls;
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
            }
        }
    }

    setupMutationObserver() {
        const observer = new MutationObserver((mutations) => {
            let shouldScan = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1 && 
                            (node.tagName === 'VIDEO' || node.querySelector('video'))) {
                            shouldScan = true;
                        }
                    });
                }
            });

            if (shouldScan) {
                setTimeout(() => this.scanForVideos(), 500);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log('👁️ DOM observer activated');
    }

    setupEnhancedFeatures() {
        // Apply current theme
        this.applyTheme(this.settings.theme);
        
        // Setup notification system
        this.createNotificationContainer();
        
        console.log('✨ Enhanced features activated');
    }

    applyTheme(theme) {
        document.documentElement.setAttribute('data-pip-theme', theme);
        
        const themeColors = {
            dark: { primary: '#1a1a1a', secondary: '#333', accent: '#ff6b6b' },
            light: { primary: '#ffffff', secondary: '#f5f5f5', accent: '#007bff' },
            youtube: { primary: '#0f0f0f', secondary: '#272727', accent: '#ff0000' }
        };

        const colors = themeColors[theme] || themeColors.dark;
        document.documentElement.style.setProperty('--pip-primary', colors.primary);
        document.documentElement.style.setProperty('--pip-secondary', colors.secondary);
        document.documentElement.style.setProperty('--pip-accent', colors.accent);
    }

    createNotificationContainer() {
        if (document.getElementById('pip-notifications')) return;

        const container = document.createElement('div');
        container.id = 'pip-notifications';
        container.className = 'pip-notifications-container';
        document.body.appendChild(container);
    }

    showNotification(message, type = 'info') {
        const container = document.getElementById('pip-notifications');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `pip-notification pip-notification-${type}`;
        notification.textContent = message;

        container.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    async saveSettings() {
        try {
            await chrome.storage.sync.set({ pipSettings: this.settings });
        } catch (error) {
            console.warn('Could not save settings:', error);
        }
    }

    injectStyles() {
        if (document.getElementById('pip-master-