// Phase 1: Core Infrastructure Upgrade (v1.0.0 → v2.0.0)
// Upgrades settings panel architecture and keyboard shortcuts

console.log("🚀 Phase 1: Core Infrastructure Upgrade");
console.log("=======================================");

// Phase 1 Infrastructure Manager
class Phase1InfrastructureUpgrade {
  constructor() {
    this.backupData = {};
    this.upgradeStatus = {
      backup: false,
      settingsArchitecture: false,
      keyboardShortcuts: false,
      compatibility: false,
      verification: false
    };
    this.executePhase1();
  }

  executePhase1() {
    console.log("🔧 Starting Phase 1 upgrade...");
    
    this.backupCurrentState();
    this.upgradeSettingsArchitecture();
    this.enhanceKeyboardShortcuts();
    this.createBackwardCompatibility();
    this.verifyPhase1();
    this.generatePhase1Report();
  }

  backupCurrentState() {
    console.log("\n1️⃣ Backing up current v1.0.0 state");
    console.log("===================================");
    
    try {
      // Backup existing PiP Master instance
      if (window.pipMasterInstance) {
        this.backupData.existingInstance = {
          platform: window.pipMasterInstance.platform,
          videos: window.pipMasterInstance.videos ? Array.from(window.pipMasterInstance.videos) : [],
          settings: window.pipMasterInstance.settingsPanel?.settings || {}
        };
        console.log("✅ Backed up existing PiP Master instance");
      }
      
      // Backup localStorage settings
      const storedSettings = localStorage.getItem('pipMaster_enhancedSettings');
      if (storedSettings) {
        this.backupData.localStorage = JSON.parse(storedSettings);
        console.log("✅ Backed up localStorage settings");
      }
      
      // Backup existing overlays
      const overlays = document.querySelectorAll('.pip-master-overlay');
      this.backupData.overlayCount = overlays.length;
      console.log(`✅ Backed up overlay state (${overlays.length} overlays)`);
      
      // Backup existing keyboard shortcuts
      this.backupData.shortcuts = {
        altP: !!document.querySelector('[data-shortcut="alt-p"]'),
        existing: true
      };
      
      this.upgradeStatus.backup = true;
      console.log("🎯 Phase 1.1 Complete: State backup successful");
      
    } catch (error) {
      console.error("❌ Backup failed:", error);
      this.upgradeStatus.backup = false;
    }
  }

  upgradeSettingsArchitecture() {
    console.log("\n2️⃣ Upgrading settings panel architecture");
    console.log("=========================================");
    
    try {
      // Initialize enhanced settings if not exists
      if (!window.pipMasterInstance) {
        window.pipMasterInstance = {
          platform: 'universal',
          videos: new Set(),
          settingsPanel: null
        };
        console.log("✅ Created base PiP Master instance");
      }
      
      // Create enhanced settings panel manager
      if (!window.pipMasterInstance.settingsPanel) {
        window.pipMasterInstance.settingsPanel = this.createEnhancedSettingsPanel();
        console.log("✅ Created enhanced settings panel");
      } else {
        this.upgradeExistingSettingsPanel();
        console.log("✅ Upgraded existing settings panel");
      }
      
      // Add enhanced settings structure
      this.addEnhancedSettingsStructure();
      
      this.upgradeStatus.settingsArchitecture = true;
      console.log("🎯 Phase 1.2 Complete: Settings architecture upgraded");
      
    } catch (error) {
      console.error("❌ Settings architecture upgrade failed:", error);
      this.upgradeStatus.settingsArchitecture = false;
    }
  }

  createEnhancedSettingsPanel() {
    return {
      settings: this.getEnhancedDefaultSettings(),
      isVisible: false,
      panel: null,
      pipMaster: window.pipMasterInstance,
      
      getDefaultSettings: function() {
        return this.settings;
      },
      
      showPanel: function() {
        if (!this.panel) {
          this.createSettingsPanel();
        }
        this.panel.style.display = 'block';
        this.isVisible = true;
        console.log("⚙️ Enhanced settings panel opened");
      },
      
      hidePanel: function() {
        if (this.panel) {
          this.panel.style.display = 'none';
        }
        this.isVisible = false;
        console.log("⚙️ Enhanced settings panel closed");
      },
      
      createSettingsPanel: function() {
        // Remove existing panel
        const existing = document.getElementById('pip-master-settings-panel');
        if (existing) existing.remove();
        
        // Create enhanced panel
        this.panel = document.createElement('div');
        this.panel.id = 'pip-master-settings-panel';
        this.panel.innerHTML = this.generateEnhancedPanelHTML();
        document.body.appendChild(this.panel);
        
        this.setupEventListeners();
        console.log("✅ Enhanced settings panel created");
      },
      
      generateEnhancedPanelHTML: function() {
        return `
          <div style="
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: white; border-radius: 12px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            padding: 24px; width: 480px; max-height: 80vh; overflow-y: auto; z-index: 10005;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          ">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
              <h2 style="margin: 0; color: #333; font-size: 20px;">⚙️ PiP Master Settings v2.0</h2>
              <button id="pip-settings-close" style="background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
            </div>

            <!-- Smart Auto-PiP Section -->
            <div class="settings-section" style="margin-bottom: 20px;">
              <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">🔄 Smart Auto-PiP</h3>
              <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
                <input type="checkbox" id="auto-enable" ${this.settings.autoEnable ? 'checked' : ''} style="margin-right: 8px;">
                Enable automatic PiP on tab switch
              </label>
              <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
                <input type="checkbox" id="auto-exit" ${this.settings.autoExitOnTabReturn ? 'checked' : ''} style="margin-right: 8px;">
                Auto-exit PiP when returning to tab
              </label>
            </div>

            <!-- Themes Section -->
            <div class="settings-section" style="margin-bottom: 20px;">
              <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">🎨 Overlay Themes</h3>
              <select id="overlay-theme" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="default" ${this.settings.overlayTheme === 'default' ? 'selected' : ''}>Default</option>
                <option value="minimal" ${this.settings.overlayTheme === 'minimal' ? 'selected' : ''}>Minimal</option>
                <option value="neon" ${this.settings.overlayTheme === 'neon' ? 'selected' : ''}>Neon</option>
                <option value="dark" ${this.settings.overlayTheme === 'dark' ? 'selected' : ''}>Dark Pro</option>
                <option value="youtube" ${this.settings.overlayTheme === 'youtube' ? 'selected' : ''}>YouTube Style</option>
              </select>
            </div>

            <!-- Performance Section -->
            <div class="settings-section" style="margin-bottom: 20px;">
              <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">⚡ Performance</h3>
              <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
                <input type="checkbox" id="low-power" ${this.settings.lowPowerMode ? 'checked' : ''} style="margin-right: 8px;">
                Low power mode (battery saving)
              </label>
            </div>

            <!-- Accessibility Section -->
            <div class="settings-section" style="margin-bottom: 20px;">
              <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">♿ Accessibility</h3>
              <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
                <input type="checkbox" id="high-contrast" ${this.settings.highContrast ? 'checked' : ''} style="margin-right: 8px;">
                High contrast mode
              </label>
              <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
                <input type="checkbox" id="reduced-motion" ${this.settings.reducedMotion ? 'checked' : ''} style="margin-right: 8px;">
                Reduced motion
              </label>
            </div>

            <!-- Action Buttons -->
            <div style="display: flex; gap: 12px; margin-top: 24px;">
              <button id="pip-settings-save" style="flex: 1; background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer;">Save Settings</button>
              <button id="pip-settings-reset" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer;">Reset</button>
            </div>

            <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #eee; font-size: 12px; color: #666; text-align: center;">
              Enhanced PiP Master v2.0 • Press Alt+S to toggle • Phase 1 Complete
            </div>
          </div>
        `;
      },
      
      setupEventListeners: function() {
        const closeBtn = this.panel.querySelector('#pip-settings-close');
        const saveBtn = this.panel.querySelector('#pip-settings-save');
        const resetBtn = this.panel.querySelector('#pip-settings-reset');
        
        if (closeBtn) closeBtn.addEventListener('click', () => this.hidePanel());
        if (saveBtn) saveBtn.addEventListener('click', () => this.saveSettings());
        if (resetBtn) resetBtn.addEventListener('click', () => this.resetToDefaults());
      },
      
      saveSettings: function() {
        console.log("💾 Saving enhanced settings...");
        try {
          localStorage.setItem('pipMaster_enhancedSettings', JSON.stringify(this.settings));
          console.log("✅ Enhanced settings saved");
        } catch (error) {
          console.error("❌ Failed to save settings:", error);
        }
      },
      
      loadSettings: function() {
        try {
          const stored = localStorage.getItem('pipMaster_enhancedSettings');
          if (stored) {
            this.settings = { ...this.getDefaultSettings(), ...JSON.parse(stored) };
            console.log("📂 Enhanced settings loaded");
          }
          return this.settings;
        } catch (error) {
          console.error("❌ Failed to load settings:", error);
          return this.getDefaultSettings();
        }
      },
      
      resetToDefaults: function() {
        this.settings = this.getDefaultSettings();
        this.saveSettings();
        console.log("🔄 Settings reset to enhanced defaults");
      }
    };
  }

  getEnhancedDefaultSettings() {
    return {
      // Enhanced v2.0 settings
      autoEnable: false,
      autoExitOnTabReturn: true,
      overlayTheme: 'default',
      lowPowerMode: false,
      scanFrequency: 'normal',
      highContrast: false,
      reducedMotion: false,
      screenReaderMode: false,
      volumeControlEnabled: true,
      speedControlEnabled: true,
      skipControlEnabled: true,
      audioControlEnabled: true,        // NEW in Phase 2
      timelinePreviewEnabled: true,     // NEW in Phase 2
      rememberSitePreferences: true,
      autoApplySiteSettings: true,
      
      // Backward compatibility with v1.0.0
      opacity: 0.9,
      position: 'top-right',
      shortcuts: true
    };
  }

  upgradeExistingSettingsPanel() {
    const existing = window.pipMasterInstance.settingsPanel;
    
    // Merge existing settings with enhanced defaults
    const enhanced = this.getEnhancedDefaultSettings();
    existing.settings = { ...enhanced, ...existing.settings };
    
    // Add enhanced methods
    existing.getDefaultSettings = function() { return enhanced; };
    existing.generateEnhancedPanelHTML = this.createEnhancedSettingsPanel().generateEnhancedPanelHTML;
    
    console.log("✅ Existing settings panel upgraded to v2.0");
  }

  addEnhancedSettingsStructure() {
    const settingsPanel = window.pipMasterInstance.settingsPanel;
    
    // Ensure all enhanced settings exist
    const defaults = this.getEnhancedDefaultSettings();
    Object.keys(defaults).forEach(key => {
      if (!(key in settingsPanel.settings)) {
        settingsPanel.settings[key] = defaults[key];
      }
    });
    
    console.log("✅ Enhanced settings structure added");
  }

  enhanceKeyboardShortcuts() {
    console.log("\n3️⃣ Enhancing keyboard shortcuts");
    console.log("===============================");
    
    try {
      // Remove existing keyboard listeners to avoid conflicts
      this.removeExistingShortcuts();
      
      // Add enhanced keyboard shortcut system
      this.setupEnhancedShortcuts();
      
      this.upgradeStatus.keyboardShortcuts = true;
      console.log("🎯 Phase 1.3 Complete: Keyboard shortcuts enhanced");
      
    } catch (error) {
      console.error("❌ Keyboard shortcuts enhancement failed:", error);
      this.upgradeStatus.keyboardShortcuts = false;
    }
  }

  removeExistingShortcuts() {
    // Remove any existing shortcut listeners
    const existingShortcuts = document.querySelectorAll('[data-pip-shortcut]');
    existingShortcuts.forEach(el => el.remove());
    console.log("🧹 Removed existing shortcut listeners");
  }

  setupEnhancedShortcuts() {
    // Enhanced keyboard shortcut system
    document.addEventListener('keydown', (event) => {
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') return;
      
      // Alt+P: Toggle PiP (backward compatible)
      if (event.altKey && event.key.toLowerCase() === 'p') {
        event.preventDefault();
        this.handlePiPToggle();
        console.log("⌨️ Alt+P: PiP toggle (v1.0.0 compatible)");
      }
      
      // Alt+S: Settings panel (NEW in v2.0)
      if (event.altKey && event.key.toLowerCase() === 's') {
        event.preventDefault();
        this.handleSettingsToggle();
        console.log("⌨️ Alt+S: Settings panel toggle (NEW v2.0)");
      }
      
      // Alt+H: Help (NEW in v2.0)
      if (event.altKey && event.key.toLowerCase() === 'h') {
        event.preventDefault();
        this.showHelp();
        console.log("⌨️ Alt+H: Help (NEW v2.0)");
      }
    });
    
    console.log("✅ Enhanced keyboard shortcuts active:");
    console.log("  Alt+P: Toggle PiP (compatible with v1.0.0)");
    console.log("  Alt+S: Settings panel (NEW)");
    console.log("  Alt+H: Help (NEW)");
  }

  handlePiPToggle() {
    if (window.pipMasterInstance?.togglePiP) {
      window.pipMasterInstance.togglePiP();
    } else {
      // Fallback for basic PiP toggle
      const video = document.querySelector('video');
      if (video) {
        if (document.pictureInPictureElement) {
          document.exitPictureInPicture();
        } else {
          video.requestPictureInPicture();
        }
      }
    }
  }

  handleSettingsToggle() {
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    if (settingsPanel) {
      if (settingsPanel.isVisible) {
        settingsPanel.hidePanel();
      } else {
        settingsPanel.showPanel();
      }
    }
  }

  showHelp() {
    console.log("📖 PiP Master v2.0 Help:");
    console.log("========================");
    console.log("Alt+P: Toggle Picture-in-Picture");
    console.log("Alt+S: Open/close settings panel");
    console.log("Alt+H: Show this help");
    console.log("More shortcuts available in Phase 2 & 3!");
  }

  createBackwardCompatibility() {
    console.log("\n4️⃣ Creating backward compatibility layer");
    console.log("========================================");
    
    try {
      // Ensure v1.0.0 API continues working
      window.pipMaster = window.pipMaster || {};
      
      // Map old methods to new enhanced methods
      window.pipMaster.toggle = () => this.handlePiPToggle();
      window.pipMaster.showSettings = () => this.handleSettingsToggle();
      
      // Preserve old settings format access
      window.pipMaster.getSettings = () => {
        const enhanced = window.pipMasterInstance.settingsPanel.settings;
        return {
          opacity: enhanced.opacity || 0.9,
          position: enhanced.position || 'top-right',
          shortcuts: enhanced.shortcuts !== false
        };
      };
      
      window.pipMaster.setSettings = (settings) => {
        const enhanced = window.pipMasterInstance.settingsPanel.settings;
        if (settings.opacity) enhanced.opacity = settings.opacity;
        if (settings.position) enhanced.position = settings.position;
        if (settings.shortcuts !== undefined) enhanced.shortcuts = settings.shortcuts;
        window.pipMasterInstance.settingsPanel.saveSettings();
      };
      
      this.upgradeStatus.compatibility = true;
      console.log("✅ Backward compatibility layer created");
      console.log("✅ v1.0.0 API methods preserved and functional");
      
    } catch (error) {
      console.error("❌ Backward compatibility creation failed:", error);
      this.upgradeStatus.compatibility = false;
    }
  }

  verifyPhase1() {
    console.log("\n5️⃣ Verifying Phase 1 completion");
    console.log("===============================");
    
    const checks = {
      pipMasterInstance: !!window.pipMasterInstance,
      enhancedSettings: !!window.pipMasterInstance?.settingsPanel,
      settingsStructure: false,
      keyboardShortcuts: true, // Assume working if no errors
      backwardCompatibility: !!window.pipMaster,
      settingsPanel: false
    };
    
    // Check settings structure
    if (window.pipMasterInstance?.settingsPanel?.settings) {
      const settings = window.pipMasterInstance.settingsPanel.settings;
      checks.settingsStructure = 'autoEnable' in settings && 'overlayTheme' in settings;
    }
    
    // Test settings panel creation
    try {
      window.pipMasterInstance.settingsPanel.showPanel();
      checks.settingsPanel = !!document.getElementById('pip-master-settings-panel');
      window.pipMasterInstance.settingsPanel.hidePanel();
    } catch (error) {
      checks.settingsPanel = false;
    }
    
    console.log("Phase 1 verification results:");
    Object.entries(checks).forEach(([check, result]) => {
      console.log(`${result ? '✅' : '❌'} ${check}: ${result}`);
    });
    
    const passedChecks = Object.values(checks).filter(Boolean).length;
    const totalChecks = Object.keys(checks).length;
    const successRate = Math.round((passedChecks / totalChecks) * 100);
    
    console.log(`\n📊 Phase 1 Success Rate: ${passedChecks}/${totalChecks} (${successRate}%)`);
    
    this.upgradeStatus.verification = successRate >= 80;
    
    if (this.upgradeStatus.verification) {
      console.log("🎉 Phase 1 verification PASSED!");
    } else {
      console.log("⚠️ Phase 1 verification needs attention");
    }
  }

  generatePhase1Report() {
    console.log("\n📊 PHASE 1 COMPLETION REPORT");
    console.log("============================");
    
    const { backup, settingsArchitecture, keyboardShortcuts, compatibility, verification } = this.upgradeStatus;
    
    console.log("Phase 1 Status:");
    console.log(`✅ 1.1 State Backup: ${backup ? 'COMPLETE' : 'FAILED'}`);
    console.log(`✅ 1.2 Settings Architecture: ${settingsArchitecture ? 'COMPLETE' : 'FAILED'}`);
    console.log(`✅ 1.3 Keyboard Shortcuts: ${keyboardShortcuts ? 'COMPLETE' : 'FAILED'}`);
    console.log(`✅ 1.4 Backward Compatibility: ${compatibility ? 'COMPLETE' : 'FAILED'}`);
    console.log(`✅ 1.5 Verification: ${verification ? 'PASSED' : 'FAILED'}`);
    
    const completedTasks = Object.values(this.upgradeStatus).filter(Boolean).length;
    const totalTasks = Object.keys(this.upgradeStatus).length;
    const completionRate = Math.round((completedTasks / totalTasks) * 100);
    
    console.log(`\n🏥 Phase 1 Completion: ${completedTasks}/${totalTasks} (${completionRate}%)`);
    
    if (completionRate >= 80) {
      console.log("🎉 PHASE 1 SUCCESSFUL - Ready for Phase 2!");
      console.log("\n🚀 Next Steps:");
      console.log("1. Copy and paste phase2-feature-integration.js");
      console.log("2. Run: new Phase2FeatureIntegration()");
    } else {
      console.log("⚠️ PHASE 1 INCOMPLETE - Fix issues before proceeding");
      console.log("\n🔧 Troubleshooting:");
      console.log("- Check browser console for errors");
      console.log("- Verify localStorage permissions");
      console.log("- Try: window.pipMasterInstance.settingsPanel.showPanel()");
    }
  }
}

// Phase 1 verification command
window.verifyPhase1 = function() {
  console.log("🔍 Phase 1 Quick Verification");
  console.log("=============================");
  
  const checks = {
    instance: !!window.pipMasterInstance,
    settings: !!window.pipMasterInstance?.settingsPanel,
    enhanced: !!window.pipMasterInstance?.settingsPanel?.settings?.autoEnable !== undefined,
    shortcuts: !!window.pipMaster,
    panel: false
  };
  
  // Test panel
  try {
    window.pipMasterInstance.settingsPanel.showPanel();
    checks.panel = !!document.getElementById('pip-master-settings-panel');
    window.pipMasterInstance.settingsPanel.hidePanel();
  } catch (error) {
    checks.panel = false;
  }
  
  Object.entries(checks).forEach(([check, result]) => {
    console.log(`${result ? '✅' : '❌'} ${check}: ${result}`);
  });
  
  const score = Object.values(checks).filter(Boolean).length;
  console.log(`\n📊 Score: ${score}/5 (${score * 20}%)`);
  
  return score >= 4;
};

// Auto-execute Phase 1
console.log("🚀 Auto-executing Phase 1...");
window.phase1Upgrade = new Phase1InfrastructureUpgrade();

console.log("\n📋 Phase 1 Commands:");
console.log("====================");
console.log("verifyPhase1()                    - Quick verification");
console.log("pipMasterInstance.settingsPanel.showPanel() - Test settings panel");
console.log("pipMaster.toggle()                - Test backward compatibility");
