// Video Suitability Debug Script
// Diagnoses exactly why YouTube videos are being rejected

console.log("🔍 Video Suitability Debug Script");
console.log("=================================");

// Function to test video suitability with detailed logging
window.debugVideoSuitability = function(video) {
  if (!video) {
    const videos = document.querySelectorAll('video');
    if (videos.length === 0) {
      console.error("❌ No videos found on page");
      return;
    }
    video = videos[0];
    console.log(`🎯 Testing first video found: ${video.src || video.currentSrc || 'no src'}`);
  }
  
  console.log("\n🔬 DETAILED SUITABILITY CHECK:");
  console.log("==============================");
  
  // Basic video info
  console.log("📹 Video Information:");
  console.log("  Element:", video.tagName);
  console.log("  Source:", video.src || video.currentSrc || "no src");
  console.log("  Ready state:", video.readyState);
  console.log("  Video dimensions:", `${video.videoWidth || '?'}x${video.videoHeight || '?'}`);
  console.log("  Duration:", video.duration || 'unknown');
  console.log("  Paused:", video.paused);
  console.log("  Ended:", video.ended);
  console.log("  Current time:", video.currentTime);
  
  // Step 1: Valid video element check
  console.log("\n1️⃣ Valid Video Element Check:");
  const isValidElement = video && video.tagName === "VIDEO";
  console.log(`  ✓ Is video element: ${isValidElement}`);
  if (!isValidElement) {
    console.error("  ❌ FAILED: Not a valid video element");
    return false;
  }
  
  // Step 2: PiP disabled check
  console.log("\n2️⃣ PiP Disabled Check:");
  const pipDisabled = video.disablePictureInPicture;
  console.log(`  ✓ PiP disabled attribute: ${pipDisabled}`);
  if (pipDisabled) {
    console.error("  ❌ FAILED: PiP explicitly disabled on video");
    return false;
  }
  
  // Step 3: Display visibility check
  console.log("\n3️⃣ Display Visibility Check:");
  const style = window.getComputedStyle(video);
  const displayNone = style.display === "none";
  console.log(`  ✓ Display style: ${style.display}`);
  console.log(`  ✓ Display none: ${displayNone}`);
  if (displayNone) {
    console.error("  ❌ FAILED: Video is display:none");
    return false;
  }
  
  // Step 4: Visibility and opacity check
  console.log("\n4️⃣ Visibility & Opacity Check:");
  const visibility = style.visibility;
  const opacity = style.opacity;
  const completelyHidden = visibility === "hidden" && opacity === "0";
  console.log(`  ✓ Visibility: ${visibility}`);
  console.log(`  ✓ Opacity: ${opacity}`);
  console.log(`  ✓ Completely hidden: ${completelyHidden}`);
  if (completelyHidden) {
    console.error("  ❌ FAILED: Video is completely hidden");
    return false;
  }
  
  // Step 5: Bounding rect size check
  console.log("\n5️⃣ Bounding Rect Size Check:");
  const rect = video.getBoundingClientRect();
  const tooSmallRect = rect.width < 10 || rect.height < 10;
  console.log(`  ✓ Bounding rect: ${rect.width}x${rect.height}`);
  console.log(`  ✓ Too small (< 10x10): ${tooSmallRect}`);
  if (tooSmallRect) {
    console.error(`  ❌ FAILED: Video too small: ${rect.width}x${rect.height}`);
    return false;
  }
  
  // Step 6: Video dimensions check (if metadata loaded)
  console.log("\n6️⃣ Video Dimensions Check:");
  const metadataLoaded = video.readyState >= 1;
  console.log(`  ✓ Metadata loaded (readyState >= 1): ${metadataLoaded}`);
  
  if (metadataLoaded) {
    const noDimensions = video.videoWidth === 0 && video.videoHeight === 0;
    console.log(`  ✓ Video dimensions: ${video.videoWidth}x${video.videoHeight}`);
    console.log(`  ✓ No dimensions (audio-only): ${noDimensions}`);
    
    if (noDimensions) {
      console.error("  ❌ FAILED: Video has no dimensions (might be audio-only)");
      return false;
    }
    
    if (video.videoWidth > 0 && video.videoHeight > 0) {
      const dimensionsTooSmall = video.videoWidth < 20 || video.videoHeight < 20;
      console.log(`  ✓ Dimensions too small (< 20x20): ${dimensionsTooSmall}`);
      if (dimensionsTooSmall) {
        console.error(`  ❌ FAILED: Video dimensions too small: ${video.videoWidth}x${video.videoHeight}`);
        return false;
      }
    }
  } else {
    console.log("  ℹ️ Metadata not loaded yet, skipping dimension checks");
  }
  
  // Step 7: Platform-specific check
  console.log("\n7️⃣ Platform-Specific Check:");
  let platformCheckPassed = true;
  
  if (window.pipMasterInstance) {
    try {
      platformCheckPassed = window.pipMasterInstance.platformSpecificVideoCheck(video);
      console.log(`  ✓ Platform check passed: ${platformCheckPassed}`);
    } catch (error) {
      console.error("  ❌ Platform check error:", error);
      platformCheckPassed = false;
    }
  } else {
    console.log("  ⚠️ Extension instance not available, skipping platform check");
  }
  
  if (!platformCheckPassed) {
    console.error("  ❌ FAILED: Platform-specific check failed (likely ad)");
    return false;
  }
  
  // Step 8: Duration check
  console.log("\n8️⃣ Duration Check:");
  const hasDuration = video.duration !== undefined && video.duration !== null;
  const tooShort = hasDuration && video.duration < 0.1;
  console.log(`  ✓ Has duration: ${hasDuration}`);
  console.log(`  ✓ Duration: ${video.duration}`);
  console.log(`  ✓ Too short (< 0.1s): ${tooShort}`);
  
  if (tooShort) {
    console.error(`  ❌ FAILED: Video too short: ${video.duration}`);
    return false;
  }
  
  // Step 9: Ad detection check
  console.log("\n9️⃣ Ad Detection Check:");
  let isAd = false;
  
  if (window.pipMasterInstance) {
    try {
      isAd = window.pipMasterInstance.isLikelyVideoAd(video);
      console.log(`  ✓ Detected as ad: ${isAd}`);
    } catch (error) {
      console.error("  ❌ Ad detection error:", error);
    }
  } else {
    console.log("  ⚠️ Extension instance not available, skipping ad check");
  }
  
  if (isAd) {
    console.error("  ❌ FAILED: Video appears to be an advertisement");
    return false;
  }
  
  // Final result
  console.log("\n🎉 FINAL RESULT:");
  console.log("================");
  console.log("✅ Video passed ALL suitability checks!");
  console.log("This video should be suitable for Picture-in-Picture");
  
  return true;
};

// Function to test all videos on the page
window.debugAllVideosSuitability = function() {
  console.log("\n🔍 Testing All Videos on Page");
  console.log("=============================");
  
  const videos = document.querySelectorAll('video');
  console.log(`Found ${videos.length} video elements`);
  
  if (videos.length === 0) {
    console.error("❌ No videos found on page");
    return;
  }
  
  videos.forEach((video, index) => {
    console.log(`\n📹 TESTING VIDEO ${index + 1}:`);
    console.log("========================");
    
    const result = window.debugVideoSuitability(video);
    console.log(`Result for video ${index + 1}: ${result ? '✅ PASSED' : '❌ FAILED'}`);
  });
};

// Function to compare extension's suitability check with our debug version
window.compareExtensionSuitability = function(video) {
  if (!video) {
    video = document.querySelector('video');
  }
  
  if (!video) {
    console.error("❌ No video found");
    return;
  }
  
  console.log("\n🔄 Comparing Extension vs Debug Results");
  console.log("======================================");
  
  // Our debug result
  const debugResult = window.debugVideoSuitability(video);
  console.log(`Debug result: ${debugResult ? '✅ PASSED' : '❌ FAILED'}`);
  
  // Extension result
  let extensionResult = null;
  if (window.pipMasterInstance) {
    try {
      extensionResult = window.pipMasterInstance.isVideoSuitableForPiP(video);
      console.log(`Extension result: ${extensionResult ? '✅ PASSED' : '❌ FAILED'}`);
    } catch (error) {
      console.error("Extension suitability check error:", error);
    }
  } else {
    console.log("Extension instance not available");
  }
  
  // Compare results
  if (debugResult !== extensionResult) {
    console.warn("⚠️ MISMATCH: Debug and extension results differ!");
    console.log("This indicates an issue with the extension's suitability logic");
  } else {
    console.log("✅ Results match - extension logic is consistent");
  }
  
  return { debug: debugResult, extension: extensionResult };
};

// Function to test YouTube-specific issues
window.debugYouTubeSuitability = function() {
  console.log("\n🎬 YouTube-Specific Suitability Debug");
  console.log("====================================");
  
  // Check if we're on YouTube
  const isYouTube = window.location.hostname.includes('youtube.com');
  console.log(`On YouTube: ${isYouTube}`);
  
  if (!isYouTube) {
    console.warn("⚠️ Not on YouTube - navigate to YouTube for best results");
  }
  
  // Find YouTube video
  const youtubeSelectors = [
    "#movie_player video",
    ".html5-video-player video",
    ".video-stream",
    "video.video-stream"
  ];
  
  let youtubeVideo = null;
  for (const selector of youtubeSelectors) {
    const videos = document.querySelectorAll(selector);
    if (videos.length > 0) {
      youtubeVideo = videos[0];
      console.log(`Found YouTube video with selector: ${selector}`);
      break;
    }
  }
  
  if (!youtubeVideo) {
    console.error("❌ No YouTube video found");
    return;
  }
  
  // Check YouTube-specific properties
  console.log("\n🎬 YouTube Video Properties:");
  console.log("  Source type:", (youtubeVideo.src || youtubeVideo.currentSrc || '').startsWith('blob:') ? 'blob:' : 'direct');
  console.log("  In #movie_player:", !!youtubeVideo.closest('#movie_player'));
  console.log("  In .html5-video-player:", !!youtubeVideo.closest('.html5-video-player'));
  console.log("  In ad container:", !!youtubeVideo.closest('.ad-showing, .video-ads, [class*="ad-"]'));
  
  // Test suitability
  const result = window.debugVideoSuitability(youtubeVideo);
  console.log(`\n🎯 YouTube video suitability: ${result ? '✅ PASSED' : '❌ FAILED'}`);
  
  return result;
};

// Auto-run on YouTube
if (window.location.hostname.includes('youtube.com')) {
  console.log("\n🎬 Auto-running YouTube suitability debug...");
  setTimeout(() => {
    window.debugYouTubeSuitability();
  }, 2000);
}

// Display available commands
console.log("\n📋 Available Debug Commands:");
console.log("============================");
console.log("debugVideoSuitability(video)     - Test specific video (or first video if none provided)");
console.log("debugAllVideosSuitability()      - Test all videos on page");
console.log("compareExtensionSuitability()    - Compare extension vs debug results");
console.log("debugYouTubeSuitability()        - YouTube-specific testing");
console.log("");
console.log("🚀 Quick start: Run debugYouTubeSuitability() on YouTube");
console.log("🔍 For specific video: debugVideoSuitability(document.querySelector('#movie_player video'))");
