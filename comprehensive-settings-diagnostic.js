// Comprehensive Settings & Features Diagnostic Test
// Complete verification of all PiP Master settings and functionality

console.log("🔧 Comprehensive Settings & Features Diagnostic");
console.log("===============================================");

// Comprehensive Diagnostic Manager
class ComprehensiveSettingsDiagnostic {
  constructor() {
    this.results = {
      settingsPanel: {},
      featureIntegration: {},
      settingsPersistence: {},
      crossFeatureCommunication: {},
      configurationConflicts: {},
      overall: {},
      errors: []
    };
    this.originalSettings = null;
    this.runComprehensiveDiagnostic();
  }

  async runComprehensiveDiagnostic() {
    console.log("🚀 Starting comprehensive diagnostic test...");
    
    // Backup current settings
    await this.backupCurrentSettings();
    
    // Run all diagnostic tests
    await this.testSettingsPanelFunctionality();
    await this.validateFeatureIntegration();
    await this.testSettingsPersistence();
    await this.verifyCrossFeatureCommunication();
    await this.checkConfigurationConflicts();
    
    // Restore original settings
    await this.restoreOriginalSettings();
    
    this.generateComprehensiveReport();
  }

  async backupCurrentSettings() {
    console.log("\n💾 Backing up current settings...");
    
    try {
      if (window.pipMasterInstance?.settingsPanel?.settings) {
        this.originalSettings = JSON.parse(JSON.stringify(window.pipMasterInstance.settingsPanel.settings));
        console.log("✅ Settings backed up successfully");
      } else {
        console.log("⚠️ No existing settings found to backup");
      }
    } catch (error) {
      console.error("❌ Failed to backup settings:", error);
      this.results.errors.push(`Backup error: ${error.message}`);
    }
  }

  async testSettingsPanelFunctionality() {
    console.log("\n1️⃣ Settings Panel Functionality Test");
    console.log("====================================");
    
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    
    if (!settingsPanel) {
      console.error("❌ Settings panel not available");
      this.results.settingsPanel = { available: false, reason: 'Settings panel not initialized' };
      return;
    }
    
    const tests = {
      panelExists: !!settingsPanel.panel,
      panelInDOM: !!document.getElementById('pip-master-settings-panel'),
      showMethod: typeof settingsPanel.showPanel === 'function',
      hideMethod: typeof settingsPanel.hidePanel === 'function',
      saveMethod: typeof settingsPanel.saveCurrentSettings === 'function',
      resetMethod: typeof settingsPanel.resetToDefaults === 'function'
    };
    
    console.log("Settings panel structure:");
    Object.entries(tests).forEach(([test, result]) => {
      console.log(`${result ? '✅' : '❌'} ${test}: ${result}`);
    });
    
    // Test panel visibility toggle
    console.log("\n🧪 Testing panel visibility...");
    try {
      settingsPanel.showPanel();
      const visibleAfterShow = settingsPanel.isVisible;
      console.log(`Show panel: ${visibleAfterShow ? '✅ Success' : '❌ Failed'}`);
      
      settingsPanel.hidePanel();
      const hiddenAfterHide = !settingsPanel.isVisible;
      console.log(`Hide panel: ${hiddenAfterHide ? '✅ Success' : '❌ Failed'}`);
      
      tests.visibilityToggle = visibleAfterShow && hiddenAfterHide;
    } catch (error) {
      console.error("❌ Panel visibility test failed:", error);
      tests.visibilityToggle = false;
      this.results.errors.push(`Panel visibility error: ${error.message}`);
    }
    
    // Test form elements
    console.log("\n🧪 Testing form elements...");
    const formElements = {
      autoEnable: document.getElementById('auto-enable'),
      autoExit: document.getElementById('auto-exit'),
      overlayTheme: document.getElementById('overlay-theme'),
      lowPower: document.getElementById('low-power'),
      scanFrequency: document.getElementById('scan-frequency'),
      highContrast: document.getElementById('high-contrast'),
      reducedMotion: document.getElementById('reduced-motion'),
      volumeControl: document.getElementById('volume-control'),
      speedControl: document.getElementById('speed-control'),
      skipControl: document.getElementById('skip-control'),
      rememberSites: document.getElementById('remember-sites'),
      autoApplySites: document.getElementById('auto-apply-sites')
    };
    
    const formElementResults = {};
    Object.entries(formElements).forEach(([name, element]) => {
      const exists = !!element;
      formElementResults[name] = exists;
      console.log(`${exists ? '✅' : '❌'} ${name}: ${exists ? 'Found' : 'Missing'}`);
    });
    
    this.results.settingsPanel = {
      available: true,
      structure: tests,
      formElements: formElementResults,
      allElementsPresent: Object.values(formElementResults).every(Boolean)
    };
  }

  async validateFeatureIntegration() {
    console.log("\n2️⃣ Feature Integration Validation");
    console.log("=================================");
    
    const features = {
      smartAutoPiP: window.pipMasterInstance?.smartAutoPiP,
      themeManager: window.pipMasterInstance?.themeManager,
      advancedControls: window.pipMasterInstance?.advancedControls,
      accessibility: window.pipMasterInstance?.accessibility,
      performanceOptimizer: window.pipMasterInstance?.performanceOptimizer,
      sitePreferences: window.pipMasterInstance?.sitePreferences,
      timelineControl: window.pipMasterInstance?.timelineControl
    };
    
    const featureResults = {};
    
    Object.entries(features).forEach(([name, feature]) => {
      const available = !!feature;
      console.log(`${available ? '✅' : '❌'} ${name}: ${available ? 'Available' : 'Missing'}`);
      
      if (available) {
        // Test feature methods
        const methods = this.getFeatureMethods(feature, name);
        featureResults[name] = {
          available: true,
          methods: methods,
          functional: Object.values(methods).every(Boolean)
        };
      } else {
        featureResults[name] = { available: false };
      }
    });
    
    // Test feature responses to settings
    console.log("\n🧪 Testing feature responses to settings...");
    await this.testFeatureSettingsResponse();
    
    this.results.featureIntegration = {
      features: featureResults,
      totalAvailable: Object.values(featureResults).filter(f => f.available).length,
      totalExpected: Object.keys(features).length
    };
  }

  getFeatureMethods(feature, featureName) {
    const methodMap = {
      smartAutoPiP: ['enable', 'disable'],
      themeManager: ['setTheme', 'getAvailableThemes'],
      accessibility: ['enableHighContrastMode', 'disableHighContrastMode', 'enableReducedMotionMode'],
      performanceOptimizer: ['getPerformanceStats'],
      sitePreferences: ['getSitePreferences', 'setSitePreference'],
      timelineControl: ['show', 'hide', 'toggle']
    };
    
    const expectedMethods = methodMap[featureName] || [];
    const methodResults = {};
    
    expectedMethods.forEach(method => {
      methodResults[method] = typeof feature[method] === 'function';
    });
    
    return methodResults;
  }

  async testFeatureSettingsResponse() {
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    if (!settingsPanel) return;
    
    // Test theme change
    console.log("🎨 Testing theme change response...");
    try {
      if (window.pipMasterInstance.themeManager) {
        const originalTheme = settingsPanel.settings.overlayTheme;
        window.pipMasterInstance.themeManager.setTheme('neon');
        
        // Check if overlays updated
        const overlays = document.querySelectorAll('.pip-master-overlay');
        const themeApplied = overlays.length === 0 || Array.from(overlays).some(overlay => {
          const container = overlay.querySelector('.pip-master-container');
          return container && getComputedStyle(container).background.includes('cyan');
        });
        
        console.log(`Theme change response: ${themeApplied ? '✅ Success' : '⚠️ No overlays to test'}`);
        
        // Restore original theme
        window.pipMasterInstance.themeManager.setTheme(originalTheme);
      }
    } catch (error) {
      console.error("❌ Theme test failed:", error);
      this.results.errors.push(`Theme test error: ${error.message}`);
    }
    
    // Test accessibility toggle
    console.log("♿ Testing accessibility toggle...");
    try {
      if (window.pipMasterInstance.accessibility) {
        window.pipMasterInstance.accessibility.enableHighContrastMode();
        const highContrastStyle = document.getElementById('pip-master-high-contrast');
        const accessibilityWorking = !!highContrastStyle;
        
        console.log(`Accessibility toggle: ${accessibilityWorking ? '✅ Success' : '❌ Failed'}`);
        
        // Cleanup
        window.pipMasterInstance.accessibility.disableHighContrastMode();
      }
    } catch (error) {
      console.error("❌ Accessibility test failed:", error);
      this.results.errors.push(`Accessibility test error: ${error.message}`);
    }
  }

  async testSettingsPersistence() {
    console.log("\n3️⃣ Settings Persistence Test");
    console.log("=============================");
    
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    if (!settingsPanel) {
      this.results.settingsPersistence = { available: false };
      return;
    }
    
    // Test saving settings
    console.log("💾 Testing settings save...");
    const testSettings = {
      autoEnable: true,
      overlayTheme: 'neon',
      highContrast: true,
      lowPowerMode: false
    };
    
    try {
      // Apply test settings
      Object.assign(settingsPanel.settings, testSettings);
      settingsPanel.saveSettings();
      
      // Verify localStorage
      const savedSettings = localStorage.getItem('pipMaster_enhancedSettings');
      const parsedSettings = savedSettings ? JSON.parse(savedSettings) : null;
      
      const saveWorked = parsedSettings && 
                        parsedSettings.autoEnable === testSettings.autoEnable &&
                        parsedSettings.overlayTheme === testSettings.overlayTheme;
      
      console.log(`Settings save: ${saveWorked ? '✅ Success' : '❌ Failed'}`);
      
      // Test loading settings
      console.log("📂 Testing settings load...");
      const newSettingsPanel = { loadSettings: settingsPanel.loadSettings.bind(settingsPanel) };
      const loadedSettings = newSettingsPanel.loadSettings();
      
      const loadWorked = loadedSettings &&
                        loadedSettings.autoEnable === testSettings.autoEnable &&
                        loadedSettings.overlayTheme === testSettings.overlayTheme;
      
      console.log(`Settings load: ${loadWorked ? '✅ Success' : '❌ Failed'}`);
      
      this.results.settingsPersistence = {
        available: true,
        saveWorked: saveWorked,
        loadWorked: loadWorked,
        localStorage: !!savedSettings
      };
      
    } catch (error) {
      console.error("❌ Settings persistence test failed:", error);
      this.results.settingsPersistence = {
        available: true,
        saveWorked: false,
        loadWorked: false,
        error: error.message
      };
      this.results.errors.push(`Persistence error: ${error.message}`);
    }
  }

  async verifyCrossFeatureCommunication() {
    console.log("\n4️⃣ Cross-Feature Communication Test");
    console.log("====================================");
    
    const tests = {};
    
    // Test settings panel → theme manager
    console.log("🔗 Testing settings → theme communication...");
    try {
      if (window.pipMasterInstance.settingsPanel && window.pipMasterInstance.themeManager) {
        const settingsPanel = window.pipMasterInstance.settingsPanel;
        const originalTheme = settingsPanel.settings.overlayTheme;
        
        // Change theme via settings
        settingsPanel.settings.overlayTheme = 'dark';
        settingsPanel.applySettings();
        
        // Check if theme manager updated
        const currentTheme = window.pipMasterInstance.themeManager.currentTheme;
        tests.settingsToTheme = currentTheme === 'dark';
        
        console.log(`Settings → Theme: ${tests.settingsToTheme ? '✅ Success' : '❌ Failed'}`);
        
        // Restore
        settingsPanel.settings.overlayTheme = originalTheme;
        settingsPanel.applySettings();
      }
    } catch (error) {
      console.error("❌ Settings → Theme test failed:", error);
      tests.settingsToTheme = false;
    }
    
    // Test settings panel → performance optimizer
    console.log("⚡ Testing settings → performance communication...");
    try {
      if (window.pipMasterInstance.settingsPanel && window.pipMasterInstance.performanceOptimizer) {
        const settingsPanel = window.pipMasterInstance.settingsPanel;
        const performanceOptimizer = window.pipMasterInstance.performanceOptimizer;
        
        const originalLowPower = settingsPanel.settings.lowPowerMode;
        
        // Enable low power mode
        settingsPanel.settings.lowPowerMode = true;
        settingsPanel.applySettings();
        
        // Check if performance optimizer updated
        const stats = performanceOptimizer.getPerformanceStats();
        tests.settingsToPerformance = stats.lowPowerMode === true;
        
        console.log(`Settings → Performance: ${tests.settingsToPerformance ? '✅ Success' : '❌ Failed'}`);
        
        // Restore
        settingsPanel.settings.lowPowerMode = originalLowPower;
        settingsPanel.applySettings();
      }
    } catch (error) {
      console.error("❌ Settings → Performance test failed:", error);
      tests.settingsToPerformance = false;
    }
    
    // Test site preferences → settings integration
    console.log("🌐 Testing site preferences integration...");
    try {
      if (window.pipMasterInstance.sitePreferences) {
        const sitePrefs = window.pipMasterInstance.sitePreferences;
        
        // Set a site preference
        sitePrefs.setSitePreference('theme', 'minimal');
        const retrievedPref = sitePrefs.getSitePreferences().theme;
        
        tests.sitePreferencesIntegration = retrievedPref === 'minimal';
        console.log(`Site Preferences: ${tests.sitePreferencesIntegration ? '✅ Success' : '❌ Failed'}`);
      }
    } catch (error) {
      console.error("❌ Site preferences test failed:", error);
      tests.sitePreferencesIntegration = false;
    }
    
    this.results.crossFeatureCommunication = {
      tests: tests,
      allPassed: Object.values(tests).every(Boolean),
      passedCount: Object.values(tests).filter(Boolean).length,
      totalTests: Object.keys(tests).length
    };
  }

  async checkConfigurationConflicts() {
    console.log("\n5️⃣ Configuration Conflicts Check");
    console.log("=================================");
    
    const conflicts = [];
    const warnings = [];
    
    // Check for conflicting settings
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    if (settingsPanel) {
      const settings = settingsPanel.settings;
      
      // Check low power mode conflicts
      if (settings.lowPowerMode && settings.autoEnable) {
        conflicts.push("Low power mode enabled with Auto-PiP - may cause conflicts");
      }
      
      // Check accessibility conflicts
      if (settings.highContrast && settings.overlayTheme === 'neon') {
        warnings.push("High contrast mode with neon theme may not be optimal");
      }
      
      // Check performance conflicts
      if (settings.scanFrequency === 'minimal' && settings.autoEnable) {
        warnings.push("Minimal scan frequency may affect Auto-PiP responsiveness");
      }
    }
    
    // Check for multiple initialization
    const multipleInits = [];
    if (window.pipMasterInstance && window.pipMasterEnhanced) {
      multipleInits.push("Multiple initialization systems detected");
    }
    
    // Check for DOM conflicts
    const settingsPanels = document.querySelectorAll('#pip-master-settings-panel');
    if (settingsPanels.length > 1) {
      conflicts.push(`Multiple settings panels detected: ${settingsPanels.length}`);
    }
    
    console.log(`Conflicts found: ${conflicts.length}`);
    console.log(`Warnings found: ${warnings.length}`);
    
    conflicts.forEach(conflict => console.log(`❌ ${conflict}`));
    warnings.forEach(warning => console.log(`⚠️ ${warning}`));
    
    if (conflicts.length === 0 && warnings.length === 0) {
      console.log("✅ No configuration conflicts detected");
    }
    
    this.results.configurationConflicts = {
      conflicts: conflicts,
      warnings: warnings,
      multipleInits: multipleInits,
      hasConflicts: conflicts.length > 0,
      hasWarnings: warnings.length > 0
    };
  }

  async restoreOriginalSettings() {
    console.log("\n🔄 Restoring original settings...");
    
    try {
      if (this.originalSettings && window.pipMasterInstance?.settingsPanel) {
        window.pipMasterInstance.settingsPanel.settings = this.originalSettings;
        window.pipMasterInstance.settingsPanel.saveSettings();
        window.pipMasterInstance.settingsPanel.applySettings();
        console.log("✅ Original settings restored");
      }
    } catch (error) {
      console.error("❌ Failed to restore settings:", error);
      this.results.errors.push(`Restore error: ${error.message}`);
    }
  }

  generateComprehensiveReport() {
    console.log("\n📊 COMPREHENSIVE DIAGNOSTIC REPORT");
    console.log("===================================");
    
    const { settingsPanel, featureIntegration, settingsPersistence, crossFeatureCommunication, configurationConflicts, errors } = this.results;
    
    // Calculate overall health score
    let score = 0;
    let maxScore = 0;
    
    // Settings Panel (25 points)
    maxScore += 25;
    if (settingsPanel.available) score += 10;
    if (settingsPanel.allElementsPresent) score += 10;
    if (settingsPanel.structure?.visibilityToggle) score += 5;
    
    // Feature Integration (25 points)
    maxScore += 25;
    if (featureIntegration.totalAvailable >= 5) score += 15;
    if (featureIntegration.totalAvailable === featureIntegration.totalExpected) score += 10;
    
    // Settings Persistence (20 points)
    maxScore += 20;
    if (settingsPersistence.saveWorked) score += 10;
    if (settingsPersistence.loadWorked) score += 10;
    
    // Cross-Feature Communication (20 points)
    maxScore += 20;
    if (crossFeatureCommunication.allPassed) score += 20;
    else score += (crossFeatureCommunication.passedCount / crossFeatureCommunication.totalTests) * 20;
    
    // Configuration (10 points)
    maxScore += 10;
    if (!configurationConflicts.hasConflicts) score += 10;
    else if (!configurationConflicts.hasWarnings) score += 5;
    
    const percentage = Math.round((score / maxScore) * 100);
    
    console.log(`🏥 Overall Health Score: ${score}/${maxScore} (${percentage}%)`);
    
    if (percentage >= 90) {
      console.log("🎉 EXCELLENT: All systems functioning optimally");
    } else if (percentage >= 75) {
      console.log("✅ GOOD: Most systems working well");
    } else if (percentage >= 60) {
      console.log("⚠️ FAIR: Some issues need attention");
    } else {
      console.log("❌ POOR: Significant issues detected");
    }
    
    // Detailed breakdown
    console.log("\n📋 Detailed Results:");
    console.log(`Settings Panel: ${settingsPanel.available ? '✅' : '❌'} (${settingsPanel.allElementsPresent ? 'All elements present' : 'Missing elements'})`);
    console.log(`Feature Integration: ${featureIntegration.totalAvailable}/${featureIntegration.totalExpected} features available`);
    console.log(`Settings Persistence: ${settingsPersistence.saveWorked && settingsPersistence.loadWorked ? '✅' : '❌'}`);
    console.log(`Cross-Feature Communication: ${crossFeatureCommunication.passedCount}/${crossFeatureCommunication.totalTests} tests passed`);
    console.log(`Configuration Conflicts: ${configurationConflicts.hasConflicts ? '❌' : '✅'} (${configurationConflicts.conflicts.length} conflicts, ${configurationConflicts.warnings.length} warnings)`);
    
    if (errors.length > 0) {
      console.log(`\n❌ Errors encountered: ${errors.length}`);
      errors.forEach((error, index) => console.log(`${index + 1}. ${error}`));
    }
    
    // Recommendations
    console.log("\n💡 Recommendations:");
    if (!settingsPanel.available) {
      console.log("1. Initialize settings panel: initializeSettingsPanel()");
    }
    if (featureIntegration.totalAvailable < featureIntegration.totalExpected) {
      console.log("2. Initialize missing features");
    }
    if (!settingsPersistence.saveWorked) {
      console.log("3. Check localStorage permissions and functionality");
    }
    if (configurationConflicts.hasConflicts) {
      console.log("4. Resolve configuration conflicts");
    }
    if (errors.length > 0) {
      console.log("5. Address JavaScript errors listed above");
    }
    
    this.results.overall = {
      score: score,
      maxScore: maxScore,
      percentage: percentage,
      status: percentage >= 75 ? 'healthy' : percentage >= 60 ? 'needs_attention' : 'critical'
    };
    
    return this.results;
  }
}

// Quick settings test function
window.quickSettingsTest = function() {
  console.log("⚡ Quick Settings Test");
  console.log("=====================");
  
  const checks = {
    settingsPanel: !!window.pipMasterInstance?.settingsPanel,
    panelInDOM: !!document.getElementById('pip-master-settings-panel'),
    canOpen: false,
    canSave: false,
    localStorage: false
  };
  
  // Test panel opening
  try {
    if (window.pipMasterInstance?.settingsPanel) {
      window.pipMasterInstance.settingsPanel.showPanel();
      checks.canOpen = window.pipMasterInstance.settingsPanel.isVisible;
      window.pipMasterInstance.settingsPanel.hidePanel();
    }
  } catch (error) {
    console.error("Panel open test failed:", error);
  }
  
  // Test saving
  try {
    if (window.pipMasterInstance?.settingsPanel) {
      window.pipMasterInstance.settingsPanel.saveSettings();
      checks.canSave = true;
    }
  } catch (error) {
    console.error("Save test failed:", error);
  }
  
  // Test localStorage
  try {
    const settings = localStorage.getItem('pipMaster_enhancedSettings');
    checks.localStorage = !!settings;
  } catch (error) {
    console.error("localStorage test failed:", error);
  }
  
  console.log("Quick test results:");
  Object.entries(checks).forEach(([check, result]) => {
    console.log(`${result ? '✅' : '❌'} ${check}: ${result}`);
  });
  
  const workingCount = Object.values(checks).filter(Boolean).length;
  const percentage = Math.round((workingCount / Object.keys(checks).length) * 100);
  
  console.log(`\n📊 Quick Score: ${workingCount}/${Object.keys(checks).length} (${percentage}%)`);
  
  return { percentage, checks };
};

// Auto-run comprehensive diagnostic
console.log("🚀 Auto-running comprehensive settings diagnostic...");
window.comprehensiveSettingsDiagnostic = new ComprehensiveSettingsDiagnostic();

console.log("\n📋 Available Diagnostic Commands:");
console.log("=================================");
console.log("quickSettingsTest()                           - Quick settings verification");
console.log("new ComprehensiveSettingsDiagnostic()         - Full comprehensive test");
console.log("pipMasterInstance.settingsPanel.showPanel()   - Open settings panel");
console.log("pipMasterInstance.settingsPanel.applySettings() - Apply current settings");
