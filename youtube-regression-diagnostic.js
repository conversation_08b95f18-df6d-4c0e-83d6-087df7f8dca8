// YouTube Regression Diagnostic Script
// Comprehensive diagnosis of YouTube functionality issues

console.log("🔍 YouTube Regression Diagnostic");
console.log("================================");

// Diagnostic Manager
class YouTubeRegressionDiagnostic {
  constructor() {
    this.results = {
      environment: {},
      coreDetection: {},
      enhancedFeatures: {},
      conflicts: {},
      errors: []
    };
    this.runDiagnostic();
  }

  runDiagnostic() {
    console.log("🚀 Starting comprehensive YouTube diagnostic...");
    
    this.checkEnvironment();
    this.testCoreDetection();
    this.testEnhancedFeatures();
    this.checkForConflicts();
    this.generateReport();
  }

  checkEnvironment() {
    console.log("\n1️⃣ Environment Check");
    console.log("====================");
    
    const env = {
      isYouTube: window.location.hostname.includes('youtube.com'),
      isVideoPage: window.location.pathname.includes('/watch'),
      url: window.location.href,
      pipSupported: 'pictureInPictureEnabled' in document,
      pipEnabled: document.pictureInPictureEnabled,
      extensionLoaded: !!window.pipMasterContentLoaded,
      instanceAvailable: !!window.pipMasterInstance,
      enhancedLoaded: !!window.pipMasterEnhanced
    };

    Object.entries(env).forEach(([key, value]) => {
      const status = value ? '✅' : '❌';
      console.log(`${status} ${key}: ${value}`);
      if (!value && ['isYouTube', 'pipSupported', 'extensionLoaded'].includes(key)) {
        this.results.errors.push(`Critical: ${key} is false`);
      }
    });

    this.results.environment = env;
  }

  testCoreDetection() {
    console.log("\n2️⃣ Core Detection Test");
    console.log("======================");
    
    // Test basic video detection (Google-style)
    const basicVideos = document.querySelectorAll('video');
    console.log(`📹 Basic video query: ${basicVideos.length} videos found`);
    
    // Test Google-style filtering
    const googleStyleVideos = Array.from(basicVideos).filter(video => {
      const rect = video.getBoundingClientRect();
      const style = getComputedStyle(video);
      
      return (
        video.tagName === 'VIDEO' &&
        !video.disablePictureInPicture &&
        (rect.width > 0 || rect.height > 0 || video.videoWidth > 0 || video.videoHeight > 0) &&
        style.display !== 'none'
      );
    });
    
    console.log(`🎯 Google-style filtered: ${googleStyleVideos.length} suitable videos`);
    
    // Test YouTube-specific selectors
    const youtubeSelectors = [
      "#movie_player video",
      ".html5-video-player video",
      ".video-stream",
      "video.video-stream"
    ];
    
    let youtubeSpecificVideos = [];
    youtubeSelectors.forEach(selector => {
      try {
        const videos = document.querySelectorAll(selector);
        console.log(`🎬 "${selector}": ${videos.length} videos`);
        videos.forEach(video => {
          if (!youtubeSpecificVideos.includes(video)) {
            youtubeSpecificVideos.push(video);
          }
        });
      } catch (error) {
        console.error(`❌ Selector "${selector}" failed:`, error.message);
        this.results.errors.push(`Selector error: ${selector} - ${error.message}`);
      }
    });
    
    console.log(`🎯 YouTube-specific total: ${youtubeSpecificVideos.length} videos`);
    
    // Test extension's detection method
    let extensionDetectedVideos = 0;
    if (window.pipMasterInstance && window.pipMasterInstance.performUniversalVideoScan) {
      try {
        const beforeCount = window.pipMasterInstance.videos ? window.pipMasterInstance.videos.size : 0;
        window.pipMasterInstance.performUniversalVideoScan();
        const afterCount = window.pipMasterInstance.videos ? window.pipMasterInstance.videos.size : 0;
        extensionDetectedVideos = afterCount;
        console.log(`🔧 Extension detection: ${extensionDetectedVideos} videos (${afterCount - beforeCount} new)`);
      } catch (error) {
        console.error(`❌ Extension detection failed:`, error.message);
        this.results.errors.push(`Extension detection error: ${error.message}`);
      }
    } else {
      console.error(`❌ Extension detection method not available`);
      this.results.errors.push('Extension detection method missing');
    }
    
    this.results.coreDetection = {
      basicVideos: basicVideos.length,
      googleStyleVideos: googleStyleVideos.length,
      youtubeSpecificVideos: youtubeSpecificVideos.length,
      extensionDetectedVideos: extensionDetectedVideos,
      workingSelectors: youtubeSelectors.filter(selector => {
        try {
          return document.querySelectorAll(selector).length > 0;
        } catch {
          return false;
        }
      })
    };
  }

  testEnhancedFeatures() {
    console.log("\n3️⃣ Enhanced Features Test");
    console.log("=========================");
    
    const features = {
      smartAutoPiP: !!window.pipMasterInstance?.smartAutoPiP,
      themeManager: !!window.pipMasterInstance?.themeManager,
      advancedControls: !!window.pipMasterInstance?.advancedControls,
      sitePreferences: !!window.pipMasterInstance?.sitePreferences,
      accessibility: !!window.pipMasterInstance?.accessibility,
      performanceOptimizer: !!window.pipMasterInstance?.performanceOptimizer,
      crossPlatformDetector: !!window.pipMasterInstance?.crossPlatformDetector,
      settingsPanel: !!window.pipMasterInstance?.settingsPanel
    };
    
    Object.entries(features).forEach(([feature, available]) => {
      const status = available ? '✅' : '❌';
      console.log(`${status} ${feature}: ${available ? 'Available' : 'Missing'}`);
    });
    
    // Test if enhanced features are interfering
    if (window.pipMasterInstance) {
      // Check if original methods were overridden
      const hasOriginalMethods = {
        originalVideoScan: !!window.pipMasterInstance._originalFindVideos,
        originalSuitability: !!window.pipMasterInstance._originalIsVideoSuitable,
        originalTogglePiP: !!window.pipMasterInstance._originalTogglePiP
      };
      
      console.log("\n🔄 Method Override Check:");
      Object.entries(hasOriginalMethods).forEach(([method, exists]) => {
        const status = exists ? '⚠️' : '✅';
        console.log(`${status} ${method}: ${exists ? 'Overridden' : 'Original'}`);
        if (exists) {
          this.results.errors.push(`Method overridden: ${method}`);
        }
      });
    }
    
    this.results.enhancedFeatures = features;
  }

  checkForConflicts() {
    console.log("\n4️⃣ Conflict Detection");
    console.log("=====================");
    
    const conflicts = [];
    
    // Check for multiple event listeners
    const eventListenerCount = this.countEventListeners();
    console.log(`📡 Event listeners: ${eventListenerCount.total} total`);
    
    if (eventListenerCount.total > 20) {
      conflicts.push('Excessive event listeners detected');
      console.warn(`⚠️ High event listener count: ${eventListenerCount.total}`);
    }
    
    // Check for DOM pollution
    const overlayCount = document.querySelectorAll('.pip-master-overlay').length;
    const settingsPanels = document.querySelectorAll('#pip-master-settings-panel').length;
    
    console.log(`🎨 Overlays in DOM: ${overlayCount}`);
    console.log(`⚙️ Settings panels: ${settingsPanels}`);
    
    if (overlayCount > 10) {
      conflicts.push('Excessive overlays in DOM');
    }
    
    if (settingsPanels > 1) {
      conflicts.push('Multiple settings panels detected');
    }
    
    // Check for JavaScript errors
    const errorCount = this.results.errors.length;
    console.log(`❌ JavaScript errors: ${errorCount}`);
    
    // Check for performance issues
    if (window.pipMasterInstance?.performanceOptimizer) {
      const perfStats = window.pipMasterInstance.performanceOptimizer.getPerformanceStats();
      console.log(`⚡ Performance stats:`, perfStats);
      
      if (perfStats.scanThrottle > 5000) {
        conflicts.push('Scan throttle too high');
      }
    }
    
    this.results.conflicts = {
      detected: conflicts,
      overlayCount,
      settingsPanels,
      eventListeners: eventListenerCount.total,
      errors: errorCount
    };
    
    if (conflicts.length > 0) {
      console.warn(`⚠️ Conflicts detected:`, conflicts);
    } else {
      console.log(`✅ No conflicts detected`);
    }
  }

  countEventListeners() {
    // Approximate event listener count (not perfect but gives an idea)
    let total = 0;
    
    // Check common elements
    const elements = [document, window, ...document.querySelectorAll('video, .pip-master-overlay')];
    
    elements.forEach(element => {
      if (element && element.getEventListeners) {
        const listeners = element.getEventListeners();
        total += Object.values(listeners).reduce((sum, arr) => sum + arr.length, 0);
      }
    });
    
    return { total };
  }

  generateReport() {
    console.log("\n📊 DIAGNOSTIC REPORT");
    console.log("====================");
    
    const { environment, coreDetection, enhancedFeatures, conflicts, errors } = this.results;
    
    // Overall health score
    let healthScore = 100;
    
    if (!environment.isYouTube) healthScore -= 50;
    if (!environment.extensionLoaded) healthScore -= 30;
    if (coreDetection.extensionDetectedVideos === 0 && coreDetection.basicVideos > 0) healthScore -= 20;
    if (errors.length > 0) healthScore -= (errors.length * 5);
    if (conflicts.detected.length > 0) healthScore -= (conflicts.detected.length * 10);
    
    healthScore = Math.max(0, healthScore);
    
    console.log(`🏥 Overall Health Score: ${healthScore}/100`);
    
    if (healthScore < 50) {
      console.error("🚨 CRITICAL ISSUES DETECTED");
    } else if (healthScore < 80) {
      console.warn("⚠️ ISSUES DETECTED");
    } else {
      console.log("✅ SYSTEM HEALTHY");
    }
    
    // Specific recommendations
    console.log("\n💡 RECOMMENDATIONS:");
    
    if (!environment.extensionLoaded) {
      console.log("1. 🔧 Reload the extension");
    }
    
    if (coreDetection.extensionDetectedVideos === 0 && coreDetection.basicVideos > 0) {
      console.log("2. 🎯 Core detection is broken - needs immediate fix");
    }
    
    if (conflicts.detected.includes('Method overridden')) {
      console.log("3. 🔄 Enhanced features may have broken core functionality");
    }
    
    if (conflicts.overlayCount > 10) {
      console.log("4. 🧹 Clean up excessive overlays");
    }
    
    if (errors.length > 0) {
      console.log("5. ❌ Fix JavaScript errors:");
      errors.forEach((error, i) => console.log(`   ${i + 1}. ${error}`));
    }
    
    // Quick fix commands
    console.log("\n🔧 QUICK FIX COMMANDS:");
    console.log("======================");
    
    if (coreDetection.extensionDetectedVideos === 0) {
      console.log("// Restore core functionality");
      console.log("restoreCoreYouTubeFunctionality()");
    }
    
    if (conflicts.overlayCount > 5) {
      console.log("// Clean up overlays");
      console.log("cleanupExcessiveOverlays()");
    }
    
    if (conflicts.detected.length > 0) {
      console.log("// Reset enhanced features");
      console.log("resetEnhancedFeatures()");
    }
    
    console.log("// Force YouTube detection");
    console.log("forceYouTubeDetection()");
    
    return this.results;
  }
}

// Quick fix functions
window.restoreCoreYouTubeFunctionality = function() {
  console.log("🔧 Restoring core YouTube functionality...");
  
  if (!window.pipMasterInstance) {
    console.error("❌ Extension instance not available");
    return false;
  }
  
  // Restore original methods if they were overridden
  if (window.pipMasterInstance._originalFindVideos) {
    window.pipMasterInstance.performUniversalVideoScan = window.pipMasterInstance._originalFindVideos;
    console.log("✅ Restored original video scan method");
  }
  
  if (window.pipMasterInstance._originalIsVideoSuitable) {
    window.pipMasterInstance.isVideoSuitableForPiP = window.pipMasterInstance._originalIsVideoSuitable;
    console.log("✅ Restored original suitability check");
  }
  
  if (window.pipMasterInstance._originalTogglePiP) {
    window.pipMasterInstance.togglePiP = window.pipMasterInstance._originalTogglePiP;
    console.log("✅ Restored original toggle PiP method");
  }
  
  // Force platform detection
  window.pipMasterInstance.platform = 'youtube';
  
  // Force video scan
  window.pipMasterInstance.performUniversalVideoScan();
  
  console.log("✅ Core functionality restored");
  return true;
};

window.cleanupExcessiveOverlays = function() {
  console.log("🧹 Cleaning up excessive overlays...");
  
  const overlays = document.querySelectorAll('.pip-master-overlay');
  let removed = 0;
  
  overlays.forEach(overlay => {
    // Check if overlay has a valid video
    const container = overlay.parentElement;
    const video = container ? container.querySelector('video') : null;
    
    if (!video || !document.contains(video)) {
      overlay.remove();
      removed++;
    }
  });
  
  console.log(`✅ Removed ${removed} orphaned overlays`);
  return removed;
};

window.resetEnhancedFeatures = function() {
  console.log("🔄 Resetting enhanced features...");
  
  // Disable potentially conflicting features
  if (window.pipMasterInstance?.smartAutoPiP) {
    window.pipMasterInstance.smartAutoPiP.disable();
  }
  
  if (window.pipMasterInstance?.performanceOptimizer) {
    window.pipMasterInstance.performanceOptimizer.scanThrottle = 2000; // Reset to normal
  }
  
  // Clear any overridden methods
  window.restoreCoreYouTubeFunctionality();
  
  console.log("✅ Enhanced features reset");
};

window.forceYouTubeDetection = function() {
  console.log("🎯 Forcing YouTube video detection...");
  
  // Direct YouTube video detection
  const youtubeSelectors = [
    "#movie_player video",
    ".html5-video-player video",
    ".video-stream",
    "video.video-stream",
    "video"
  ];
  
  let foundVideos = [];
  
  youtubeSelectors.forEach(selector => {
    try {
      const videos = document.querySelectorAll(selector);
      console.log(`${selector}: ${videos.length} videos`);
      videos.forEach(video => {
        if (!foundVideos.includes(video)) {
          foundVideos.push(video);
        }
      });
    } catch (error) {
      console.warn(`Selector ${selector} failed:`, error);
    }
  });
  
  console.log(`Found ${foundVideos.length} total videos`);
  
  // Process videos manually
  if (window.pipMasterInstance && foundVideos.length > 0) {
    foundVideos.forEach((video, index) => {
      try {
        if (!window.pipMasterInstance.videos.has(video)) {
          window.pipMasterInstance.videos.add(video);
          window.pipMasterInstance.createOverlay(video);
          console.log(`✅ Processed video ${index + 1}`);
        }
      } catch (error) {
        console.warn(`Failed to process video ${index + 1}:`, error);
      }
    });
  }
  
  return foundVideos.length;
};

// Run diagnostic automatically
window.youtubeRegression = new YouTubeRegressionDiagnostic();

console.log("\n📋 Available Diagnostic Commands:");
console.log("=================================");
console.log("restoreCoreYouTubeFunctionality() - Restore core functionality");
console.log("cleanupExcessiveOverlays()        - Remove orphaned overlays");
console.log("resetEnhancedFeatures()           - Reset enhanced features");
console.log("forceYouTubeDetection()           - Force video detection");
console.log("new YouTubeRegressionDiagnostic() - Run full diagnostic again");
