// Settings & YouTube Troubleshooting Script
// Comprehensive diagnosis and fix for missing settings and YouTube regression

console.log("🔧 Settings & YouTube Troubleshooting");
console.log("=====================================");

// Comprehensive Troubleshooter
class SettingsYouTubeTroubleshooter {
  constructor() {
    this.issues = [];
    this.fixes = [];
    this.runTroubleshooting();
  }

  runTroubleshooting() {
    console.log("🚀 Starting comprehensive troubleshooting...");
    
    this.checkExtensionState();
    this.diagnoseSettingsPanel();
    this.diagnoseYouTubeFunctionality();
    this.checkSettingsIntegration();
    this.testSettingsPersistence();
    this.generateTroubleshootingReport();
    this.provideFixCommands();
  }

  checkExtensionState() {
    console.log("\n1️⃣ Extension State Check");
    console.log("========================");
    
    const state = {
      pipMasterInstance: !!window.pipMasterInstance,
      settingsPanel: !!window.pipMasterInstance?.settingsPanel,
      enhancedFeatures: !!window.pipMasterEnhanced,
      timelineControl: !!window.pipMasterInstance?.timelineControl
    };
    
    Object.entries(state).forEach(([component, available]) => {
      console.log(`${available ? '✅' : '❌'} ${component}: ${available ? 'Available' : 'Missing'}`);
      if (!available) {
        this.issues.push(`${component} not available`);
      }
    });
    
    if (!state.pipMasterInstance) {
      this.fixes.push('reinitializeExtension');
    }
  }

  diagnoseSettingsPanel() {
    console.log("\n2️⃣ Settings Panel Diagnosis");
    console.log("============================");
    
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    
    if (!settingsPanel) {
      console.error("❌ Settings panel not available");
      this.issues.push('Settings panel missing');
      this.fixes.push('recreateSettingsPanel');
      return;
    }
    
    // Check if panel exists in DOM
    const panelElement = document.getElementById('pip-master-settings-panel');
    console.log(`Settings panel DOM: ${panelElement ? '✅ Found' : '❌ Missing'}`);
    
    if (!panelElement) {
      this.issues.push('Settings panel DOM missing');
      this.fixes.push('recreateSettingsPanelDOM');
    } else {
      // Check panel HTML content
      const panelHTML = panelElement.innerHTML;
      const hasAudioControl = panelHTML.includes('audio-control-enabled');
      const hasTimelinePreview = panelHTML.includes('timeline-preview-enabled');
      const hasPiPControls = panelHTML.includes('🎮 PiP Window Controls');
      const hasTimelineSection = panelHTML.includes('⏯️ Timeline Controls');
      
      console.log(`Audio control setting: ${hasAudioControl ? '✅ Found' : '❌ Missing'}`);
      console.log(`Timeline preview setting: ${hasTimelinePreview ? '✅ Found' : '❌ Missing'}`);
      console.log(`PiP Controls section: ${hasPiPControls ? '✅ Found' : '❌ Missing'}`);
      console.log(`Timeline Controls section: ${hasTimelineSection ? '✅ Found' : '❌ Missing'}`);
      
      if (!hasAudioControl || !hasTimelinePreview) {
        this.issues.push('New settings missing from panel HTML');
        this.fixes.push('updateSettingsPanelHTML');
      }
    }
    
    // Check settings object
    const settings = settingsPanel.settings;
    const hasAudioSetting = 'audioControlEnabled' in settings;
    const hasTimelineSetting = 'timelinePreviewEnabled' in settings;
    
    console.log(`Audio setting in object: ${hasAudioSetting ? '✅ Found' : '❌ Missing'}`);
    console.log(`Timeline setting in object: ${hasTimelineSetting ? '✅ Found' : '❌ Missing'}`);
    
    if (!hasAudioSetting || !hasTimelineSetting) {
      this.issues.push('New settings missing from settings object');
      this.fixes.push('updateSettingsObject');
    }
  }

  diagnoseYouTubeFunctionality() {
    console.log("\n3️⃣ YouTube Functionality Diagnosis");
    console.log("===================================");
    
    // Check if we're on YouTube
    const isYouTube = window.location.hostname.includes('youtube.com');
    console.log(`On YouTube: ${isYouTube ? '✅ Yes' : '❌ No'}`);
    
    if (!isYouTube) {
      console.log("⚠️ Not on YouTube - navigate to youtube.com to test");
      return;
    }
    
    // Check video detection
    const videos = document.querySelectorAll('video');
    const trackedVideos = window.pipMasterInstance?.videos?.size || 0;
    const overlays = document.querySelectorAll('.pip-master-overlay');
    
    console.log(`Videos in DOM: ${videos.length}`);
    console.log(`Videos tracked: ${trackedVideos}`);
    console.log(`Overlays created: ${overlays.length}`);
    
    if (videos.length > 0 && trackedVideos === 0) {
      this.issues.push('YouTube video detection broken');
      this.fixes.push('fixYouTubeDetection');
    }
    
    if (videos.length > 0 && overlays.length === 0) {
      this.issues.push('YouTube overlay creation broken');
      this.fixes.push('recreateOverlays');
    }
    
    // Test PiP functionality
    if (videos.length > 0) {
      const testVideo = videos[0];
      try {
        const suitable = window.pipMasterInstance?.isVideoSuitableForPiP?.(testVideo);
        console.log(`PiP suitability: ${suitable ? '✅ Suitable' : '❌ Not suitable'}`);
        
        if (!suitable) {
          this.issues.push('YouTube PiP suitability check failing');
          this.fixes.push('fixPiPSuitability');
        }
      } catch (error) {
        console.error("PiP suitability test failed:", error);
        this.issues.push('PiP suitability method broken');
        this.fixes.push('fixPiPSuitability');
      }
    }
  }

  checkSettingsIntegration() {
    console.log("\n4️⃣ Settings Integration Check");
    console.log("==============================");
    
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    
    if (!settingsPanel) {
      console.log("❌ Cannot check integration - settings panel missing");
      return;
    }
    
    // Check if new methods exist
    const methods = {
      applyAudioControlSettings: typeof settingsPanel.applyAudioControlSettings === 'function',
      setupAudioControlListeners: typeof settingsPanel.setupAudioControlListeners === 'function',
      applyTimelinePreviewSettings: typeof settingsPanel.applyTimelinePreviewSettings === 'function'
    };
    
    Object.entries(methods).forEach(([method, exists]) => {
      console.log(`${exists ? '✅' : '❌'} ${method}: ${exists ? 'Available' : 'Missing'}`);
      if (!exists) {
        this.issues.push(`Method ${method} missing`);
        this.fixes.push('addMissingMethods');
      }
    });
    
    // Check timeline control integration
    const timelineControl = window.pipMasterInstance?.timelineControl;
    if (timelineControl) {
      const timelineMethods = {
        enableHoverPreview: typeof timelineControl.enableHoverPreview === 'function',
        disableHoverPreview: typeof timelineControl.disableHoverPreview === 'function'
      };
      
      Object.entries(timelineMethods).forEach(([method, exists]) => {
        console.log(`${exists ? '✅' : '❌'} Timeline ${method}: ${exists ? 'Available' : 'Missing'}`);
        if (!exists) {
          this.issues.push(`Timeline method ${method} missing`);
          this.fixes.push('addTimelineMethods');
        }
      });
    } else {
      console.log("❌ Timeline control not available");
      this.issues.push('Timeline control missing');
      this.fixes.push('initializeTimelineControl');
    }
  }

  testSettingsPersistence() {
    console.log("\n5️⃣ Settings Persistence Test");
    console.log("=============================");
    
    try {
      // Test localStorage access
      localStorage.setItem('pipMaster_test', 'test');
      localStorage.removeItem('pipMaster_test');
      console.log("✅ localStorage access: Working");
      
      // Check existing settings
      const stored = localStorage.getItem('pipMaster_enhancedSettings');
      if (stored) {
        const parsed = JSON.parse(stored);
        const hasAudio = 'audioControlEnabled' in parsed;
        const hasTimeline = 'timelinePreviewEnabled' in parsed;
        
        console.log(`Stored audio setting: ${hasAudio ? '✅ Found' : '❌ Missing'}`);
        console.log(`Stored timeline setting: ${hasTimeline ? '✅ Found' : '❌ Missing'}`);
        
        if (!hasAudio || !hasTimeline) {
          this.issues.push('New settings not in localStorage');
          this.fixes.push('updateStoredSettings');
        }
      } else {
        console.log("⚠️ No stored settings found");
        this.fixes.push('initializeStoredSettings');
      }
      
    } catch (error) {
      console.error("❌ localStorage test failed:", error);
      this.issues.push('localStorage access broken');
      this.fixes.push('fixLocalStorage');
    }
  }

  generateTroubleshootingReport() {
    console.log("\n📊 TROUBLESHOOTING REPORT");
    console.log("=========================");
    
    console.log(`🔍 Issues found: ${this.issues.length}`);
    console.log(`🔧 Fixes needed: ${this.fixes.length}`);
    
    if (this.issues.length > 0) {
      console.log("\n❌ Issues identified:");
      this.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
    
    if (this.fixes.length > 0) {
      console.log("\n🔧 Fixes to apply:");
      this.fixes.forEach((fix, index) => {
        console.log(`${index + 1}. ${fix}`);
      });
    }
    
    if (this.issues.length === 0) {
      console.log("🎉 No issues found - extension should be working correctly!");
    }
  }

  provideFixCommands() {
    console.log("\n💡 SPECIFIC FIX COMMANDS");
    console.log("========================");
    
    console.log("// 1. Force reload settings panel with new elements");
    console.log("forceReloadSettingsPanel()");
    
    console.log("\n// 2. Restore YouTube functionality");
    console.log("restoreYouTubeFunctionality()");
    
    console.log("\n// 3. Manually test new audio and timeline settings");
    console.log("testNewSettingsManually()");
    
    console.log("\n// 4. Verify all components working together");
    console.log("verifyCompleteIntegration()");
    
    console.log("\n// 5. Emergency full reset and reinitialize");
    console.log("emergencyFullReset()");
  }
}

// Fix command implementations
window.forceReloadSettingsPanel = function() {
  console.log("🔄 Force Reloading Settings Panel");
  console.log("=================================");
  
  try {
    // Remove existing panel
    const existingPanel = document.getElementById('pip-master-settings-panel');
    if (existingPanel) {
      existingPanel.remove();
      console.log("🗑️ Removed existing settings panel");
    }
    
    // Reinitialize settings panel
    if (window.pipMasterInstance) {
      // Add new settings to existing settings object
      if (!('audioControlEnabled' in window.pipMasterInstance.settingsPanel.settings)) {
        window.pipMasterInstance.settingsPanel.settings.audioControlEnabled = true;
        console.log("➕ Added audioControlEnabled setting");
      }
      
      if (!('timelinePreviewEnabled' in window.pipMasterInstance.settingsPanel.settings)) {
        window.pipMasterInstance.settingsPanel.settings.timelinePreviewEnabled = true;
        console.log("➕ Added timelinePreviewEnabled setting");
      }
      
      // Recreate panel with new settings
      window.pipMasterInstance.settingsPanel.createSettingsPanel();
      console.log("✅ Settings panel recreated with new elements");
      
      // Test panel opening
      window.pipMasterInstance.settingsPanel.showPanel();
      console.log("✅ Settings panel opened for verification");
      
      // Check for new elements
      const audioElement = document.getElementById('audio-control-enabled');
      const timelineElement = document.getElementById('timeline-preview-enabled');
      
      console.log(`Audio control element: ${audioElement ? '✅ Found' : '❌ Missing'}`);
      console.log(`Timeline preview element: ${timelineElement ? '✅ Found' : '❌ Missing'}`);
      
      window.pipMasterInstance.settingsPanel.hidePanel();
      
    } else {
      console.error("❌ PiP Master instance not available");
    }
    
  } catch (error) {
    console.error("❌ Failed to reload settings panel:", error);
  }
};

window.restoreYouTubeFunctionality = function() {
  console.log("🎯 Restoring YouTube Functionality");
  console.log("==================================");
  
  try {
    if (!window.pipMasterInstance) {
      console.error("❌ PiP Master instance not available");
      return;
    }
    
    // Force platform detection
    window.pipMasterInstance.platform = 'youtube';
    console.log("🌐 Platform set to YouTube");
    
    // Clear existing video tracking
    if (window.pipMasterInstance.videos) {
      window.pipMasterInstance.videos.clear();
    }
    
    // Remove existing overlays
    document.querySelectorAll('.pip-master-overlay').forEach(overlay => overlay.remove());
    console.log("🧹 Cleared existing overlays");
    
    // Force video scan
    window.pipMasterInstance.performUniversalVideoScan();
    console.log("🔍 Performed universal video scan");
    
    // Check results
    const videos = document.querySelectorAll('video');
    const trackedVideos = window.pipMasterInstance.videos ? window.pipMasterInstance.videos.size : 0;
    const overlays = document.querySelectorAll('.pip-master-overlay');
    
    console.log(`📹 Videos found: ${videos.length}`);
    console.log(`📹 Videos tracked: ${trackedVideos}`);
    console.log(`🎨 Overlays created: ${overlays.length}`);
    
    if (videos.length > 0 && trackedVideos > 0 && overlays.length > 0) {
      console.log("🎉 YouTube functionality restored!");
    } else {
      console.log("⚠️ YouTube functionality may still have issues");
    }
    
  } catch (error) {
    console.error("❌ Failed to restore YouTube functionality:", error);
  }
};

window.testNewSettingsManually = function() {
  console.log("🧪 Testing New Settings Manually");
  console.log("================================");
  
  try {
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    
    if (!settingsPanel) {
      console.error("❌ Settings panel not available");
      return;
    }
    
    // Test audio control setting
    console.log("\n🔊 Testing Audio Control Setting:");
    settingsPanel.settings.audioControlEnabled = false;
    if (settingsPanel.applyAudioControlSettings) {
      settingsPanel.applyAudioControlSettings();
      console.log("✅ Audio control setting applied (disabled)");
    } else {
      console.log("❌ applyAudioControlSettings method missing");
    }
    
    settingsPanel.settings.audioControlEnabled = true;
    if (settingsPanel.applyAudioControlSettings) {
      settingsPanel.applyAudioControlSettings();
      console.log("✅ Audio control setting applied (enabled)");
    }
    
    // Test timeline preview setting
    console.log("\n⏯️ Testing Timeline Preview Setting:");
    settingsPanel.settings.timelinePreviewEnabled = false;
    if (settingsPanel.applyTimelinePreviewSettings) {
      settingsPanel.applyTimelinePreviewSettings();
      console.log("✅ Timeline preview setting applied (disabled)");
    } else {
      console.log("❌ applyTimelinePreviewSettings method missing");
    }
    
    settingsPanel.settings.timelinePreviewEnabled = true;
    if (settingsPanel.applyTimelinePreviewSettings) {
      settingsPanel.applyTimelinePreviewSettings();
      console.log("✅ Timeline preview setting applied (enabled)");
    }
    
    // Test settings persistence
    console.log("\n💾 Testing Settings Persistence:");
    settingsPanel.saveSettings();
    console.log("✅ Settings saved");
    
    const loaded = settingsPanel.loadSettings();
    console.log(`✅ Settings loaded: audioControlEnabled=${loaded.audioControlEnabled}, timelinePreviewEnabled=${loaded.timelinePreviewEnabled}`);
    
  } catch (error) {
    console.error("❌ Manual testing failed:", error);
  }
};

window.verifyCompleteIntegration = function() {
  console.log("🔍 Verifying Complete Integration");
  console.log("=================================");
  
  const checks = {
    extensionLoaded: !!window.pipMasterInstance,
    settingsPanel: !!window.pipMasterInstance?.settingsPanel,
    audioSetting: false,
    timelineSetting: false,
    audioMethods: false,
    timelineMethods: false,
    youtubeWorking: false,
    settingsPersistence: false
  };
  
  // Check settings
  if (window.pipMasterInstance?.settingsPanel) {
    const settings = window.pipMasterInstance.settingsPanel.settings;
    checks.audioSetting = 'audioControlEnabled' in settings;
    checks.timelineSetting = 'timelinePreviewEnabled' in settings;
    
    checks.audioMethods = typeof window.pipMasterInstance.settingsPanel.applyAudioControlSettings === 'function';
    checks.timelineMethods = typeof window.pipMasterInstance.settingsPanel.applyTimelinePreviewSettings === 'function';
  }
  
  // Check YouTube
  const videos = document.querySelectorAll('video');
  const trackedVideos = window.pipMasterInstance?.videos?.size || 0;
  checks.youtubeWorking = videos.length > 0 && trackedVideos > 0;
  
  // Check persistence
  try {
    const stored = localStorage.getItem('pipMaster_enhancedSettings');
    checks.settingsPersistence = !!stored;
  } catch (error) {
    checks.settingsPersistence = false;
  }
  
  console.log("Integration check results:");
  Object.entries(checks).forEach(([check, result]) => {
    console.log(`${result ? '✅' : '❌'} ${check}: ${result}`);
  });
  
  const workingCount = Object.values(checks).filter(Boolean).length;
  const percentage = Math.round((workingCount / Object.keys(checks).length) * 100);
  
  console.log(`\n📊 Integration Score: ${workingCount}/${Object.keys(checks).length} (${percentage}%)`);
  
  if (percentage >= 80) {
    console.log("🎉 Integration is working well!");
  } else {
    console.log("⚠️ Integration needs attention");
  }
  
  return { percentage, checks };
};

window.emergencyFullReset = function() {
  console.log("🚨 Emergency Full Reset");
  console.log("======================");
  
  try {
    // Clear all overlays
    document.querySelectorAll('.pip-master-overlay').forEach(overlay => overlay.remove());
    console.log("🧹 Cleared all overlays");
    
    // Clear settings panel
    const panel = document.getElementById('pip-master-settings-panel');
    if (panel) panel.remove();
    console.log("🧹 Removed settings panel");
    
    // Reset localStorage
    localStorage.removeItem('pipMaster_enhancedSettings');
    localStorage.removeItem('pipMaster_sitePreferences');
    console.log("🧹 Cleared localStorage");
    
    // Reinitialize if possible
    if (window.initializeSettingsPanel) {
      window.initializeSettingsPanel();
      console.log("🔄 Reinitialized settings panel");
    }
    
    if (window.pipMasterInstance) {
      window.pipMasterInstance.performUniversalVideoScan();
      console.log("🔄 Performed video scan");
    }
    
    console.log("✅ Emergency reset complete - try using the extension now");
    
  } catch (error) {
    console.error("❌ Emergency reset failed:", error);
  }
};

// Auto-run troubleshooting
console.log("🚀 Auto-running troubleshooting...");
window.settingsYouTubeTroubleshooter = new SettingsYouTubeTroubleshooter();

console.log("\n📋 Available Fix Commands:");
console.log("==========================");
console.log("forceReloadSettingsPanel()    - Reload settings with new elements");
console.log("restoreYouTubeFunctionality() - Fix YouTube video detection");
console.log("testNewSettingsManually()     - Test audio and timeline settings");
console.log("verifyCompleteIntegration()   - Check all components");
console.log("emergencyFullReset()          - Nuclear option - reset everything");
