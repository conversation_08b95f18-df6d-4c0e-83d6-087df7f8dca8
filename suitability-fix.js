// Video Suitability Fix Script
// Fixes the specific issue causing YouTube videos to be rejected

console.log("🔧 Video Suitability Fix Script");
console.log("===============================");

// Function to apply the fix to the extension
window.applySuitabilityFix = function() {
  console.log("🔧 Applying suitability fix...");
  
  if (!window.pipMasterInstance) {
    console.error("❌ Extension instance not available!");
    return false;
  }
  
  // Backup original methods
  window.pipMasterInstance._originalIsVideoSuitableForPiP = window.pipMasterInstance.isVideoSuitableForPiP;
  window.pipMasterInstance._originalPlatformSpecificVideoCheck = window.pipMasterInstance.platformSpecificVideoCheck;
  
  // Fixed isVideoSuitableForPiP method
  window.pipMasterInstance.isVideoSuitableForPiP = function(video) {
    try {
      const videoId = video.src || video.currentSrc || "unknown";
      console.log("PiP Master: [FIXED] Checking video suitability for:", videoId);

      // Check if video element is valid
      if (!video || video.tagName !== "VIDEO") {
        console.log("PiP Master: [FIXED] Invalid video element");
        return false;
      }

      // Check if PiP is explicitly disabled
      if (video.disablePictureInPicture) {
        console.log("PiP Master: [FIXED] PiP explicitly disabled on video");
        return false;
      }

      // Check if video is visible (basic check)
      const style = window.getComputedStyle(video);
      if (style.display === "none") {
        console.log("PiP Master: [FIXED] Video is display:none");
        return false;
      }

      // More lenient visibility check
      if (style.visibility === "hidden" && style.opacity === "0") {
        console.log("PiP Master: [FIXED] Video is completely hidden");
        return false;
      }

      // Very basic size check (more lenient)
      const rect = video.getBoundingClientRect();
      if (rect.width < 5 || rect.height < 5) {
        console.log(`PiP Master: [FIXED] Video too small: ${rect.width}x${rect.height}`);
        return false;
      }

      // More lenient dimension checks
      if (video.readyState >= 1) {
        // Allow videos with no dimensions initially (YouTube often loads dimensions later)
        if (video.videoWidth === 0 && video.videoHeight === 0) {
          console.log("PiP Master: [FIXED] Video has no dimensions yet, but allowing (YouTube case)");
          // Don't return false immediately - allow it
        }

        // Very lenient dimension check
        if (video.videoWidth > 0 && video.videoHeight > 0) {
          if (video.videoWidth < 10 || video.videoHeight < 10) {
            console.log(`PiP Master: [FIXED] Video dimensions too small: ${video.videoWidth}x${video.videoHeight}`);
            return false;
          }
        }
      } else {
        console.log("PiP Master: [FIXED] Video metadata not loaded yet, allowing");
      }

      // Platform-specific checks (fixed)
      if (!this.platformSpecificVideoCheck(video)) {
        console.log("PiP Master: [FIXED] Failed platform-specific check");
        return false;
      }

      // Very lenient duration check
      if (video.duration && video.duration < 0.05) {
        console.log("PiP Master: [FIXED] Video too short:", video.duration);
        return false;
      }

      // More lenient ad check
      if (this.isLikelyVideoAd(video)) {
        console.log("PiP Master: [FIXED] Video appears to be an advertisement");
        return false;
      }

      console.log("PiP Master: [FIXED] Video passed suitability check:", {
        platform: this.platform,
        dimensions: `${video.videoWidth || "unknown"}x${video.videoHeight || "unknown"}`,
        rendered: `${Math.round(rect.width)}x${Math.round(rect.height)}`,
        duration: video.duration || "unknown",
        readyState: video.readyState,
        src: videoId.substring(0, 50) + (videoId.length > 50 ? "..." : ""),
      });

      return true;
    } catch (error) {
      console.error("PiP Master: [FIXED] Error checking video suitability:", error);
      // Be more permissive on errors - allow the video
      console.log("PiP Master: [FIXED] Allowing video despite suitability check error");
      return true;
    }
  };
  
  // Fixed platformSpecificVideoCheck method (less aggressive ad detection)
  window.pipMasterInstance.platformSpecificVideoCheck = function(video) {
    switch (this.platform) {
      case "youtube":
        console.log("PiP Master: [FIXED] YouTube platform check");
        
        // Only check for very obvious ad indicators
        const obviousAdSelectors = [
          ".ad-showing",
          ".video-ads"
        ];

        for (const selector of obviousAdSelectors) {
          const adContainer = video.closest(selector);
          if (adContainer) {
            console.log(`PiP Master: [FIXED] Skipping obvious YouTube ad (${selector})`);
            return false;
          }
        }

        // Check for YouTube Premium ad indicators (most reliable)
        const ytPlayer = document.querySelector("#movie_player");
        if (ytPlayer && ytPlayer.classList.contains("ad-showing")) {
          console.log("PiP Master: [FIXED] Skipping YouTube ad (player ad-showing)");
          return false;
        }

        // Skip the aggressive container checks that might be causing false positives
        console.log("PiP Master: [FIXED] YouTube video passed platform check");
        break;

      case "twitch":
        // Skip Twitch ads
        const twitchAdContainer = video.closest('[data-a-target="video-ad"]');
        if (twitchAdContainer) {
          console.log("PiP Master: [FIXED] Skipping Twitch ad video");
          return false;
        }
        break;

      case "netflix":
        // Netflix might have DRM restrictions
        if (video.src && video.src.includes("blob:")) {
          console.log("PiP Master: [FIXED] Netflix blob video detected (may have DRM)");
        }
        break;
    }
    return true;
  };
  
  // Fixed isLikelyVideoAd method (less aggressive)
  window.pipMasterInstance.isLikelyVideoAd = function(video) {
    // Only check for very obvious ad indicators
    const obviousAdIndicators = [
      "advertisement",
      "preroll",
      "midroll",
      "postroll"
    ];

    // Check video src (only obvious indicators)
    const src = (video.src || video.currentSrc || "").toLowerCase();
    if (obviousAdIndicators.some((indicator) => src.includes(indicator))) {
      console.log("PiP Master: [FIXED] Video detected as ad by src");
      return true;
    }

    // Check only immediate parent for obvious ad indicators
    const parent = video.parentElement;
    if (parent) {
      const className = (parent.className || "").toLowerCase();
      const id = (parent.id || "").toLowerCase();

      if (obviousAdIndicators.some((indicator) => className.includes(indicator) || id.includes(indicator))) {
        console.log("PiP Master: [FIXED] Video detected as ad by parent container");
        return true;
      }
    }

    console.log("PiP Master: [FIXED] Video passed ad detection");
    return false;
  };
  
  console.log("✅ Suitability fix applied successfully!");
  console.log("🔄 Re-scanning videos with fixed logic...");
  
  // Re-scan videos with fixed logic
  setTimeout(() => {
    window.pipMasterInstance.performUniversalVideoScan();
  }, 500);
  
  return true;
};

// Function to test the fix
window.testSuitabilityFix = function() {
  console.log("🧪 Testing suitability fix...");
  
  const videos = document.querySelectorAll('video');
  if (videos.length === 0) {
    console.error("❌ No videos found to test");
    return;
  }
  
  console.log(`Testing ${videos.length} videos with fixed logic...`);
  
  videos.forEach((video, index) => {
    console.log(`\n📹 Testing video ${index + 1}:`);
    
    if (window.pipMasterInstance) {
      const result = window.pipMasterInstance.isVideoSuitableForPiP(video);
      console.log(`Result: ${result ? '✅ PASSED' : '❌ FAILED'}`);
      
      if (result) {
        console.log("🎉 This video should now work with PiP!");
      }
    }
  });
};

// Function to restore original methods
window.restoreOriginalSuitability = function() {
  console.log("🔄 Restoring original suitability methods...");
  
  if (!window.pipMasterInstance) {
    console.error("❌ Extension instance not available!");
    return false;
  }
  
  if (window.pipMasterInstance._originalIsVideoSuitableForPiP) {
    window.pipMasterInstance.isVideoSuitableForPiP = window.pipMasterInstance._originalIsVideoSuitableForPiP;
  }
  
  if (window.pipMasterInstance._originalPlatformSpecificVideoCheck) {
    window.pipMasterInstance.platformSpecificVideoCheck = window.pipMasterInstance._originalPlatformSpecificVideoCheck;
  }
  
  console.log("✅ Original methods restored");
  return true;
};

// Function to force video processing with fixed logic
window.forceVideoProcessingWithFix = function() {
  console.log("🔧 Force processing videos with fix...");
  
  if (!window.pipMasterInstance) {
    console.error("❌ Extension instance not available!");
    return;
  }
  
  const videos = document.querySelectorAll('video');
  let processed = 0;
  
  videos.forEach((video, index) => {
    console.log(`Processing video ${index + 1}...`);
    
    try {
      // Force add to tracking
      if (!window.pipMasterInstance.videos.has(video)) {
        window.pipMasterInstance.videos.add(video);
        console.log(`  ✅ Added to tracking`);
      }
      
      // Force create overlay
      if (!window.pipMasterInstance.overlays.has(video)) {
        window.pipMasterInstance.createOverlay(video);
        console.log(`  ✅ Overlay created`);
        processed++;
      }
    } catch (error) {
      console.error(`  ❌ Error processing video ${index + 1}:`, error);
    }
  });
  
  console.log(`✅ Processed ${processed} videos`);
  return processed;
};

// Auto-apply fix on YouTube
if (window.location.hostname.includes('youtube.com')) {
  console.log("🎬 Auto-applying YouTube suitability fix...");
  setTimeout(() => {
    window.applySuitabilityFix();
    setTimeout(() => {
      window.testSuitabilityFix();
    }, 1000);
  }, 1000);
}

// Display available commands
console.log("\n📋 Available Fix Commands:");
console.log("==========================");
console.log("applySuitabilityFix()           - Apply the suitability fix");
console.log("testSuitabilityFix()            - Test videos with fixed logic");
console.log("forceVideoProcessingWithFix()   - Force process all videos");
console.log("restoreOriginalSuitability()    - Restore original methods");
console.log("");
console.log("🚀 Quick start: applySuitabilityFix() (auto-runs on YouTube)");
console.log("🧪 To test: testSuitabilityFix()");
console.log("🔧 Force fix: forceVideoProcessingWithFix()");
