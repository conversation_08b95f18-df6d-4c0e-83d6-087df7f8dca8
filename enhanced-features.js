// Enhanced Features for PiP Master
// Advanced functionality while maintaining Google-style reliability

console.log("🚀 PiP Master Enhanced Features");
console.log("===============================");

// Enhancement 1: Smart Auto-PiP on Tab Switch
class SmartAutoPiP {
  constructor(pipMaster) {
    this.pipMaster = pipMaster;
    this.isEnabled = false;
    this.activeVideo = null;
    this.wasPlayingBeforeSwitch = false;
    this.setupTabSwitchDetection();
  }

  enable() {
    this.isEnabled = true;
    console.log("✅ Smart Auto-PiP enabled");
  }

  disable() {
    this.isEnabled = false;
    console.log("❌ Smart Auto-PiP disabled");
  }

  setupTabSwitchDetection() {
    // Detect when user switches away from tab
    document.addEventListener('visibilitychange', () => {
      if (this.isEnabled) {
        if (document.hidden) {
          this.handleTabHidden();
        } else {
          this.handleTabVisible();
        }
      }
    });

    // Detect when user switches back to tab with PiP active
    document.addEventListener('enterpictureinpicture', (event) => {
      this.activeVideo = event.target;
    });

    document.addEventListener('leavepictureinpicture', (event) => {
      this.activeVideo = null;
    });
  }

  async handleTabHidden() {
    console.log("🔄 Tab hidden - checking for Auto-PiP opportunity");
    
    // Find playing video
    const playingVideo = this.findPlayingVideo();
    if (!playingVideo) {
      console.log("No playing video found for Auto-PiP");
      return;
    }

    // Check if video is suitable and not already in PiP
    if (this.pipMaster.isVideoSuitableForPiP(playingVideo) && !document.pictureInPictureElement) {
      try {
        console.log("🎬 Activating Auto-PiP for playing video");
        await this.pipMaster.togglePiP(playingVideo);
        this.activeVideo = playingVideo;
        this.wasPlayingBeforeSwitch = !playingVideo.paused;
        
        // Show notification
        this.pipMaster.showSuccess("Auto Picture-in-Picture activated!");
      } catch (error) {
        console.log("Auto-PiP failed:", error.message);
      }
    }
  }

  handleTabVisible() {
    console.log("👁️ Tab visible - checking Auto-PiP state");
    
    // Option to auto-exit PiP when returning to tab
    if (this.activeVideo && document.pictureInPictureElement) {
      // User preference: auto-exit or keep PiP
      const autoExit = this.pipMaster.settings.autoExitOnTabReturn !== false;
      if (autoExit) {
        setTimeout(() => {
          if (document.pictureInPictureElement) {
            document.exitPictureInPicture();
            console.log("🔄 Auto-exited PiP on tab return");
          }
        }, 1000); // Small delay to avoid jarring experience
      }
    }
  }

  findPlayingVideo() {
    const videos = Array.from(document.querySelectorAll('video'));
    return videos.find(video => 
      !video.paused && 
      !video.ended && 
      video.readyState >= 2 && // HAVE_CURRENT_DATA
      !video.disablePictureInPicture
    );
  }
}

// Enhancement 2: Advanced PiP Window Controls
class AdvancedPiPControls {
  constructor(pipMaster) {
    this.pipMaster = pipMaster;
    this.pipWindow = null;
    this.controls = null;
    this.setupPiPEventListeners();
  }

  setupPiPEventListeners() {
    document.addEventListener('enterpictureinpicture', (event) => {
      this.pipWindow = event.target;
      this.addAdvancedControls();
    });

    document.addEventListener('leavepictureinpicture', () => {
      this.pipWindow = null;
      this.controls = null;
    });
  }

  addAdvancedControls() {
    if (!this.pipWindow || !('pictureInPictureWindow' in this.pipWindow)) {
      return;
    }

    try {
      // Add custom controls to PiP window
      const pipWindowObj = this.pipWindow.pictureInPictureWindow;
      
      // Volume control
      this.addVolumeControl(pipWindowObj);
      
      // Playback speed control
      this.addPlaybackSpeedControl(pipWindowObj);
      
      // Skip controls (for supported platforms)
      this.addSkipControls(pipWindowObj);
      
      console.log("✅ Advanced PiP controls added");
    } catch (error) {
      console.log("Advanced controls not supported:", error.message);
    }
  }

  addVolumeControl(pipWindow) {
    // Volume up/down with keyboard shortcuts
    document.addEventListener('keydown', (event) => {
      if (document.pictureInPictureElement && event.target === document.body) {
        if (event.key === 'ArrowUp' && event.ctrlKey) {
          event.preventDefault();
          this.adjustVolume(0.1);
        } else if (event.key === 'ArrowDown' && event.ctrlKey) {
          event.preventDefault();
          this.adjustVolume(-0.1);
        }
      }
    });
  }

  adjustVolume(delta) {
    if (this.pipWindow) {
      const newVolume = Math.max(0, Math.min(1, this.pipWindow.volume + delta));
      this.pipWindow.volume = newVolume;
      
      // Show volume feedback
      this.showVolumeIndicator(Math.round(newVolume * 100));
    }
  }

  showVolumeIndicator(volumePercent) {
    // Create temporary volume indicator
    const indicator = document.createElement('div');
    indicator.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px 15px;
      border-radius: 5px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 10001;
      pointer-events: none;
    `;
    indicator.textContent = `Volume: ${volumePercent}%`;
    
    document.body.appendChild(indicator);
    
    setTimeout(() => {
      if (indicator.parentElement) {
        indicator.parentElement.removeChild(indicator);
      }
    }, 2000);
  }

  addPlaybackSpeedControl(pipWindow) {
    // Playback speed control with keyboard shortcuts
    document.addEventListener('keydown', (event) => {
      if (document.pictureInPictureElement && event.target === document.body) {
        if (event.key === '>' && event.shiftKey) {
          event.preventDefault();
          this.adjustPlaybackSpeed(0.25);
        } else if (event.key === '<' && event.shiftKey) {
          event.preventDefault();
          this.adjustPlaybackSpeed(-0.25);
        } else if (event.key === '=' && event.ctrlKey) {
          event.preventDefault();
          this.resetPlaybackSpeed();
        }
      }
    });
  }

  adjustPlaybackSpeed(delta) {
    if (this.pipWindow) {
      const newSpeed = Math.max(0.25, Math.min(4, this.pipWindow.playbackRate + delta));
      this.pipWindow.playbackRate = newSpeed;
      
      // Show speed feedback
      this.showSpeedIndicator(newSpeed);
    }
  }

  resetPlaybackSpeed() {
    if (this.pipWindow) {
      this.pipWindow.playbackRate = 1;
      this.showSpeedIndicator(1);
    }
  }

  showSpeedIndicator(speed) {
    const indicator = document.createElement('div');
    indicator.style.cssText = `
      position: fixed;
      top: 60px;
      right: 20px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px 15px;
      border-radius: 5px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 10001;
      pointer-events: none;
    `;
    indicator.textContent = `Speed: ${speed}x`;
    
    document.body.appendChild(indicator);
    
    setTimeout(() => {
      if (indicator.parentElement) {
        indicator.parentElement.removeChild(indicator);
      }
    }, 2000);
  }

  addSkipControls(pipWindow) {
    // Skip forward/backward controls
    document.addEventListener('keydown', (event) => {
      if (document.pictureInPictureElement && event.target === document.body) {
        if (event.key === 'ArrowRight' && event.ctrlKey) {
          event.preventDefault();
          this.skipForward(10); // 10 seconds
        } else if (event.key === 'ArrowLeft' && event.ctrlKey) {
          event.preventDefault();
          this.skipBackward(10); // 10 seconds
        }
      }
    });
  }

  skipForward(seconds) {
    if (this.pipWindow) {
      this.pipWindow.currentTime = Math.min(
        this.pipWindow.duration || this.pipWindow.currentTime + seconds,
        this.pipWindow.currentTime + seconds
      );
      this.showSkipIndicator(`+${seconds}s`);
    }
  }

  skipBackward(seconds) {
    if (this.pipWindow) {
      this.pipWindow.currentTime = Math.max(0, this.pipWindow.currentTime - seconds);
      this.showSkipIndicator(`-${seconds}s`);
    }
  }

  showSkipIndicator(text) {
    const indicator = document.createElement('div');
    indicator.style.cssText = `
      position: fixed;
      top: 100px;
      right: 20px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px 15px;
      border-radius: 5px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 10001;
      pointer-events: none;
    `;
    indicator.textContent = `Skip: ${text}`;
    
    document.body.appendChild(indicator);
    
    setTimeout(() => {
      if (indicator.parentElement) {
        indicator.parentElement.removeChild(indicator);
      }
    }, 1500);
  }
}

// Enhancement 3: Customizable Overlay Themes
class OverlayThemeManager {
  constructor(pipMaster) {
    this.pipMaster = pipMaster;
    this.currentTheme = 'default';
    this.themes = this.defineThemes();
  }

  defineThemes() {
    return {
      default: {
        name: 'Default',
        background: 'rgba(0, 0, 0, 0.7)',
        color: '#ffffff',
        borderRadius: '8px',
        iconColor: '#ffffff'
      },
      minimal: {
        name: 'Minimal',
        background: 'rgba(255, 255, 255, 0.9)',
        color: '#333333',
        borderRadius: '50%',
        iconColor: '#333333'
      },
      neon: {
        name: 'Neon',
        background: 'rgba(0, 255, 255, 0.2)',
        color: '#00ffff',
        borderRadius: '4px',
        iconColor: '#00ffff',
        boxShadow: '0 0 10px rgba(0, 255, 255, 0.5)'
      },
      dark: {
        name: 'Dark Pro',
        background: 'rgba(20, 20, 20, 0.9)',
        color: '#ffffff',
        borderRadius: '6px',
        iconColor: '#ffffff',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      },
      youtube: {
        name: 'YouTube Style',
        background: 'rgba(255, 0, 0, 0.8)',
        color: '#ffffff',
        borderRadius: '4px',
        iconColor: '#ffffff'
      }
    };
  }

  setTheme(themeName) {
    if (!this.themes[themeName]) {
      console.warn(`Theme "${themeName}" not found`);
      return;
    }

    this.currentTheme = themeName;
    this.applyThemeToAllOverlays();
    
    // Save preference
    if (this.pipMaster.settings) {
      this.pipMaster.settings.overlayTheme = themeName;
      this.pipMaster.saveSettings();
    }
    
    console.log(`✅ Applied theme: ${this.themes[themeName].name}`);
  }

  applyThemeToAllOverlays() {
    const overlays = document.querySelectorAll('.pip-master-overlay');
    overlays.forEach(overlay => this.applyThemeToOverlay(overlay));
  }

  applyThemeToOverlay(overlay) {
    const theme = this.themes[this.currentTheme];
    const container = overlay.querySelector('.pip-master-container');
    const indicator = overlay.querySelector('.pip-master-indicator');
    
    if (container) {
      container.style.background = theme.background;
      container.style.color = theme.color;
      container.style.borderRadius = theme.borderRadius;
      
      if (theme.boxShadow) {
        container.style.boxShadow = theme.boxShadow;
      }
      
      if (theme.border) {
        container.style.border = theme.border;
      }
    }
    
    if (indicator) {
      indicator.style.color = theme.iconColor;
    }
  }

  getAvailableThemes() {
    return Object.keys(this.themes).map(key => ({
      id: key,
      name: this.themes[key].name
    }));
  }
}

// Initialize enhanced features
window.initializeEnhancedFeatures = function() {
  console.log("🚀 Initializing Enhanced Features");
  
  if (!window.pipMasterInstance) {
    console.error("❌ PiP Master instance not available");
    return false;
  }
  
  // Initialize Smart Auto-PiP
  window.pipMasterInstance.smartAutoPiP = new SmartAutoPiP(window.pipMasterInstance);
  
  // Initialize Advanced Controls
  window.pipMasterInstance.advancedControls = new AdvancedPiPControls(window.pipMasterInstance);
  
  // Initialize Theme Manager
  window.pipMasterInstance.themeManager = new OverlayThemeManager(window.pipMasterInstance);
  
  console.log("✅ Enhanced features initialized");
  
  // Show available features
  console.log("\n🎯 Available Enhanced Features:");
  console.log("==============================");
  console.log("Smart Auto-PiP: pipMasterInstance.smartAutoPiP.enable()");
  console.log("Theme Manager: pipMasterInstance.themeManager.setTheme('neon')");
  console.log("Advanced Controls: Automatic when PiP is active");
  console.log("");
  console.log("🎮 PiP Window Controls (when PiP is active):");
  console.log("Ctrl + ↑/↓: Volume control");
  console.log("Shift + </> : Playback speed");
  console.log("Ctrl + ←/→: Skip 10 seconds");
  console.log("Ctrl + =: Reset speed to 1x");
  
  return true;
};

// Auto-initialize on load
if (window.pipMasterInstance) {
  window.initializeEnhancedFeatures();
} else {
  // Wait for PiP Master to load
  setTimeout(() => {
    if (window.pipMasterInstance) {
      window.initializeEnhancedFeatures();
    }
  }, 2000);
}

console.log("\n📋 Enhanced Features Commands:");
console.log("==============================");
console.log("initializeEnhancedFeatures()                    - Initialize all features");
console.log("pipMasterInstance.smartAutoPiP.enable()         - Enable Auto-PiP");
console.log("pipMasterInstance.themeManager.setTheme('neon') - Change overlay theme");
console.log("pipMasterInstance.themeManager.getAvailableThemes() - List themes");
