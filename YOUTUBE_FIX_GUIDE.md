# PiP Master - YouTube Fix Guide

## 🎬 YouTube-Specific Issues and Solutions

### 🚀 Quick Fix (Try This First!)

1. **Go to any YouTube video page**
2. **Open DevTools Console** (F12)
3. **Copy and paste this quick fix:**

```javascript
// Quick YouTube Fix
if (window.pipMasterInstance) {
  window.pipMasterInstance.platform = "youtube";
  window.pipMasterInstance.setupYouTubeDetection();
  window.pipMasterInstance.performUniversalVideoScan();
  console.log("YouTube fix applied!");
} else {
  console.log("Extension not loaded - check chrome://extensions/");
}
```

4. **Wait 3 seconds** and look for overlay buttons on the video

### 📋 Comprehensive Test Steps

1. **Go to YouTube**: Navigate to any YouTube video page
2. **Open DevTools**: Press F12, go to Console tab
3. **Run comprehensive test**: Copy/paste contents of `youtube-fix-test.js`
4. **Follow the detailed results**: The script will diagnose and fix issues automatically

### Common YouTube Issues

#### Issue 1: Extension Not Detecting YouTube Videos

**Symptoms:**

- Console shows "Basic scan found 0 videos" on YouTube
- No overlay buttons appear on YouTube videos
- Extension works on other sites but not YouTube

**Causes:**

- YouTube's complex video loading system
- Videos loaded dynamically after page load
- YouTube-specific selectors not working

**Solutions:**

1. **Manual video scan:**

```javascript
// Force a YouTube video scan
window.pipMasterDebug.scanForVideos();
```

2. **Check YouTube selectors:**

```javascript
// Test each YouTube selector
const selectors = [
  "#movie_player video",
  ".html5-video-player video",
  ".video-stream",
  "video.video-stream",
];

selectors.forEach((sel) => {
  const videos = document.querySelectorAll(sel);
  console.log(`${sel}: ${videos.length} videos`);
});
```

3. **Wait for video to load:**

```javascript
// YouTube videos load asynchronously
setTimeout(() => {
  window.pipMasterDebug.scanForVideos();
}, 3000);
```

#### Issue 2: YouTube Ad Blocking PiP

**Symptoms:**

- Extension detects videos but won't activate PiP
- Error: "Cannot use Picture-in-Picture on advertisements"
- PiP works after ads finish

**Solutions:**

1. **Wait for ads to finish** - This is expected behavior
2. **Check ad detection:**

```javascript
// Check if ads are currently showing
const moviePlayer = document.querySelector("#movie_player");
console.log("Ad showing:", moviePlayer?.classList.contains("ad-showing"));
```

3. **Skip ads if possible** or wait for main content

#### Issue 3: YouTube Navigation Breaking Extension

**Symptoms:**

- Extension works on first video
- Stops working when navigating to new videos
- Need to refresh page to make it work again

**Solutions:**

1. **The extension now listens for YouTube navigation events**
2. **If still having issues, manually trigger detection:**

```javascript
// Listen for YouTube navigation
document.addEventListener("yt-navigate-finish", () => {
  console.log("YouTube navigation detected");
  setTimeout(() => {
    window.pipMasterDebug.scanForVideos();
  }, 1000);
});
```

#### Issue 4: YouTube Theater/Fullscreen Mode Issues

**Symptoms:**

- Overlay appears in wrong position
- Overlay hidden behind YouTube controls
- PiP button not visible in fullscreen

**Solutions:**

1. **The extension now adjusts positioning for YouTube modes**
2. **Check positioning:**

```javascript
// Check YouTube player mode
const player = document.querySelector("#movie_player");
console.log("Fullscreen:", player?.classList.contains("ytp-fullscreen"));
console.log("Theater:", player?.classList.contains("ytp-large-width-mode"));
```

#### Issue 5: YouTube Premium/DRM Content

**Symptoms:**

- PiP fails with "NotAllowedError"
- Works on some videos but not others
- Error mentions policy restrictions

**Solutions:**

1. **This is a YouTube limitation** - some premium content blocks PiP
2. **Try the browser's built-in PiP:**
   - Right-click on video → "Picture in Picture"
3. **Check video type:**

```javascript
const video = document.querySelector("video");
console.log("Video src:", video.src || video.currentSrc);
console.log("Blob video (DRM):", video.src?.startsWith("blob:"));
```

### YouTube-Specific Debug Commands

Run these in the console on YouTube:

```javascript
// 1. Check YouTube detection
console.log("Platform:", window.pipMasterInstance?.platform);

// 2. Force YouTube video scan
window.pipMasterInstance?.setupYouTubeDetection();

// 3. Check movie player
const player = document.querySelector("#movie_player");
console.log("Movie player:", !!player);
console.log("Player videos:", player?.querySelectorAll("video").length);

// 4. Test YouTube selectors
document.querySelectorAll("#movie_player video").length;

// 5. Check for ads
document.querySelector("#movie_player")?.classList.contains("ad-showing");

// 6. Manual video processing
const video = document.querySelector("#movie_player video");
if (video) window.pipMasterInstance?.handleVideoFound(video);

// 7. Test PiP directly
const video = document.querySelector("video");
video?.requestPictureInPicture();
```

### YouTube Extension Reload Process

If YouTube isn't working:

1. **Go to chrome://extensions/**
2. **Find PiP Master extension**
3. **Click the reload button** 🔄
4. **Go back to YouTube**
5. **Refresh the YouTube page**
6. **Wait 3-5 seconds for detection**
7. **Run debug script if still not working**

### YouTube-Specific Settings

For best YouTube experience:

1. **Overlay Position**: "top-right" (avoids YouTube controls)
2. **Auto-activation**: Disabled (YouTube has many short videos)
3. **Hover activation**: Enabled (easier to discover)

### Expected YouTube Behavior

When working correctly on YouTube:

1. **Video loads** → Extension detects within 2-3 seconds
2. **Overlay appears** in top-right corner (avoiding YouTube UI)
3. **Click overlay** → PiP activates immediately
4. **Navigate to new video** → Extension re-detects automatically
5. **Ads play** → Extension waits for main content
6. **Alt+P shortcut** → Works on main video content

### YouTube Troubleshooting Checklist

- [ ] Extension loaded and enabled
- [ ] On a YouTube video page (not homepage)
- [ ] Video has started loading (not just thumbnail)
- [ ] Not currently showing ads
- [ ] Console shows "Platform detected: youtube"
- [ ] Console shows "YouTube video check..." messages
- [ ] Video element found with YouTube selectors
- [ ] Overlay created and positioned correctly

### Still Not Working on YouTube?

1. **Try incognito mode** (rules out extension conflicts)
2. **Disable other extensions** temporarily
3. **Try different YouTube videos** (some may have restrictions)
4. **Check Chrome version** (need 88+ for PiP)
5. **Try the simple debug version** (use `manifest-debug.json`)

### YouTube-Specific Error Messages

- **"Cannot use Picture-in-Picture on advertisements"** → Wait for ad to finish
- **"Picture-in-Picture is disabled by YouTube policy"** → Try different video
- **"Video is not ready for Picture-in-Picture"** → Wait for video to load
- **"NotAllowedError"** → YouTube Premium/DRM content restriction

### YouTube Test Videos

Try these YouTube videos for testing:

- Any music video (usually works well)
- Tech reviews or tutorials
- Public domain content
- Avoid: Live streams, premium content, very new uploads
