<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PiP Master - Professional Logo Generator</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        margin: 0;
      }
      .container {
        max-width: 900px;
        margin: 0 auto;
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
        padding: 30px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      }
      .header h1 {
        color: #333;
        margin-bottom: 10px;
        font-size: 28px;
      }
      .header p {
        color: #666;
        font-size: 16px;
      }
      .icon-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 25px;
        margin-bottom: 30px;
      }
      .icon-card {
        background: white;
        padding: 25px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        text-align: center;
        transition: transform 0.2s ease;
      }
      .icon-card:hover {
        transform: translateY(-2px);
      }
      .icon-card h3 {
        color: #333;
        margin-bottom: 15px;
        font-size: 18px;
      }
      .icon-preview {
        margin: 20px 0;
        padding: 25px;
        background: #f8f9fa;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 2px dashed #dee2e6;
      }
      .download-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 14px 28px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.2s ease;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }
      .download-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }
      .download-btn:active {
        transform: translateY(0);
      }
      .instructions {
        background: white;
        border-left: 4px solid #667eea;
        padding: 25px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      .instructions h3 {
        margin-top: 0;
        color: #667eea;
        font-size: 20px;
      }
      .instructions ol {
        color: #555;
        line-height: 1.6;
      }
      .instructions li {
        margin-bottom: 8px;
      }
      .instructions code {
        background: #f1f3f4;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: "Monaco", "Menlo", monospace;
        color: #d73a49;
      }
      .success {
        background: white;
        border-left: 4px solid #28a745;
        padding: 25px;
        border-radius: 8px;
        margin-top: 20px;
        display: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      .success h3 {
        margin-top: 0;
        color: #28a745;
        font-size: 20px;
      }
      .success ol {
        color: #555;
        line-height: 1.6;
      }
      .download-all {
        text-align: center;
        margin: 30px 0;
      }
      .download-all-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 16px 32px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.2s ease;
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
      }
      .download-all-btn:hover {
        background: #218838;
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🎬 PiP Master Professional Logo Generator</h1>
        <p>
          Create high-quality placeholder icons for immediate Chrome extension
          testing
        </p>
      </div>

      <div class="instructions">
        <h3>🚀 Quick Setup Instructions</h3>
        <ol>
          <li>
            Click "Download All Icons" below or download each icon individually
          </li>
          <li>
            Save the files with exact names: <code>icon16.png</code>,
            <code>icon48.png</code>, <code>icon128.png</code>
          </li>
          <li>
            Place them in the <code>icons/</code> folder of your PiP Master
            extension
          </li>
          <li>
            Load the extension in Chrome's developer mode
            (<code>chrome://extensions/</code>)
          </li>
          <li>Enable "Developer mode" and click "Load unpacked"</li>
        </ol>
      </div>

      <div class="download-all">
        <button class="download-all-btn" onclick="downloadAllIcons()">
          📦 Download All Icons (Recommended)
        </button>
      </div>

      <div class="icon-grid">
        <div class="icon-card">
          <h3>16x16 Icon</h3>
          <div class="icon-preview">
            <canvas
              id="icon16"
              width="16"
              height="16"
              style="
                border: 1px solid #ddd;
                image-rendering: pixelated;
                width: 64px;
                height: 64px;
              "
            ></canvas>
          </div>
          <button class="download-btn" onclick="downloadIcon('icon16', 16)">
            Download icon16.png
          </button>
        </div>

        <div class="icon-card">
          <h3>48x48 Icon</h3>
          <div class="icon-preview">
            <canvas
              id="icon48"
              width="48"
              height="48"
              style="border: 1px solid #ddd; width: 96px; height: 96px"
            ></canvas>
          </div>
          <button class="download-btn" onclick="downloadIcon('icon48', 48)">
            Download icon48.png
          </button>
        </div>

        <div class="icon-card">
          <h3>128x128 Icon</h3>
          <div class="icon-preview">
            <canvas
              id="icon128"
              width="128"
              height="128"
              style="border: 1px solid #ddd; width: 128px; height: 128px"
            ></canvas>
          </div>
          <button class="download-btn" onclick="downloadIcon('icon128', 128)">
            Download icon128.png
          </button>
        </div>
      </div>

      <div class="success" id="successMessage">
        <h3>🎉 Icons Generated Successfully!</h3>
        <p><strong>Professional PiP Master icons are ready for use!</strong></p>
        <ol>
          <li>
            Save the downloaded PNG files in your <code>icons/</code> folder
            with exact names:
            <ul style="margin: 8px 0; padding-left: 20px">
              <li><code>icon16.png</code> - Toolbar icon</li>
              <li><code>icon48.png</code> - Extension management</li>
              <li><code>icon128.png</code> - Chrome Web Store</li>
            </ul>
          </li>
          <li>Open Chrome and go to <code>chrome://extensions/</code></li>
          <li>Enable "Developer mode" (toggle in top-right)</li>
          <li>
            Click "Load unpacked" and select your PiP Master extension folder
          </li>
          <li>🚀 Test the extension on YouTube, Netflix, or any video site!</li>
        </ol>
        <p
          style="
            margin-top: 15px;
            padding: 10px;
            background: #f0f8ff;
            border-radius: 4px;
            font-size: 14px;
          "
        >
          <strong>💡 Pro Tip:</strong> These are high-quality placeholder icons.
          You can replace them with custom branded designs later while keeping
          the same filenames and sizes.
        </p>
      </div>
    </div>

    <script>
      // Create icons when page loads
      window.addEventListener("load", function () {
        createIcon16();
        createIcon48();
        createIcon128();
      });

      function createIcon16() {
        const canvas = document.getElementById("icon16");
        const ctx = canvas.getContext("2d");

        // Clear canvas
        ctx.clearRect(0, 0, 16, 16);

        // Background with rounded corners
        ctx.fillStyle = "#4285f4";
        ctx.fillRect(0, 0, 16, 16);

        // Main screen (white background)
        ctx.fillStyle = "#ffffff";
        ctx.fillRect(1, 2, 13, 9);

        // PiP window (blue overlay)
        ctx.fillStyle = "#667eea";
        ctx.fillRect(8, 4, 6, 4);

        // PiP window border
        ctx.strokeStyle = "#ffffff";
        ctx.lineWidth = 0.5;
        ctx.strokeRect(8, 4, 6, 4);

        // Play button (triangle)
        ctx.fillStyle = "#ffffff";
        ctx.beginPath();
        ctx.moveTo(3, 5);
        ctx.lineTo(3, 8);
        ctx.lineTo(6, 6.5);
        ctx.closePath();
        ctx.fill();

        // Small indicator dot
        ctx.fillStyle = "#ff4444";
        ctx.fillRect(13, 2, 2, 2);
      }

      function createIcon48() {
        const canvas = document.getElementById("icon48");
        const ctx = canvas.getContext("2d");

        // Clear canvas
        ctx.clearRect(0, 0, 48, 48);

        // Background gradient with rounded corners
        const gradient = ctx.createLinearGradient(0, 0, 48, 48);
        gradient.addColorStop(0, "#667eea");
        gradient.addColorStop(1, "#764ba2");
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.roundRect(0, 0, 48, 48, 6);
        ctx.fill();

        // Main screen with rounded corners and shadow
        ctx.fillStyle = "#ffffff";
        ctx.beginPath();
        ctx.roundRect(6, 10, 36, 24, 3);
        ctx.fill();

        // Screen border
        ctx.strokeStyle = "#e0e0e0";
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.roundRect(6, 10, 36, 24, 3);
        ctx.stroke();

        // PiP window with gradient
        const pipGradient = ctx.createLinearGradient(26, 14, 38, 22);
        pipGradient.addColorStop(0, "#4285f4");
        pipGradient.addColorStop(1, "#3367d6");
        ctx.fillStyle = pipGradient;
        ctx.beginPath();
        ctx.roundRect(26, 14, 12, 8, 2);
        ctx.fill();

        // PiP window border
        ctx.strokeStyle = "#ffffff";
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.roundRect(26, 14, 12, 8, 2);
        ctx.stroke();

        // Play button triangle
        ctx.fillStyle = "#4285f4";
        ctx.beginPath();
        ctx.moveTo(12, 18);
        ctx.lineTo(12, 26);
        ctx.lineTo(20, 22);
        ctx.closePath();
        ctx.fill();

        // Volume bars
        ctx.fillStyle = "#cccccc";
        ctx.fillRect(22, 20, 2, 1);
        ctx.fillRect(22, 22, 2, 1);
        ctx.fillRect(22, 24, 2, 1);

        // Active indicator
        ctx.fillStyle = "#28a745";
        ctx.beginPath();
        ctx.arc(40, 12, 2, 0, 2 * Math.PI);
        ctx.fill();
      }

      function createIcon128() {
        const canvas = document.getElementById("icon128");
        const ctx = canvas.getContext("2d");

        // Clear canvas
        ctx.clearRect(0, 0, 128, 128);

        // Background gradient with rounded corners
        const bgGradient = ctx.createLinearGradient(0, 0, 128, 128);
        bgGradient.addColorStop(0, "#667eea");
        bgGradient.addColorStop(0.5, "#764ba2");
        bgGradient.addColorStop(1, "#5a67d8");
        ctx.fillStyle = bgGradient;
        ctx.beginPath();
        ctx.roundRect(0, 0, 128, 128, 12);
        ctx.fill();

        // Drop shadow for main screen
        ctx.fillStyle = "rgba(0, 0, 0, 0.1)";
        ctx.beginPath();
        ctx.roundRect(14, 22, 104, 76, 8);
        ctx.fill();

        // Main screen with rounded corners
        ctx.fillStyle = "#ffffff";
        ctx.beginPath();
        ctx.roundRect(12, 20, 104, 76, 8);
        ctx.fill();

        // Screen border with subtle gradient
        ctx.strokeStyle = "#d0d0d0";
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.roundRect(12, 20, 104, 76, 8);
        ctx.stroke();

        // Inner screen content area
        ctx.fillStyle = "#f8f9fa";
        ctx.beginPath();
        ctx.roundRect(16, 24, 96, 68, 6);
        ctx.fill();

        // PiP window with gradient and shadow
        ctx.fillStyle = "rgba(66, 133, 244, 0.2)";
        ctx.beginPath();
        ctx.roundRect(74, 34, 32, 20, 4);
        ctx.fill();

        const pipGradient = ctx.createLinearGradient(72, 32, 104, 52);
        pipGradient.addColorStop(0, "#4285f4");
        pipGradient.addColorStop(1, "#3367d6");
        ctx.fillStyle = pipGradient;
        ctx.beginPath();
        ctx.roundRect(72, 32, 32, 20, 4);
        ctx.fill();

        // PiP window border
        ctx.strokeStyle = "#ffffff";
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.roundRect(72, 32, 32, 20, 4);
        ctx.stroke();

        // Play button with shadow
        ctx.fillStyle = "rgba(66, 133, 244, 0.3)";
        ctx.beginPath();
        ctx.moveTo(34, 46);
        ctx.lineTo(34, 66);
        ctx.lineTo(50, 56);
        ctx.closePath();
        ctx.fill();

        ctx.fillStyle = "#4285f4";
        ctx.beginPath();
        ctx.moveTo(32, 44);
        ctx.lineTo(32, 64);
        ctx.lineTo(48, 54);
        ctx.closePath();
        ctx.fill();

        // Volume bars with gradient
        const volGradient = ctx.createLinearGradient(52, 50, 64, 63);
        volGradient.addColorStop(0, "#28a745");
        volGradient.addColorStop(1, "#20c997");
        ctx.fillStyle = volGradient;
        ctx.fillRect(52, 50, 12, 3);
        ctx.fillRect(52, 55, 10, 3);
        ctx.fillRect(52, 60, 8, 3);

        // Active status indicator with glow
        ctx.fillStyle = "rgba(40, 167, 69, 0.3)";
        ctx.beginPath();
        ctx.arc(100, 36, 5, 0, 2 * Math.PI);
        ctx.fill();

        ctx.fillStyle = "#28a745";
        ctx.beginPath();
        ctx.arc(100, 36, 3, 0, 2 * Math.PI);
        ctx.fill();

        // Brand text (small)
        ctx.fillStyle = "#ffffff";
        ctx.font = "bold 8px Arial";
        ctx.textAlign = "center";
        ctx.fillText("PiP", 64, 110);
      }

      // Polyfill for roundRect if not supported
      if (!CanvasRenderingContext2D.prototype.roundRect) {
        CanvasRenderingContext2D.prototype.roundRect = function (
          x,
          y,
          width,
          height,
          radius
        ) {
          this.beginPath();
          this.moveTo(x + radius, y);
          this.lineTo(x + width - radius, y);
          this.quadraticCurveTo(x + width, y, x + width, y + radius);
          this.lineTo(x + width, y + height - radius);
          this.quadraticCurveTo(
            x + width,
            y + height,
            x + width - radius,
            y + height
          );
          this.lineTo(x + radius, y + height);
          this.quadraticCurveTo(x, y + height, x, y + height - radius);
          this.lineTo(x, y + radius);
          this.quadraticCurveTo(x, y, x + radius, y);
          this.closePath();
        };
      }

      function downloadIcon(canvasId, size) {
        const canvas = document.getElementById(canvasId);

        // Create download link
        canvas.toBlob(function (blob) {
          const link = document.createElement("a");
          link.download = `icon${size}.png`;
          link.href = URL.createObjectURL(blob);

          // Trigger download
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Clean up
          URL.revokeObjectURL(link.href);

          // Show success message
          document.getElementById("successMessage").style.display = "block";

          console.log(`Downloaded icon${size}.png`);
        }, "image/png");
      }

      function downloadAllIcons() {
        // Download all three icons with a small delay between each
        downloadIcon("icon16", 16);
        setTimeout(() => downloadIcon("icon48", 48), 500);
        setTimeout(() => downloadIcon("icon128", 128), 1000);

        // Show success message
        setTimeout(() => {
          document.getElementById("successMessage").style.display = "block";
        }, 1500);
      }
    </script>
  </body>
</html>
