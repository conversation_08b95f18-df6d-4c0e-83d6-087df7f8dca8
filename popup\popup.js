// PiP Master Popup Script
// Handles the extension popup interface and user interactions

class PiPMasterPopup {
  constructor() {
    this.settings = {};
    this.init();
  }

  async init() {
    try {
      // Load current settings
      await this.loadSettings();

      // Set up event listeners
      this.setupEventListeners();

      // Update UI with current settings
      this.updateUI();

      // Update status
      this.updateStatus();

      console.log("PiP Master: Popup initialized");
    } catch (error) {
      console.error("PiP Master: Failed to initialize popup:", error);
    }
  }

  async loadSettings() {
    try {
      const response = await this.sendMessage({ type: "GET_SETTINGS" });
      this.settings = response.data || {};
    } catch (error) {
      console.error("PiP Master: Failed to load settings:", error);
      this.settings = {};
    }
  }

  setupEventListeners() {
    // Toggle PiP button
    document.getElementById("togglePipBtn").addEventListener("click", () => {
      this.togglePiP();
    });

    // Settings toggles
    document.getElementById("enabledToggle").addEventListener("change", (e) => {
      this.updateSetting("enabled", e.target.checked);
    });

    document
      .getElementById("showOverlayToggle")
      .addEventListener("change", (e) => {
        this.updateSetting("showOverlay", e.target.checked);
      });

    document
      .getElementById("snapToCornersToggle")
      .addEventListener("change", (e) => {
        this.updateSetting("snapToCorners", e.target.checked);
      });

    document
      .getElementById("autoActivateToggle")
      .addEventListener("change", (e) => {
        this.updateSetting("autoActivate", e.target.checked);
      });

    // Opacity slider
    const opacitySlider = document.getElementById("opacitySlider");
    const opacityValue = document.getElementById("opacityValue");

    opacitySlider.addEventListener("input", (e) => {
      const value = parseFloat(e.target.value);
      opacityValue.textContent = Math.round(value * 100) + "%";
      this.updateSetting("opacity", value);
    });

    // Overlay position
    document
      .getElementById("overlayPosition")
      .addEventListener("change", (e) => {
        this.updateSetting("overlayPosition", e.target.value);
      });

    // Footer buttons
    document.getElementById("settingsBtn").addEventListener("click", () => {
      this.openSettings();
    });

    document.getElementById("helpBtn").addEventListener("click", () => {
      this.openHelp();
    });
  }

  updateUI() {
    // Update toggles
    document.getElementById("enabledToggle").checked =
      this.settings.enabled !== false;
    document.getElementById("showOverlayToggle").checked =
      this.settings.showOverlay !== false;
    document.getElementById("snapToCornersToggle").checked =
      this.settings.snapToCorners !== false;
    document.getElementById("autoActivateToggle").checked =
      this.settings.autoActivate === true;

    // Update opacity slider
    const opacity = this.settings.opacity || 0.9;
    document.getElementById("opacitySlider").value = opacity;
    document.getElementById("opacityValue").textContent =
      Math.round(opacity * 100) + "%";

    // Update overlay position
    document.getElementById("overlayPosition").value =
      this.settings.overlayPosition || "top-right";
  }

  async updateStatus() {
    try {
      // Get current tab
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });

      if (!tab) {
        this.setStatus("No active tab", 0);
        return;
      }

      // Check if content script is loaded and get video count
      try {
        const response = await chrome.tabs.sendMessage(tab.id, {
          type: "GET_VIDEO_COUNT",
        });
        if (response && response.success) {
          this.setStatus("Ready", response.data.count || 0);
        } else {
          this.setStatus("Loading...", 0);
        }
      } catch (error) {
        // Content script might not be loaded yet
        this.setStatus("Loading...", 0);
      }
    } catch (error) {
      console.error("PiP Master: Failed to update status:", error);
      this.setStatus("Error", 0);
    }
  }

  setStatus(status, videoCount) {
    document.getElementById("pipStatus").textContent = status;
    document.getElementById("videoCount").textContent = videoCount;
  }

  async togglePiP() {
    try {
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });

      if (!tab) {
        this.showError("No active tab found");
        return;
      }

      // Send toggle command to content script
      const response = await chrome.tabs.sendMessage(tab.id, {
        type: "KEYBOARD_COMMAND",
        command: "toggle-pip",
      });

      if (response && response.success) {
        // Update status after a short delay
        setTimeout(() => this.updateStatus(), 500);
      } else {
        this.showError("Failed to toggle Picture-in-Picture");
      }
    } catch (error) {
      console.error("PiP Master: Failed to toggle PiP:", error);
      this.showError("Picture-in-Picture not available on this page");
    }
  }

  async updateSetting(key, value) {
    try {
      this.settings[key] = value;

      await this.sendMessage({
        type: "UPDATE_SETTINGS",
        data: { [key]: value },
      });

      console.log(`PiP Master: Updated setting ${key} to ${value}`);
    } catch (error) {
      console.error("PiP Master: Failed to update setting:", error);
    }
  }

  openSettings() {
    chrome.runtime.openOptionsPage();
  }

  openHelp() {
    chrome.tabs.create({
      url: "https://github.com/pip-master/pip-master#readme",
    });
  }

  showError(message) {
    // Create a temporary error notification
    const notification = document.createElement("div");
    notification.className = "error-notification";
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 10px;
      left: 10px;
      right: 10px;
      background: #f44336;
      color: white;
      padding: 12px;
      border-radius: 6px;
      z-index: 1000;
      font-size: 13px;
      text-align: center;
      animation: slideDown 0.3s ease-out;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentElement) {
        notification.style.animation = "slideUp 0.3s ease-out";
        setTimeout(() => {
          if (notification.parentElement) {
            notification.parentElement.removeChild(notification);
          }
        }, 300);
      }
    }, 3000);
  }

  async sendMessage(message) {
    return new Promise((resolve, reject) => {
      try {
        chrome.runtime.sendMessage(message, (response) => {
          if (chrome.runtime.lastError) {
            console.error("PiP Master Popup: Runtime error:", chrome.runtime.lastError.message);
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }

          if (response && response.success === false) {
            console.error("PiP Master Popup: Message failed:", response.error);
            reject(new Error(response.error || "Unknown error"));
            return;
          }

          resolve(response || {});
        });
      } catch (error) {
        console.error("PiP Master Popup: Failed to send message:", error);
        reject(error);
      }
    });
  }
}

// Add CSS for error notifications
const style = document.createElement("style");
style.textContent = `
  @keyframes slideDown {
    from {
      transform: translateY(-100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  @keyframes slideUp {
    from {
      transform: translateY(0);
      opacity: 1;
    }
    to {
      transform: translateY(-100%);
      opacity: 0;
    }
  }
`;
document.head.appendChild(style);

// Initialize popup when DOM is ready
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", () => {
    new PiPMasterPopup();
  });
} else {
  new PiPMasterPopup();
}
