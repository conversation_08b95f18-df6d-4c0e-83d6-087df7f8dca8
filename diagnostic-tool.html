<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PiP Master - Diagnostic Tool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .diagnostic-section {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        .status-pass { background: #28a745; }
        .status-fail { background: #dc3545; }
        .status-warn { background: #ffc107; color: #333; }
        .status-pending { background: #6c757d; }
        .test-description {
            flex: 1;
        }
        .test-details {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        .run-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .run-btn:hover {
            background: #0056b3;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .error-details {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 PiP Master Diagnostic Tool</h1>
        <p>Automatically check your extension setup and identify issues</p>
    </div>

    <div class="diagnostic-section">
        <h2>🚀 Quick Diagnostics</h2>
        <button class="run-btn" onclick="runAllTests()">Run All Tests</button>
        <button class="run-btn" onclick="runBasicTests()">Basic Tests Only</button>
        <button class="run-btn" onclick="clearResults()">Clear Results</button>
        
        <div id="testResults">
            <div class="test-item">
                <div class="status-icon status-pending">?</div>
                <div class="test-description">
                    <strong>Chrome Version Check</strong>
                    <div class="test-details">Verifying Chrome 88+ for PiP support</div>
                </div>
            </div>
            
            <div class="test-item">
                <div class="status-icon status-pending">?</div>
                <div class="test-description">
                    <strong>Extension Installation</strong>
                    <div class="test-details">Checking if PiP Master is installed and enabled</div>
                </div>
            </div>
            
            <div class="test-item">
                <div class="status-icon status-pending">?</div>
                <div class="test-description">
                    <strong>Picture-in-Picture API</strong>
                    <div class="test-details">Testing browser PiP support</div>
                </div>
            </div>
            
            <div class="test-item">
                <div class="status-icon status-pending">?</div>
                <div class="test-description">
                    <strong>Video Detection</strong>
                    <div class="test-details">Checking for video elements on current page</div>
                </div>
            </div>
            
            <div class="test-item">
                <div class="status-icon status-pending">?</div>
                <div class="test-description">
                    <strong>Content Script</strong>
                    <div class="test-details">Verifying content script injection</div>
                </div>
            </div>
            
            <div class="test-item">
                <div class="status-icon status-pending">?</div>
                <div class="test-description">
                    <strong>Keyboard Shortcuts</strong>
                    <div class="test-details">Testing Alt+P, Alt+., Alt+, shortcuts</div>
                </div>
            </div>
        </div>
    </div>

    <div class="diagnostic-section">
        <h2>📊 System Information</h2>
        <div id="systemInfo">
            <p><strong>User Agent:</strong> <span id="userAgent">Loading...</span></p>
            <p><strong>Chrome Version:</strong> <span id="chromeVersion">Loading...</span></p>
            <p><strong>Platform:</strong> <span id="platform">Loading...</span></p>
            <p><strong>PiP Support:</strong> <span id="pipSupport">Loading...</span></p>
        </div>
    </div>

    <div class="diagnostic-section">
        <h2>🎥 Test Video</h2>
        <p>Use this test video to verify PiP functionality:</p>
        <video controls width="400" style="max-width: 100%;">
            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <div style="margin-top: 10px;">
            <button class="run-btn" onclick="testVideoPiP()">Test PiP on This Video</button>
        </div>
    </div>

    <div id="detailedResults" class="results" style="display: none;">
        <h3>📋 Detailed Results</h3>
        <div id="resultsContent"></div>
    </div>

    <script>
        let testResults = [];

        function updateTestStatus(index, status, details = '') {
            const testItems = document.querySelectorAll('.test-item');
            const statusIcon = testItems[index].querySelector('.status-icon');
            const testDetails = testItems[index].querySelector('.test-details');
            
            statusIcon.className = `status-icon status-${status}`;
            statusIcon.textContent = status === 'pass' ? '✓' : status === 'fail' ? '✗' : status === 'warn' ? '!' : '?';
            
            if (details) {
                testDetails.textContent = details;
            }
        }

        function addResult(test, status, message, details = '') {
            testResults.push({ test, status, message, details });
        }

        async function runAllTests() {
            clearResults();
            await runBasicTests();
            await runAdvancedTests();
            showDetailedResults();
        }

        async function runBasicTests() {
            // Test 1: Chrome Version
            try {
                const userAgent = navigator.userAgent;
                const chromeMatch = userAgent.match(/Chrome\/(\d+)/);
                if (chromeMatch) {
                    const version = parseInt(chromeMatch[1]);
                    if (version >= 88) {
                        updateTestStatus(0, 'pass', `Chrome ${version} - PiP supported`);
                        addResult('Chrome Version', 'pass', `Chrome ${version} detected`);
                    } else {
                        updateTestStatus(0, 'fail', `Chrome ${version} - Update required`);
                        addResult('Chrome Version', 'fail', `Chrome ${version} is too old. Need 88+`);
                    }
                } else {
                    updateTestStatus(0, 'warn', 'Chrome version not detected');
                    addResult('Chrome Version', 'warn', 'Could not detect Chrome version');
                }
            } catch (error) {
                updateTestStatus(0, 'fail', 'Version check failed');
                addResult('Chrome Version', 'fail', 'Error checking version', error.message);
            }

            // Test 2: Extension Installation
            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    updateTestStatus(1, 'pass', 'Extension APIs available');
                    addResult('Extension', 'pass', 'Chrome extension APIs detected');
                } else {
                    updateTestStatus(1, 'fail', 'Extension APIs not available');
                    addResult('Extension', 'fail', 'Chrome extension APIs not found');
                }
            } catch (error) {
                updateTestStatus(1, 'fail', 'Extension check failed');
                addResult('Extension', 'fail', 'Error checking extension', error.message);
            }

            // Test 3: PiP API Support
            try {
                if ('pictureInPictureEnabled' in document) {
                    if (document.pictureInPictureEnabled) {
                        updateTestStatus(2, 'pass', 'PiP API available and enabled');
                        addResult('PiP API', 'pass', 'Picture-in-Picture API is supported');
                    } else {
                        updateTestStatus(2, 'warn', 'PiP API disabled by policy');
                        addResult('PiP API', 'warn', 'PiP API disabled by site policy');
                    }
                } else {
                    updateTestStatus(2, 'fail', 'PiP API not supported');
                    addResult('PiP API', 'fail', 'Picture-in-Picture API not available');
                }
            } catch (error) {
                updateTestStatus(2, 'fail', 'PiP API check failed');
                addResult('PiP API', 'fail', 'Error checking PiP API', error.message);
            }

            // Test 4: Video Detection
            try {
                const videos = document.querySelectorAll('video');
                if (videos.length > 0) {
                    updateTestStatus(3, 'pass', `${videos.length} video(s) found`);
                    addResult('Video Detection', 'pass', `Found ${videos.length} video elements`);
                } else {
                    updateTestStatus(3, 'warn', 'No videos found on this page');
                    addResult('Video Detection', 'warn', 'No video elements detected');
                }
            } catch (error) {
                updateTestStatus(3, 'fail', 'Video detection failed');
                addResult('Video Detection', 'fail', 'Error detecting videos', error.message);
            }

            // Update system info
            updateSystemInfo();
        }

        async function runAdvancedTests() {
            // Test 5: Content Script
            try {
                // Check if PiP Master content script is loaded
                const pipMasterLoaded = window.pipMasterContentLoaded || false;
                if (pipMasterLoaded) {
                    updateTestStatus(4, 'pass', 'Content script loaded');
                    addResult('Content Script', 'pass', 'PiP Master content script detected');
                } else {
                    updateTestStatus(4, 'warn', 'Content script not detected');
                    addResult('Content Script', 'warn', 'PiP Master content script not found');
                }
            } catch (error) {
                updateTestStatus(4, 'fail', 'Content script check failed');
                addResult('Content Script', 'fail', 'Error checking content script', error.message);
            }

            // Test 6: Keyboard Shortcuts
            try {
                updateTestStatus(5, 'warn', 'Manual testing required');
                addResult('Keyboard Shortcuts', 'warn', 'Press Alt+P to test shortcuts manually');
            } catch (error) {
                updateTestStatus(5, 'fail', 'Shortcut test failed');
                addResult('Keyboard Shortcuts', 'fail', 'Error testing shortcuts', error.message);
            }
        }

        function updateSystemInfo() {
            document.getElementById('userAgent').textContent = navigator.userAgent;
            
            const chromeMatch = navigator.userAgent.match(/Chrome\/(\d+\.\d+\.\d+\.\d+)/);
            document.getElementById('chromeVersion').textContent = chromeMatch ? chromeMatch[1] : 'Not detected';
            
            document.getElementById('platform').textContent = navigator.platform;
            
            const pipSupported = 'pictureInPictureEnabled' in document;
            document.getElementById('pipSupport').textContent = pipSupported ? 'Yes' : 'No';
        }

        function testVideoPiP() {
            const video = document.querySelector('video');
            if (!video) {
                alert('No video found to test');
                return;
            }

            video.requestPictureInPicture()
                .then(() => {
                    alert('✅ Picture-in-Picture activated successfully!');
                    addResult('Video PiP Test', 'pass', 'Successfully activated PiP on test video');
                })
                .catch(error => {
                    alert('❌ Picture-in-Picture failed: ' + error.message);
                    addResult('Video PiP Test', 'fail', 'Failed to activate PiP', error.message);
                });
        }

        function showDetailedResults() {
            const resultsDiv = document.getElementById('detailedResults');
            const contentDiv = document.getElementById('resultsContent');
            
            let html = '';
            testResults.forEach(result => {
                const statusClass = result.status === 'pass' ? 'status-pass' : 
                                  result.status === 'fail' ? 'status-fail' : 'status-warn';
                
                html += `
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; align-items: center; margin-bottom: 5px;">
                            <span class="status-icon ${statusClass}" style="width: 20px; height: 20px; font-size: 12px; margin-right: 10px;">
                                ${result.status === 'pass' ? '✓' : result.status === 'fail' ? '✗' : '!'}
                            </span>
                            <strong>${result.test}:</strong> ${result.message}
                        </div>
                        ${result.details ? `<div class="error-details">${result.details}</div>` : ''}
                    </div>
                `;
            });
            
            contentDiv.innerHTML = html;
            resultsDiv.style.display = 'block';
        }

        function clearResults() {
            testResults = [];
            document.getElementById('detailedResults').style.display = 'none';
            
            // Reset all test statuses
            for (let i = 0; i < 6; i++) {
                updateTestStatus(i, 'pending', '');
            }
        }

        // Initialize system info on page load
        window.addEventListener('load', updateSystemInfo);

        // Listen for keyboard shortcuts to test them
        document.addEventListener('keydown', function(event) {
            if (event.altKey) {
                if (event.key === 'p' || event.key === 'P') {
                    console.log('PiP Master: Alt+P detected');
                    addResult('Keyboard Test', 'pass', 'Alt+P shortcut detected');
                } else if (event.key === '.') {
                    console.log('PiP Master: Alt+. detected');
                    addResult('Keyboard Test', 'pass', 'Alt+. shortcut detected');
                } else if (event.key === ',') {
                    console.log('PiP Master: Alt+, detected');
                    addResult('Keyboard Test', 'pass', 'Alt+, shortcut detected');
                }
            }
        });
    </script>
</body>
</html>
