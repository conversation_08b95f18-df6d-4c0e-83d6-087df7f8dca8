# PiP Master - Universal Picture-in-Picture Chrome Extension

A comprehensive Picture-in-Picture Chrome extension that works universally across all major video platforms with enhanced user experience.

## 🚀 Features

### Core Functionality

- **Universal Compatibility**: Works on YouTube, Netflix, Prime Video, Udemy, Vimeo, Twitch, and more
- **One-click PiP Toggle**: Overlay buttons appear on videos for instant PiP activation
- **Global Keyboard Shortcuts**: Quick access with customizable hotkeys (default: Alt+P)
- **Smart Video Detection**: Automatically detects suitable videos on any website

### Enhanced Controls

- **Resizable Windows**: Drag corners to resize PiP windows
- **Snap-to-Corners**: Automatically snap windows to screen corners
- **Opacity Controls**: Adjust transparency with keyboard shortcuts or settings
- **Audio Controls**: Volume controls within PiP window
- **Always-on-Top**: Keep videos above other windows

### User Experience

- **Dark/Light Theme Support**: Adapts to system preferences
- **Smooth Animations**: Polished transitions and hover effects
- **Accessibility**: Full keyboard navigation and screen reader support
- **Minimal Permissions**: Privacy-focused with only necessary permissions

## 📦 Installation

### From Chrome Web Store (Coming Soon)

1. Visit the Chrome Web Store
2. Search for "PiP Master"
3. Click "Add to Chrome"

### Manual Installation (Development)

1. Clone or download this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension folder
5. The extension icon should appear in your toolbar

## 🎯 Usage

### Quick Start

1. Navigate to any video website (YouTube, Netflix, etc.)
2. Play a video
3. Look for the PiP button overlay (usually in top-right corner)
4. Click the button or press `Alt+P` to activate Picture-in-Picture

### Keyboard Shortcuts

- `Alt+P` - Toggle Picture-in-Picture mode
- `Alt+.` - Increase PiP window opacity
- `Alt+,` - Decrease PiP window opacity

### Settings

- Click the extension icon in the toolbar to access quick settings
- Right-click the extension icon and select "Options" for advanced settings
- Customize overlay position, opacity, theme, and keyboard shortcuts

## 🛠️ Technical Details

### Architecture

- **Manifest V3**: Uses the latest Chrome extension standards
- **Content Scripts**: Universal video detection across all websites
- **Background Service Worker**: Handles global shortcuts and settings
- **Chrome Storage API**: Syncs settings across devices
- **Picture-in-Picture Web API**: Native browser PiP implementation

### File Structure

```
PIP/
├── manifest.json              # Extension manifest
├── background.js              # Service worker
├── content/
│   ├── content.js            # Video detection and PiP logic
│   └── content.css           # Overlay styles
├── popup/
│   ├── popup.html            # Extension popup
│   ├── popup.js              # Popup functionality
│   └── popup.css             # Popup styles
├── options/
│   ├── options.html          # Settings page
│   ├── options.js            # Settings functionality
│   └── options.css           # Settings styles
└── icons/                    # Extension icons
```

### Supported Platforms

- YouTube (all variants)
- Netflix
- Amazon Prime Video
- Disney+
- Hulu
- Twitch
- Vimeo
- Udemy
- Coursera
- And virtually any website with HTML5 videos

## 🔧 Development

### Prerequisites

- Chrome browser (version 88+)
- Basic knowledge of JavaScript, HTML, CSS

### Setup

1. Clone the repository
2. Make your changes
3. Load the extension in Chrome for testing
4. Test on various video platforms

### Building

No build process required - this is a pure JavaScript extension.

### Testing

1. Test on multiple video platforms
2. Verify keyboard shortcuts work
3. Check settings persistence
4. Test accessibility features

## 🤝 Contributing

We welcome contributions! Please:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Areas for Contribution

- Additional video platform support
- UI/UX improvements
- Accessibility enhancements
- Bug fixes
- Documentation improvements

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🐛 Bug Reports

Found a bug? Please create an issue with:

- Chrome version
- Extension version
- Website where the issue occurred
- Steps to reproduce
- Expected vs actual behavior

## 🔮 Roadmap

### Version 1.1

- [ ] Custom PiP window controls
- [ ] Video playback speed controls
- [ ] Multiple PiP windows support
- [ ] Advanced video filters

### Version 1.2

- [ ] Cloud settings sync
- [ ] Usage analytics (privacy-focused)
- [ ] Pro features (optional)
- [ ] Mobile companion app

## 💡 FAQ

**Q: Why doesn't PiP work on some videos?**
A: Some streaming services block PiP for DRM-protected content. This is a browser/platform limitation.

**Q: Can I use multiple PiP windows?**
A: Currently, browsers support only one PiP window at a time. Multiple window support is planned for future versions.

**Q: Is my data collected?**
A: No, PiP Master only stores your settings locally and doesn't collect any personal data.

**Q: Why do I need the "Access your data on all websites" permission?**
A: This allows the extension to detect videos on any website you visit. No data is transmitted or stored.

## 🙏 Acknowledgments

- Chrome team for the Picture-in-Picture Web API
- Open source community for inspiration and feedback
- Beta testers for their valuable input

---

Made with ❤️ for better video watching experiences.
