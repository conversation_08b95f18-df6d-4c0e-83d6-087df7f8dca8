// Enhanced Settings Panel and Feature Integration
// Comprehensive control panel for all enhanced features

console.log("⚙️ Enhanced Settings Panel");
console.log("==========================");

// Settings Panel Manager
class SettingsPanelManager {
  constructor(pipMaster) {
    this.pipMaster = pipMaster;
    this.panel = null;
    this.isVisible = false;
    this.settings = this.loadSettings();
    this.createSettingsPanel();
    this.setupKeyboardShortcuts();
  }

  loadSettings() {
    try {
      const stored = localStorage.getItem("pipMaster_enhancedSettings");
      return stored ? JSON.parse(stored) : this.getDefaultSettings();
    } catch (error) {
      console.warn("Failed to load settings:", error);
      return this.getDefaultSettings();
    }
  }

  getDefaultSettings() {
    return {
      // Smart Auto-PiP
      autoEnable: false,
      autoExitOnTabReturn: true,

      // Themes
      overlayTheme: "default",

      // Performance
      lowPowerMode: false,
      scanFrequency: "normal",

      // Accessibility
      highContrast: false,
      reducedMotion: false,
      screenReaderMode: false,

      // Advanced Controls
      volumeControlEnabled: true,
      speedControlEnabled: true,
      skipControlEnabled: true,
      audioControlEnabled: true,

      // Timeline Controls
      timelinePreviewEnabled: true,

      // Site Preferences
      rememberSitePreferences: true,
      autoApplySiteSettings: true,
    };
  }

  saveSettings() {
    try {
      localStorage.setItem(
        "pipMaster_enhancedSettings",
        JSON.stringify(this.settings)
      );
      console.log("✅ Settings saved");
    } catch (error) {
      console.warn("Failed to save settings:", error);
    }
  }

  createSettingsPanel() {
    this.panel = document.createElement("div");
    this.panel.id = "pip-master-settings-panel";
    this.panel.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 400px;
      max-height: 80vh;
      background: #ffffff;
      border: 1px solid #ddd;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      z-index: 10002;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      display: none;
      overflow-y: auto;
    `;

    this.panel.innerHTML = this.generatePanelHTML();
    document.body.appendChild(this.panel);

    this.setupEventListeners();
  }

  generatePanelHTML() {
    return `
      <div style="padding: 20px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 15px;">
          <h2 style="margin: 0; color: #333; font-size: 18px;">🎬 PiP Master Settings</h2>
          <button id="pip-settings-close" style="background: none; border: none; font-size: 20px; cursor: pointer; color: #666;">×</button>
        </div>

        <!-- Smart Auto-PiP Section -->
        <div class="settings-section" style="margin-bottom: 20px;">
          <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">🔄 Smart Auto-PiP</h3>
          <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
            <input type="checkbox" id="auto-enable" ${
              this.settings.autoEnable ? "checked" : ""
            } style="margin-right: 8px;">
            Enable Auto-PiP on tab switch
          </label>
          <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
            <input type="checkbox" id="auto-exit" ${
              this.settings.autoExitOnTabReturn ? "checked" : ""
            } style="margin-right: 8px;">
            Auto-exit PiP when returning to tab
          </label>
        </div>

        <!-- Theme Section -->
        <div class="settings-section" style="margin-bottom: 20px;">
          <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">🎨 Overlay Theme</h3>
          <select id="overlay-theme" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
            <option value="default" ${
              this.settings.overlayTheme === "default" ? "selected" : ""
            }>Default</option>
            <option value="minimal" ${
              this.settings.overlayTheme === "minimal" ? "selected" : ""
            }>Minimal</option>
            <option value="neon" ${
              this.settings.overlayTheme === "neon" ? "selected" : ""
            }>Neon</option>
            <option value="dark" ${
              this.settings.overlayTheme === "dark" ? "selected" : ""
            }>Dark Pro</option>
            <option value="youtube" ${
              this.settings.overlayTheme === "youtube" ? "selected" : ""
            }>YouTube Style</option>
          </select>
        </div>

        <!-- Performance Section -->
        <div class="settings-section" style="margin-bottom: 20px;">
          <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">⚡ Performance</h3>
          <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
            <input type="checkbox" id="low-power" ${
              this.settings.lowPowerMode ? "checked" : ""
            } style="margin-right: 8px;">
            Enable low power mode
          </label>
          <div style="margin-bottom: 8px;">
            <label style="display: block; margin-bottom: 4px;">Scan Frequency:</label>
            <select id="scan-frequency" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
              <option value="minimal" ${
                this.settings.scanFrequency === "minimal" ? "selected" : ""
              }>Minimal (Battery Saver)</option>
              <option value="reduced" ${
                this.settings.scanFrequency === "reduced" ? "selected" : ""
              }>Reduced</option>
              <option value="normal" ${
                this.settings.scanFrequency === "normal" ? "selected" : ""
              }>Normal</option>
            </select>
          </div>
        </div>

        <!-- Accessibility Section -->
        <div class="settings-section" style="margin-bottom: 20px;">
          <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">♿ Accessibility</h3>
          <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
            <input type="checkbox" id="high-contrast" ${
              this.settings.highContrast ? "checked" : ""
            } style="margin-right: 8px;">
            High contrast mode
          </label>
          <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
            <input type="checkbox" id="reduced-motion" ${
              this.settings.reducedMotion ? "checked" : ""
            } style="margin-right: 8px;">
            Reduced motion
          </label>
          <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
            <input type="checkbox" id="screen-reader" ${
              this.settings.screenReaderMode ? "checked" : ""
            } style="margin-right: 8px;">
            Enhanced screen reader support
          </label>
        </div>

        <!-- Advanced Controls Section -->
        <div class="settings-section" style="margin-bottom: 20px;">
          <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">🎮 PiP Window Controls</h3>
          <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
            <input type="checkbox" id="volume-control" ${
              this.settings.volumeControlEnabled ? "checked" : ""
            } style="margin-right: 8px;">
            Volume control (Ctrl + ↑/↓)
          </label>
          <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
            <input type="checkbox" id="speed-control" ${
              this.settings.speedControlEnabled ? "checked" : ""
            } style="margin-right: 8px;">
            Playback speed control (Shift + &lt;/&gt;)
          </label>
          <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
            <input type="checkbox" id="skip-control" ${
              this.settings.skipControlEnabled ? "checked" : ""
            } style="margin-right: 8px;">
            Skip controls (Ctrl + ←/→)
          </label>
          <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
            <input type="checkbox" id="audio-control-enabled" ${
              this.settings.audioControlEnabled ? "checked" : ""
            } style="margin-right: 8px;">
            Enable audio in Picture-in-Picture mode
          </label>
        </div>

        <!-- Timeline Controls Section -->
        <div class="settings-section" style="margin-bottom: 20px;">
          <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">⏯️ Timeline Controls</h3>
          <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
            <input type="checkbox" id="timeline-preview-enabled" ${
              this.settings.timelinePreviewEnabled ? "checked" : ""
            } style="margin-right: 8px;">
            Enable timeline preview on hover
          </label>
          <div style="font-size: 12px; color: #666; margin-left: 24px; line-height: 1.4;">
            When enabled, timeline controls appear when hovering near the bottom of the PiP window
          </div>
        </div>

        <!-- Site Preferences Section -->
        <div class="settings-section" style="margin-bottom: 20px;">
          <h3 style="margin: 0 0 10px 0; color: #444; font-size: 16px;">🌐 Site Preferences</h3>
          <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
            <input type="checkbox" id="remember-sites" ${
              this.settings.rememberSitePreferences ? "checked" : ""
            } style="margin-right: 8px;">
            Remember preferences per site
          </label>
          <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
            <input type="checkbox" id="auto-apply-sites" ${
              this.settings.autoApplySiteSettings ? "checked" : ""
            } style="margin-right: 8px;">
            Auto-apply site-specific settings
          </label>
        </div>

        <!-- Action Buttons -->
        <div style="display: flex; gap: 10px; margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee;">
          <button id="pip-settings-save" style="flex: 1; padding: 10px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">Save Settings</button>
          <button id="pip-settings-reset" style="flex: 1; padding: 10px; background: #666; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">Reset to Defaults</button>
        </div>

        <!-- Keyboard Shortcuts Info -->
        <div style="margin-top: 15px; padding: 10px; background: #f5f5f5; border-radius: 4px; font-size: 12px; color: #666;">
          <strong>Keyboard Shortcuts:</strong><br>
          Alt + P: Toggle PiP<br>
          Alt + S: Open Settings<br>
          Escape: Close Settings/Exit PiP
        </div>
      </div>
    `;
  }

  setupEventListeners() {
    // Close button
    this.panel
      .querySelector("#pip-settings-close")
      .addEventListener("click", () => {
        this.hidePanel();
      });

    // Save button
    this.panel
      .querySelector("#pip-settings-save")
      .addEventListener("click", () => {
        this.saveCurrentSettings();
      });

    // Reset button
    this.panel
      .querySelector("#pip-settings-reset")
      .addEventListener("click", () => {
        this.resetToDefaults();
      });

    // Real-time updates for some settings
    this.panel
      .querySelector("#overlay-theme")
      .addEventListener("change", (e) => {
        if (this.pipMaster.themeManager) {
          this.pipMaster.themeManager.setTheme(e.target.value);
        }
      });

    this.panel
      .querySelector("#high-contrast")
      .addEventListener("change", (e) => {
        if (this.pipMaster.accessibility) {
          if (e.target.checked) {
            this.pipMaster.accessibility.enableHighContrastMode();
          } else {
            this.pipMaster.accessibility.disableHighContrastMode();
          }
        }
      });

    this.panel
      .querySelector("#reduced-motion")
      .addEventListener("change", (e) => {
        if (this.pipMaster.accessibility) {
          if (e.target.checked) {
            this.pipMaster.accessibility.enableReducedMotionMode();
          } else {
            this.pipMaster.accessibility.disableReducedMotionMode();
          }
        }
      });

    // Close on escape key
    document.addEventListener("keydown", (e) => {
      if (e.key === "Escape" && this.isVisible) {
        this.hidePanel();
      }
    });

    // Close on outside click
    this.panel.addEventListener("click", (e) => {
      if (e.target === this.panel) {
        this.hidePanel();
      }
    });
  }

  setupKeyboardShortcuts() {
    document.addEventListener("keydown", (e) => {
      // Alt + S to open settings
      if (e.altKey && e.key === "s") {
        e.preventDefault();
        this.togglePanel();
      }
    });
  }

  showPanel() {
    this.panel.style.display = "block";
    this.isVisible = true;

    // Focus management for accessibility
    this.panel.querySelector("#pip-settings-close").focus();

    console.log("⚙️ Settings panel opened");
  }

  hidePanel() {
    this.panel.style.display = "none";
    this.isVisible = false;
    console.log("⚙️ Settings panel closed");
  }

  togglePanel() {
    if (this.isVisible) {
      this.hidePanel();
    } else {
      this.showPanel();
    }
  }

  saveCurrentSettings() {
    // Collect all settings from the form
    this.settings.autoEnable = this.panel.querySelector("#auto-enable").checked;
    this.settings.autoExitOnTabReturn =
      this.panel.querySelector("#auto-exit").checked;
    this.settings.overlayTheme =
      this.panel.querySelector("#overlay-theme").value;
    this.settings.lowPowerMode = this.panel.querySelector("#low-power").checked;
    this.settings.scanFrequency =
      this.panel.querySelector("#scan-frequency").value;
    this.settings.highContrast =
      this.panel.querySelector("#high-contrast").checked;
    this.settings.reducedMotion =
      this.panel.querySelector("#reduced-motion").checked;
    this.settings.screenReaderMode =
      this.panel.querySelector("#screen-reader").checked;
    this.settings.volumeControlEnabled =
      this.panel.querySelector("#volume-control").checked;
    this.settings.speedControlEnabled =
      this.panel.querySelector("#speed-control").checked;
    this.settings.skipControlEnabled =
      this.panel.querySelector("#skip-control").checked;
    this.settings.audioControlEnabled = this.panel.querySelector(
      "#audio-control-enabled"
    ).checked;
    this.settings.timelinePreviewEnabled = this.panel.querySelector(
      "#timeline-preview-enabled"
    ).checked;
    this.settings.rememberSitePreferences =
      this.panel.querySelector("#remember-sites").checked;
    this.settings.autoApplySiteSettings =
      this.panel.querySelector("#auto-apply-sites").checked;

    this.saveSettings();
    this.applySettings();

    // Show confirmation
    this.showNotification("Settings saved successfully!", "success");
  }

  resetToDefaults() {
    if (confirm("Reset all settings to defaults? This cannot be undone.")) {
      this.settings = this.getDefaultSettings();
      this.saveSettings();
      this.updatePanelFromSettings();
      this.applySettings();

      this.showNotification("Settings reset to defaults", "info");
    }
  }

  updatePanelFromSettings() {
    // Update all form elements from current settings
    this.panel.querySelector("#auto-enable").checked = this.settings.autoEnable;
    this.panel.querySelector("#auto-exit").checked =
      this.settings.autoExitOnTabReturn;
    this.panel.querySelector("#overlay-theme").value =
      this.settings.overlayTheme;
    this.panel.querySelector("#low-power").checked = this.settings.lowPowerMode;
    this.panel.querySelector("#scan-frequency").value =
      this.settings.scanFrequency;
    this.panel.querySelector("#high-contrast").checked =
      this.settings.highContrast;
    this.panel.querySelector("#reduced-motion").checked =
      this.settings.reducedMotion;
    this.panel.querySelector("#screen-reader").checked =
      this.settings.screenReaderMode;
    this.panel.querySelector("#volume-control").checked =
      this.settings.volumeControlEnabled;
    this.panel.querySelector("#speed-control").checked =
      this.settings.speedControlEnabled;
    this.panel.querySelector("#skip-control").checked =
      this.settings.skipControlEnabled;
    this.panel.querySelector("#audio-control-enabled").checked =
      this.settings.audioControlEnabled;
    this.panel.querySelector("#timeline-preview-enabled").checked =
      this.settings.timelinePreviewEnabled;
    this.panel.querySelector("#remember-sites").checked =
      this.settings.rememberSitePreferences;
    this.panel.querySelector("#auto-apply-sites").checked =
      this.settings.autoApplySiteSettings;
  }

  applySettings() {
    // Apply all settings to the extension
    if (this.pipMaster.smartAutoPiP) {
      if (this.settings.autoEnable) {
        this.pipMaster.smartAutoPiP.enable();
      } else {
        this.pipMaster.smartAutoPiP.disable();
      }
    }

    if (this.pipMaster.themeManager) {
      this.pipMaster.themeManager.setTheme(this.settings.overlayTheme);
    }

    if (this.pipMaster.performanceOptimizer) {
      if (this.settings.lowPowerMode) {
        this.pipMaster.performanceOptimizer.enableLowPowerMode();
      } else {
        this.pipMaster.performanceOptimizer.disableLowPowerMode();
      }
    }

    if (this.pipMaster.accessibility) {
      if (this.settings.highContrast) {
        this.pipMaster.accessibility.enableHighContrastMode();
      } else {
        this.pipMaster.accessibility.disableHighContrastMode();
      }

      if (this.settings.reducedMotion) {
        this.pipMaster.accessibility.enableReducedMotionMode();
      } else {
        this.pipMaster.accessibility.disableReducedMotionMode();
      }
    }

    // Apply audio control settings
    this.applyAudioControlSettings();

    // Apply timeline preview settings
    this.applyTimelinePreviewSettings();

    console.log("✅ All settings applied");
  }

  applyAudioControlSettings() {
    // Set up audio control behavior for PiP
    if (this.settings.audioControlEnabled) {
      // Enable audio in PiP mode
      this.setupAudioControlListeners(true);
    } else {
      // Disable audio in PiP mode
      this.setupAudioControlListeners(false);
    }
  }

  setupAudioControlListeners(enableAudio) {
    // Remove existing listeners
    document.removeEventListener(
      "enterpictureinpicture",
      this.pipEnterAudioHandler
    );
    document.removeEventListener(
      "leavepictureinpicture",
      this.pipLeaveAudioHandler
    );

    if (enableAudio) {
      // When audio is enabled, preserve current audio state
      this.pipEnterAudioHandler = (event) => {
        const video = event.target;
        // Store original volume for restoration
        video._pipMasterOriginalVolume = video.volume;
        video._pipMasterOriginalMuted = video.muted;
        console.log("🔊 Audio preserved in PiP mode");
      };

      this.pipLeaveAudioHandler = (event) => {
        const video = event.target;
        // Restore original audio state if it was stored
        if (video._pipMasterOriginalVolume !== undefined) {
          video.volume = video._pipMasterOriginalVolume;
          video.muted = video._pipMasterOriginalMuted;
          delete video._pipMasterOriginalVolume;
          delete video._pipMasterOriginalMuted;
          console.log("🔊 Audio state restored after PiP");
        }
      };
    } else {
      // When audio is disabled, mute during PiP
      this.pipEnterAudioHandler = (event) => {
        const video = event.target;
        // Store original volume and mute
        video._pipMasterOriginalVolume = video.volume;
        video._pipMasterOriginalMuted = video.muted;
        video.muted = true;
        console.log("🔇 Audio muted in PiP mode");
      };

      this.pipLeaveAudioHandler = (event) => {
        const video = event.target;
        // Restore original audio state
        if (video._pipMasterOriginalVolume !== undefined) {
          video.volume = video._pipMasterOriginalVolume;
          video.muted = video._pipMasterOriginalMuted;
          delete video._pipMasterOriginalVolume;
          delete video._pipMasterOriginalMuted;
          console.log("🔊 Audio restored after PiP");
        }
      };
    }

    // Add the appropriate listeners
    document.addEventListener(
      "enterpictureinpicture",
      this.pipEnterAudioHandler
    );
    document.addEventListener(
      "leavepictureinpicture",
      this.pipLeaveAudioHandler
    );
  }

  applyTimelinePreviewSettings() {
    // Apply timeline preview settings to timeline control
    if (this.pipMaster.timelineControl) {
      if (this.settings.timelinePreviewEnabled) {
        // Enable hover-based timeline preview
        this.pipMaster.timelineControl.enableHoverPreview();
        console.log("⏯️ Timeline preview on hover enabled");
      } else {
        // Disable hover-based timeline preview
        this.pipMaster.timelineControl.disableHoverPreview();
        console.log("⏯️ Timeline preview on hover disabled");
      }
    }
  }

  showNotification(message, type = "info") {
    const notification = document.createElement("div");
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 16px;
      background: ${
        type === "success"
          ? "#4caf50"
          : type === "error"
          ? "#f44336"
          : "#2196f3"
      };
      color: white;
      border-radius: 4px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      z-index: 10003;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentElement) {
        notification.parentElement.removeChild(notification);
      }
    }, 3000);
  }
}

// Initialize settings panel
window.initializeSettingsPanel = function () {
  console.log("⚙️ Initializing Settings Panel");

  if (!window.pipMasterInstance) {
    console.error("❌ PiP Master instance not available");
    return false;
  }

  // Initialize Settings Panel
  window.pipMasterInstance.settingsPanel = new SettingsPanelManager(
    window.pipMasterInstance
  );

  // Apply saved settings
  window.pipMasterInstance.settingsPanel.applySettings();

  console.log("✅ Settings panel initialized");
  console.log("💡 Press Alt + S to open settings panel");

  return true;
};

// Auto-initialize
if (window.pipMasterInstance) {
  window.initializeSettingsPanel();
} else {
  setTimeout(() => {
    if (window.pipMasterInstance) {
      window.initializeSettingsPanel();
    }
  }, 2000);
}

console.log("\n📋 Settings Panel Commands:");
console.log("===========================");
console.log(
  "initializeSettingsPanel()                           - Initialize settings panel"
);
console.log(
  "pipMasterInstance.settingsPanel.showPanel()         - Show settings panel"
);
console.log(
  "pipMasterInstance.settingsPanel.hidePanel()         - Hide settings panel"
);
console.log(
  "Alt + S                                              - Toggle settings panel"
);
