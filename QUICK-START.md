# 🚀 PiP Master - Quick Start Guide

## ⚡ **5-Minute Setup**

### **Step 1: Generate Icons (2 minutes)**
1. Open `icons/create-placeholder-icons.html` in Chrome
2. Click "📦 Download All Icons (Recommended)"
3. Save files as `icon16.png`, `icon48.png`, `icon128.png` in the `icons/` folder

### **Step 2: Load Extension (1 minute)**
1. Open Chrome → `chrome://extensions/`
2. Enable "Developer mode" (top-right toggle)
3. Click "Load unpacked" → Select the `PIP` folder
4. Extension should appear with custom PiP icon

### **Step 3: Test Functionality (2 minutes)**
1. Open `test-page.html` or go to YouTube
2. Play a video
3. Press `Alt+P` or click the PiP overlay button
4. Test opacity controls: `Alt+.` (increase) and `Alt+,` (decrease)

---

## ✅ **Verification Checklist**

**Extension Loaded Successfully:**
- [ ] Custom PiP icon visible in Chrome toolbar (not puzzle piece)
- [ ] No red errors in `chrome://extensions/`
- [ ] Extension popup opens when clicked

**Functionality Working:**
- [ ] PiP overlay buttons appear on videos
- [ ] `Alt+P` activates Picture-in-Picture
- [ ] `Alt+.` and `Alt+,` adjust opacity during PiP
- [ ] Extension popup shows correct video count

**Console Messages (F12 → Console):**
- [ ] "PiP Master: Content script initialized"
- [ ] "PiP Master: Video found" (when videos present)
- [ ] No JavaScript errors

---

## 🔧 **Quick Troubleshooting**

### **Extension Won't Load**
- Check all files are in correct locations
- Verify icon files exist: `icon16.png`, `icon48.png`, `icon128.png`
- Look for red error messages in `chrome://extensions/`

### **No PiP Overlay Buttons**
- Refresh the page after loading extension
- Check console for "Content script initialized" message
- Try on `test-page.html` first

### **Keyboard Shortcuts Don't Work**
- Go to `chrome://extensions/shortcuts`
- Verify shortcuts are assigned:
  - Toggle PiP: `Alt+P`
  - Increase opacity: `Alt+.`
  - Decrease opacity: `Alt+,`
- Check for conflicts with other extensions

### **PiP Doesn't Activate**
- Right-click video → check for "Picture in picture" option
- Some sites (Netflix, Disney+) may block PiP
- Try on YouTube or the test page first

---

## 🛠️ **Diagnostic Tools**

### **Automatic Diagnosis**
Open `diagnostic-tool.html` in Chrome and click "Run All Tests" for automated troubleshooting.

### **Manual Console Checks**
Open DevTools (F12) and run these commands:

**Check Extension Status:**
```javascript
// Should show extension info
window.pipMasterDiagnostics
```

**Check Video Detection:**
```javascript
// Should return number > 0 if videos present
document.querySelectorAll('video').length
```

**Test PiP API:**
```javascript
// Should return true if PiP is supported
'pictureInPictureEnabled' in document
```

**Check Settings:**
```javascript
// Should show extension settings
chrome.storage.sync.get('pipMasterSettings', console.log)
```

---

## 📱 **Supported Platforms**

### **✅ Fully Supported**
- YouTube (all variants)
- Local video files
- Most HTML5 video sites
- Test page included with extension

### **⚠️ Limited Support**
- Netflix (some content blocked)
- Amazon Prime Video (some content blocked)
- Hulu (some content blocked)

### **❌ Not Supported**
- Sites with strict DRM policies
- Flash-based video players
- Some embedded video players

---

## 🎯 **Expected Behavior**

### **When Working Correctly:**
1. **Video Detection**: Overlay buttons appear on videos when playing
2. **PiP Activation**: `Alt+P` or clicking overlay activates PiP smoothly
3. **Opacity Control**: `Alt+.` and `Alt+,` adjust transparency during PiP
4. **Settings Persistence**: Changes in popup/options save automatically
5. **Cross-Site Compatibility**: Works on multiple video platforms

### **Visual Indicators:**
- Custom blue gradient PiP icon in toolbar
- Semi-transparent overlay button on videos (usually top-right)
- Smooth PiP window with proper video content
- Settings popup shows current video count

---

## 📞 **Getting Help**

### **If Issues Persist:**
1. **Check Chrome Version**: Ensure Chrome 88+ (`chrome://version/`)
2. **Run Diagnostics**: Use `diagnostic-tool.html` for automated checks
3. **Review Logs**: Check browser console for error messages
4. **Fresh Install**: Delete and reinstall extension if needed

### **Common Solutions:**
- **Refresh pages** after loading/updating extension
- **Disable other extensions** temporarily to check for conflicts
- **Clear browser cache** if settings aren't saving
- **Test in incognito mode** to isolate issues

### **Documentation:**
- `TROUBLESHOOTING.md` - Comprehensive problem-solving guide
- `README.md` - Full feature documentation
- `INSTALLATION.md` - Detailed installation instructions

---

## 🎉 **Success!**

Once everything is working:
- Enjoy Picture-in-Picture on any video site
- Customize settings through the extension popup
- Use keyboard shortcuts for quick access
- Share the extension with others who need PiP functionality

**Pro Tip:** The extension works best on sites that fully support the Picture-in-Picture Web API. YouTube is an excellent testing ground for all features!
