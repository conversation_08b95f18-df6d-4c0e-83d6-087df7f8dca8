<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PiP Master</title>
    <link rel="stylesheet" href="popup.css" />
  </head>
  <body>
    <div class="popup-container">
      <!-- Header -->
      <header class="popup-header">
        <div class="logo">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M19 7h-8v6h8V7zm2-4H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h18v14z"
            />
          </svg>
          <h1>PiP Master</h1>
        </div>
        <div class="version">v1.0.0</div>
      </header>

      <!-- Main Content -->
      <main class="popup-main">
        <!-- Quick Actions -->
        <section class="quick-actions">
          <button
            id="togglePipBtn"
            class="action-btn primary"
            title="Toggle Picture-in-Picture (Alt+P)"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M19 7h-8v6h8V7zm2-4H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h18v14z"
              />
            </svg>
            <span>Toggle PiP</span>
          </button>
        </section>

        <!-- Status -->
        <section class="status-section">
          <div class="status-item">
            <span class="status-label">Status:</span>
            <span id="pipStatus" class="status-value">Ready</span>
          </div>
          <div class="status-item">
            <span class="status-label">Videos found:</span>
            <span id="videoCount" class="status-value">0</span>
          </div>
        </section>

        <!-- Quick Settings -->
        <section class="quick-settings">
          <h3>Quick Settings</h3>

          <div class="setting-item">
            <label class="setting-label">
              <input type="checkbox" id="enabledToggle" checked />
              <span class="checkmark"></span>
              Enable PiP Master
            </label>
          </div>

          <div class="setting-item">
            <label class="setting-label">
              <input type="checkbox" id="showOverlayToggle" checked />
              <span class="checkmark"></span>
              Show overlay buttons
            </label>
          </div>

          <div class="setting-item">
            <label class="setting-label">
              <input type="checkbox" id="snapToCornersToggle" checked />
              <span class="checkmark"></span>
              Snap to corners
            </label>
          </div>

          <div class="setting-item">
            <label class="setting-label">
              <input type="checkbox" id="autoActivateToggle" />
              <span class="checkmark"></span>
              Auto-activate for long videos
            </label>
          </div>

          <div class="setting-item">
            <label for="opacitySlider" class="setting-label-text"
              >Opacity</label
            >
            <div class="slider-container">
              <input
                type="range"
                id="opacitySlider"
                min="0.1"
                max="1"
                step="0.1"
                value="0.9"
              />
              <span id="opacityValue" class="slider-value">90%</span>
            </div>
          </div>

          <div class="setting-item">
            <label for="overlayPosition" class="setting-label-text"
              >Overlay Position</label
            >
            <select id="overlayPosition" class="setting-select">
              <option value="top-left">Top Left</option>
              <option value="top-right" selected>Top Right</option>
              <option value="bottom-left">Bottom Left</option>
              <option value="bottom-right">Bottom Right</option>
            </select>
          </div>
        </section>

        <!-- Keyboard Shortcuts -->
        <section class="shortcuts-section">
          <h3>Keyboard Shortcuts</h3>
          <div class="shortcut-item">
            <span class="shortcut-key">Alt + P</span>
            <span class="shortcut-desc">Toggle Picture-in-Picture</span>
          </div>
          <div class="shortcut-item">
            <span class="shortcut-key">Alt + .</span>
            <span class="shortcut-desc">Increase opacity</span>
          </div>
          <div class="shortcut-item">
            <span class="shortcut-key">Alt + ,</span>
            <span class="shortcut-desc">Decrease opacity</span>
          </div>
        </section>
      </main>

      <!-- Footer -->
      <footer class="popup-footer">
        <button id="settingsBtn" class="footer-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"
            />
          </svg>
          Settings
        </button>
        <button id="helpBtn" class="footer-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M13,19h-2v-2h2V19z M15.07,11.25l-0.9,0.92 C13.45,12.9,13,13.5,13,15h-2v-0.5c0-1.1,0.45-2.1,1.17-2.83l1.24-1.26c0.37-0.36,0.59-0.86,0.59-1.41c0-1.1-0.9-2-2-2 s-2,0.9-2,2H8c0-2.21,1.79-4,4-4s4,1.79,4,4C16,9.89,15.64,10.67,15.07,11.25z"
            />
          </svg>
          Help
        </button>
      </footer>
    </div>

    <script src="popup.js"></script>
  </body>
</html>
