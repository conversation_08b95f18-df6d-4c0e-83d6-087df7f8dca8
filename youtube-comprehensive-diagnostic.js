// YouTube Comprehensive Diagnostic & Repair Script
// Systematic diagnosis of YouTube detection failures

console.log("🔬 YouTube Comprehensive Diagnostic & Repair Script");
console.log("==================================================");
console.log(
  "This script will systematically test each component of YouTube integration"
);

// Global diagnostic state
window.youtubeDiagnostic = {
  results: {},
  videos: [],
  errors: [],
  fixes: [],
};

// Test 1: Environment Validation
console.log("\n🌍 TEST 1: Environment Validation");
console.log("================================");

const env = {
  isYouTube: window.location.hostname.includes("youtube.com"),
  isVideoPage: window.location.pathname.includes("/watch"),
  url: window.location.href,
  userAgent: navigator.userAgent,
  pipSupported: "pictureInPictureEnabled" in document,
  pipEnabled: document.pictureInPictureEnabled,
};

Object.entries(env).forEach(([key, value]) => {
  console.log(`✓ ${key}:`, value);
});

window.youtubeDiagnostic.results.environment = env;

if (!env.isYouTube) {
  console.error("❌ CRITICAL: Not on YouTube! Navigate to youtube.com");
  return;
}

// Test 2: Extension Loading Verification
console.log("\n🔌 TEST 2: Extension Loading Verification");
console.log("========================================");

const extension = {
  contentLoaded: !!window.pipMasterContentLoaded,
  instanceAvailable: !!window.pipMasterInstance,
  debugAvailable: !!window.pipMasterDebug,
  diagnosticsAvailable: !!window.pipMasterDiagnostics,
  chromeAPIs: typeof chrome !== "undefined" && !!chrome.runtime,
};

Object.entries(extension).forEach(([key, value]) => {
  console.log(`✓ ${key}:`, value);
  if (!value) {
    console.error(`❌ FAILURE: ${key} is missing!`);
    window.youtubeDiagnostic.errors.push(`Extension loading: ${key} missing`);
  }
});

window.youtubeDiagnostic.results.extension = extension;

if (!extension.instanceAvailable) {
  console.error("❌ CRITICAL: Extension instance not available!");
  console.log("🔧 ATTEMPTING MANUAL EXTENSION INJECTION...");

  // Try to manually inject content script
  try {
    const script = document.createElement("script");
    script.src = chrome.runtime.getURL("content/content.js");
    document.head.appendChild(script);
    console.log("✅ Manual injection attempted - wait 3 seconds and retry");
  } catch (error) {
    console.error("❌ Manual injection failed:", error);
  }
  return;
}

// Test 3: Platform Detection Verification
console.log("\n🎯 TEST 3: Platform Detection Verification");
console.log("==========================================");

const platform = {
  detected: window.pipMasterInstance.platform,
  expected: "youtube",
  hostname: window.location.hostname,
  detectionMethod: "automatic",
};

console.log(`✓ Platform detected:`, platform.detected);
console.log(`✓ Expected platform:`, platform.expected);

if (platform.detected !== platform.expected) {
  console.error(`❌ FAILURE: Platform detection incorrect!`);
  console.log("🔧 APPLYING FIX: Forcing platform to YouTube...");
  window.pipMasterInstance.platform = "youtube";
  platform.detected = "youtube";
  platform.detectionMethod = "manual_override";
  window.youtubeDiagnostic.fixes.push("Platform forced to YouTube");
  console.log("✅ Platform corrected to YouTube");
}

window.youtubeDiagnostic.results.platform = platform;

// Test 4: YouTube Video Element Detection
console.log("\n📹 TEST 4: YouTube Video Element Detection");
console.log("==========================================");

const youtubeSelectors = [
  "#movie_player video",
  ".html5-video-player video",
  ".video-stream",
  "video.video-stream",
  "#player video",
  ".ytp-html5-video",
  "video[src*='googlevideo']",
  "video[src*='youtube']",
  ".ytp-html5-video-container video",
  "#ytd-player video",
  "ytd-player video",
  ".player-container video",
  "video", // Basic fallback
];

const selectorResults = {};
let allFoundVideos = [];

youtubeSelectors.forEach((selector, index) => {
  try {
    const videos = document.querySelectorAll(selector);
    const count = videos.length;
    selectorResults[selector] = {
      count: count,
      success: count > 0,
      videos: Array.from(videos),
    };

    console.log(
      `${count > 0 ? "✅" : "❌"} ${index + 1}. "${selector}": ${count} videos`
    );

    // Add unique videos to our collection
    videos.forEach((video) => {
      if (!allFoundVideos.includes(video)) {
        allFoundVideos.push(video);
      }
    });
  } catch (error) {
    selectorResults[selector] = {
      count: 0,
      success: false,
      error: error.message,
    };
    console.error(`❌ ${index + 1}. "${selector}": ERROR - ${error.message}`);
  }
});

console.log(
  `\n📊 SUMMARY: Found ${allFoundVideos.length} unique videos across all selectors`
);
window.youtubeDiagnostic.results.videoDetection = {
  selectors: selectorResults,
  totalVideos: allFoundVideos.length,
  videos: allFoundVideos,
};
window.youtubeDiagnostic.videos = allFoundVideos;

if (allFoundVideos.length === 0) {
  console.error("❌ CRITICAL: No videos found with any selector!");
  console.log("🔍 DEBUGGING: Checking YouTube page state...");

  // Check YouTube player containers
  const moviePlayer = document.querySelector("#movie_player");
  const ytdPlayer = document.querySelector("ytd-player");
  const htmlPlayer = document.querySelector(".html5-video-player");

  console.log("YouTube containers:");
  console.log("  #movie_player:", !!moviePlayer);
  console.log("  ytd-player:", !!ytdPlayer);
  console.log("  .html5-video-player:", !!htmlPlayer);

  if (moviePlayer) {
    console.log("  #movie_player classes:", moviePlayer.className);
    const playerVideos = moviePlayer.querySelectorAll("*");
    console.log("  Elements in #movie_player:", playerVideos.length);
  }

  window.youtubeDiagnostic.errors.push("No videos found with any selector");
  return;
}

// Test 5: Video Analysis & Suitability Check
console.log("\n🔍 TEST 5: Video Analysis & Suitability Check");
console.log("==============================================");

allFoundVideos.forEach((video, index) => {
  console.log(`\n📹 VIDEO ${index + 1} ANALYSIS:`);
  console.log("  Element:", video.tagName);
  console.log("  Source:", video.src || video.currentSrc || "no src");
  console.log("  Ready state:", video.readyState);
  console.log(
    "  Dimensions:",
    `${video.videoWidth || "?"}x${video.videoHeight || "?"}`
  );
  console.log("  Duration:", video.duration || "unknown");
  console.log("  Paused:", video.paused);
  console.log("  PiP disabled:", video.disablePictureInPicture);

  // Container analysis
  const moviePlayer = video.closest("#movie_player");
  const htmlPlayer = video.closest(".html5-video-player");
  const adContainer = video.closest('.ad-showing, .video-ads, [class*="ad-"]');

  console.log("  In #movie_player:", !!moviePlayer);
  console.log("  In .html5-video-player:", !!htmlPlayer);
  console.log("  In ad container:", !!adContainer);

  // Suitability checks
  if (window.pipMasterInstance) {
    try {
      const suitable = window.pipMasterInstance.isVideoSuitableForPiP(video);
      const platformCheck =
        window.pipMasterInstance.platformSpecificVideoCheck(video);
      const isAd = window.pipMasterInstance.isLikelyVideoAd(video);
      const tracked = window.pipMasterInstance.videos.has(video);

      console.log("  Suitable for PiP:", suitable);
      console.log("  Passes platform check:", platformCheck);
      console.log("  Detected as ad:", isAd);
      console.log("  Already tracked:", tracked);

      if (!suitable) {
        console.warn("  ⚠️ FAILED suitability check");
        window.youtubeDiagnostic.errors.push(
          `Video ${index + 1} failed suitability check`
        );
      }
      if (!platformCheck) {
        console.warn("  ⚠️ FAILED platform check (likely ad)");
      }
      if (isAd) {
        console.warn("  ⚠️ DETECTED as advertisement");
      }
    } catch (error) {
      console.error("  ❌ ERROR during suitability check:", error);
      window.youtubeDiagnostic.errors.push(
        `Video ${index + 1} suitability check error: ${error.message}`
      );
    }
  }
});

// Test 6: Extension Method Execution Test
console.log("\n⚙️ TEST 6: Extension Method Execution Test");
console.log("==========================================");

const methodTests = {};

// Test setupYouTubeDetection
try {
  console.log("🔧 Testing setupYouTubeDetection...");
  if (window.pipMasterInstance.setupYouTubeDetection) {
    window.pipMasterInstance.setupYouTubeDetection();
    methodTests.setupYouTubeDetection = { success: true };
    console.log("✅ setupYouTubeDetection executed successfully");
  } else {
    methodTests.setupYouTubeDetection = {
      success: false,
      error: "Method not found",
    };
    console.error("❌ setupYouTubeDetection method not found");
  }
} catch (error) {
  methodTests.setupYouTubeDetection = { success: false, error: error.message };
  console.error("❌ setupYouTubeDetection failed:", error);
}

// Test performUniversalVideoScan
try {
  console.log("🔧 Testing performUniversalVideoScan...");
  const beforeCount = window.pipMasterInstance.videos.size;
  window.pipMasterInstance.performUniversalVideoScan();
  const afterCount = window.pipMasterInstance.videos.size;

  methodTests.performUniversalVideoScan = {
    success: true,
    beforeCount: beforeCount,
    afterCount: afterCount,
    videosAdded: afterCount - beforeCount,
  };
  console.log(
    `✅ performUniversalVideoScan executed - videos: ${beforeCount} → ${afterCount}`
  );
} catch (error) {
  methodTests.performUniversalVideoScan = {
    success: false,
    error: error.message,
  };
  console.error("❌ performUniversalVideoScan failed:", error);
}

window.youtubeDiagnostic.results.methodTests = methodTests;

// Test 7: Manual Video Processing Override
console.log("\n🔨 TEST 7: Manual Video Processing Override");
console.log("===========================================");

if (allFoundVideos.length > 0) {
  console.log("🔧 Attempting manual video processing...");

  allFoundVideos.forEach((video, index) => {
    try {
      console.log(`Processing video ${index + 1} manually...`);

      // Force add to videos set if not already tracked
      if (!window.pipMasterInstance.videos.has(video)) {
        window.pipMasterInstance.videos.add(video);
        console.log(`  ✅ Added video ${index + 1} to tracking`);
      }

      // Force overlay creation
      if (!window.pipMasterInstance.overlays.has(video)) {
        window.pipMasterInstance.createOverlay(video);
        console.log(`  ✅ Created overlay for video ${index + 1}`);
      }
    } catch (error) {
      console.error(`  ❌ Failed to process video ${index + 1}:`, error);
      window.youtubeDiagnostic.errors.push(
        `Manual processing failed for video ${index + 1}: ${error.message}`
      );
    }
  });

  window.youtubeDiagnostic.fixes.push("Manual video processing applied");
}

// Test 8: Overlay Detection & Validation
console.log("\n👁️ TEST 8: Overlay Detection & Validation");
console.log("==========================================");

setTimeout(() => {
  const overlays = document.querySelectorAll(".pip-master-overlay");
  console.log(`📊 Found ${overlays.length} overlays in DOM`);

  const overlayAnalysis = {
    count: overlays.length,
    overlays: [],
  };

  overlays.forEach((overlay, index) => {
    const style = getComputedStyle(overlay);
    const analysis = {
      index: index + 1,
      display: style.display,
      visibility: style.visibility,
      opacity: style.opacity,
      position: style.position,
      zIndex: style.zIndex,
      top: style.top,
      right: style.right,
      platform: overlay.getAttribute("data-platform"),
    };

    overlayAnalysis.overlays.push(analysis);

    console.log(`Overlay ${index + 1}:`, analysis);

    if (style.display === "none") {
      console.warn(`  ⚠️ Overlay ${index + 1} is hidden (display: none)`);
    }
    if (style.visibility === "hidden") {
      console.warn(`  ⚠️ Overlay ${index + 1} is hidden (visibility: hidden)`);
    }
    if (parseFloat(style.opacity) < 0.1) {
      console.warn(`  ⚠️ Overlay ${index + 1} is nearly transparent`);
    }
  });

  window.youtubeDiagnostic.results.overlays = overlayAnalysis;

  // Test 9: Event Listener Verification
  console.log("\n📡 TEST 9: Event Listener Verification");
  console.log("======================================");

  // Test YouTube navigation events
  const eventTests = {
    "yt-navigate-finish": false,
    "yt-player-updated": false,
    "yt-page-data-updated": false,
  };

  // Temporarily add test listeners
  Object.keys(eventTests).forEach((eventType) => {
    const testHandler = () => {
      eventTests[eventType] = true;
      console.log(`✅ ${eventType} event detected`);
    };

    document.addEventListener(eventType, testHandler, { once: true });

    // Remove after 5 seconds
    setTimeout(() => {
      document.removeEventListener(eventType, testHandler);
    }, 5000);
  });

  console.log("🔧 Event listeners registered for testing...");
  console.log("Navigate to another YouTube video to test navigation events");

  window.youtubeDiagnostic.results.eventTests = eventTests;

  // Test 10: Final Diagnostic Summary & Repair
  console.log("\n📋 TEST 10: Final Diagnostic Summary & Repair");
  console.log("==============================================");

  setTimeout(() => {
    const finalState = {
      videosFound: allFoundVideos.length,
      videosTracked: window.pipMasterInstance?.videos.size || 0,
      overlaysCreated: document.querySelectorAll(".pip-master-overlay").length,
      errorsFound: window.youtubeDiagnostic.errors.length,
      fixesApplied: window.youtubeDiagnostic.fixes.length,
    };

    console.log("\n🎯 FINAL DIAGNOSTIC RESULTS:");
    console.log("============================");
    Object.entries(finalState).forEach(([key, value]) => {
      console.log(`✓ ${key}:`, value);
    });

    console.log("\n🚨 ERRORS FOUND:");
    window.youtubeDiagnostic.errors.forEach((error, index) => {
      console.error(`${index + 1}. ${error}`);
    });

    console.log("\n🔧 FIXES APPLIED:");
    window.youtubeDiagnostic.fixes.forEach((fix, index) => {
      console.log(`${index + 1}. ${fix}`);
    });

    // Determine success status
    const isWorking =
      finalState.videosFound > 0 &&
      finalState.videosTracked > 0 &&
      finalState.overlaysCreated > 0;

    if (isWorking) {
      console.log("\n🎉 SUCCESS: YouTube integration appears to be working!");
      console.log("✅ Videos detected, tracked, and overlays created");
      console.log("👀 Look for overlay buttons on YouTube videos");
      console.log("⌨️ Try Alt+P keyboard shortcut");
    } else {
      console.log("\n❌ FAILURE: YouTube integration still not working");
      console.log("\n🔧 EMERGENCY REPAIR COMMANDS:");
      console.log("Copy and paste these commands one by one:");
      console.log("");
      console.log("// 1. Force platform and setup");
      console.log("window.pipMasterInstance.platform = 'youtube';");
      console.log("window.pipMasterInstance.setupYouTubeDetection();");
      console.log("");
      console.log("// 2. Force video processing");
      console.log(
        "const video = document.querySelector('#movie_player video');"
      );
      console.log("if (video) {");
      console.log("  window.pipMasterInstance.videos.add(video);");
      console.log("  window.pipMasterInstance.createOverlay(video);");
      console.log("  console.log('Manual override applied');");
      console.log("}");
      console.log("");
      console.log("// 3. Test PiP directly");
      console.log("const testVideo = document.querySelector('video');");
      console.log("if (testVideo) testVideo.requestPictureInPicture();");
    }

    window.youtubeDiagnostic.results.finalState = finalState;
    window.youtubeDiagnostic.results.isWorking = isWorking;

    console.log(
      "\n📊 Complete diagnostic data saved to: window.youtubeDiagnostic"
    );
  }, 2000);
}, 1000);
