// Settings Fix Verification Script
// Run this in the browser console to verify all settings functionality

console.log("🔧 PiP Master Settings Fix Verification v2.0");
console.log("==============================================");

class SettingsVerifier {
  constructor() {
    this.testResults = [];
    this.init();
  }

  async init() {
    console.log("🔍 Starting comprehensive settings verification...");
    
    await this.testStorageAccess();
    await this.testDefaultSettings();
    await this.testSettingsOperations();
    await this.testCommunication();
    await this.testSynchronization();
    
    this.displayResults();
  }

  addResult(test, passed, details = '') {
    this.testResults.push({ test, passed, details });
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${test}${details ? ': ' + details : ''}`);
  }

  async testStorageAccess() {
    console.log("\n📦 Testing Storage Access...");
    
    // Test chrome.storage.sync
    try {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
        await chrome.storage.sync.set({ testKey: 'testValue' });
        const result = await chrome.storage.sync.get('testKey');
        if (result.testKey === 'testValue') {
          this.addResult('chrome.storage.sync access', true);
          await chrome.storage.sync.remove('testKey');
        } else {
          this.addResult('chrome.storage.sync access', false, 'Value mismatch');
        }
      } else {
        this.addResult('chrome.storage.sync access', false, 'API not available');
      }
    } catch (error) {
      this.addResult('chrome.storage.sync access', false, error.message);
    }

    // Test chrome.storage.local
    try {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        await chrome.storage.local.set({ testKey: 'testValue' });
        const result = await chrome.storage.local.get('testKey');
        if (result.testKey === 'testValue') {
          this.addResult('chrome.storage.local access', true);
          await chrome.storage.local.remove('testKey');
        } else {
          this.addResult('chrome.storage.local access', false, 'Value mismatch');
        }
      } else {
        this.addResult('chrome.storage.local access', false, 'API not available');
      }
    } catch (error) {
      this.addResult('chrome.storage.local access', false, error.message);
    }

    // Test localStorage
    try {
      localStorage.setItem('testKey', 'testValue');
      const value = localStorage.getItem('testKey');
      if (value === 'testValue') {
        this.addResult('localStorage access', true);
        localStorage.removeItem('testKey');
      } else {
        this.addResult('localStorage access', false, 'Value mismatch');
      }
    } catch (error) {
      this.addResult('localStorage access', false, error.message);
    }
  }

  async testDefaultSettings() {
    console.log("\n⚙️ Testing Default Settings...");
    
    try {
      // Check if settings manager is available
      if (window.settingsManager) {
        this.addResult('Settings Manager loaded', true);
        
        // Test default settings structure
        const defaults = window.settingsManager.defaultSettings;
        const requiredKeys = ['enabled', 'showOverlay', 'opacity', 'overlayPosition', 'theme'];
        
        let allKeysPresent = true;
        for (const key of requiredKeys) {
          if (!(key in defaults)) {
            allKeysPresent = false;
            break;
          }
        }
        
        this.addResult('Default settings structure', allKeysPresent, 
          allKeysPresent ? `${Object.keys(defaults).length} keys` : 'Missing required keys');
        
      } else {
        this.addResult('Settings Manager loaded', false, 'Not available');
      }
    } catch (error) {
      this.addResult('Default settings test', false, error.message);
    }
  }

  async testSettingsOperations() {
    console.log("\n🔄 Testing Settings Operations...");
    
    try {
      if (window.testSettings) {
        // Test get settings
        const settings = await window.testSettings.get();
        this.addResult('Get settings', true, `${Object.keys(settings).length} keys loaded`);
        
        // Test update setting
        const originalOpacity = settings.opacity;
        const testOpacity = 0.75;
        
        const updateResult = await window.testSettings.update('opacity', testOpacity);
        this.addResult('Update single setting', updateResult);
        
        // Verify update
        const updatedSettings = await window.testSettings.get();
        const updateVerified = updatedSettings.opacity === testOpacity;
        this.addResult('Setting update verification', updateVerified);
        
        // Restore original value
        await window.testSettings.update('opacity', originalOpacity);
        
        // Test export
        const exportData = await window.testSettings.export();
        this.addResult('Export settings', !!exportData, 
          exportData ? `Version ${exportData.version}` : 'Failed');
        
        // Test import (using exported data)
        if (exportData) {
          const importResult = await window.testSettings.import(exportData);
          this.addResult('Import settings', importResult);
        }
        
      } else {
        this.addResult('Settings operations', false, 'testSettings not available');
      }
    } catch (error) {
      this.addResult('Settings operations test', false, error.message);
    }
  }

  async testCommunication() {
    console.log("\n📡 Testing Communication...");
    
    // Test background script communication
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await chrome.runtime.sendMessage({ type: 'GET_SETTINGS' });
        this.addResult('Background communication', !!response, 
          response ? 'Response received' : 'No response');
      } else {
        this.addResult('Background communication', false, 'Chrome runtime not available');
      }
    } catch (error) {
      this.addResult('Background communication', false, error.message);
    }

    // Test content script communication
    try {
      if (typeof chrome !== 'undefined' && chrome.tabs) {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tab) {
          const response = await chrome.tabs.sendMessage(tab.id, { type: 'GET_VIDEO_COUNT' });
          this.addResult('Content script communication', !!response,
            response ? `${response.data?.count || 0} videos` : 'No response');
        } else {
          this.addResult('Content script communication', false, 'No active tab');
        }
      } else {
        this.addResult('Content script communication', false, 'Chrome tabs API not available');
      }
    } catch (error) {
      this.addResult('Content script communication', false, error.message);
    }
  }

  async testSynchronization() {
    console.log("\n🔄 Testing Synchronization...");
    
    try {
      if (window.testSettings && typeof chrome !== 'undefined' && chrome.storage) {
        // Test sync between storage types
        const testValue = Math.random().toString(36).substring(7);
        
        // Save to sync storage
        await chrome.storage.sync.set({ syncTest: testValue });
        
        // Save to local storage
        await chrome.storage.local.set({ localTest: testValue });
        
        // Verify both storages
        const syncResult = await chrome.storage.sync.get('syncTest');
        const localResult = await chrome.storage.local.get('localTest');
        
        const syncWorking = syncResult.syncTest === testValue;
        const localWorking = localResult.localTest === testValue;
        
        this.addResult('Sync storage synchronization', syncWorking);
        this.addResult('Local storage synchronization', localWorking);
        
        // Clean up
        await chrome.storage.sync.remove('syncTest');
        await chrome.storage.local.remove('localTest');
        
        // Test settings synchronization across components
        const originalSettings = await window.testSettings.get();
        const testSettings = { ...originalSettings, testSync: Date.now() };
        
        // Update settings
        if (window.settingsManager) {
          await window.settingsManager.saveSettings(testSettings);
          
          // Wait a moment for sync
          await new Promise(resolve => setTimeout(resolve, 100));
          
          // Verify sync
          const syncedSettings = await window.testSettings.get();
          const syncVerified = syncedSettings.testSync === testSettings.testSync;
          
          this.addResult('Settings component synchronization', syncVerified);
          
          // Clean up
          delete testSettings.testSync;
          await window.settingsManager.saveSettings(testSettings);
        }
        
      } else {
        this.addResult('Synchronization test', false, 'Required APIs not available');
      }
    } catch (error) {
      this.addResult('Synchronization test', false, error.message);
    }
  }

  displayResults() {
    console.log("\n📊 Verification Results Summary");
    console.log("================================");
    
    const passed = this.testResults.filter(r => r.passed).length;
    const total = this.testResults.length;
    const percentage = Math.round((passed / total) * 100);
    
    console.log(`Overall Score: ${passed}/${total} (${percentage}%)`);
    
    if (percentage >= 90) {
      console.log("🎉 Excellent! Settings system is working correctly.");
    } else if (percentage >= 70) {
      console.log("✅ Good! Settings system is mostly working with minor issues.");
    } else if (percentage >= 50) {
      console.log("⚠️ Fair! Settings system has some issues that need attention.");
    } else {
      console.log("❌ Poor! Settings system has significant issues.");
    }
    
    console.log("\nFailed Tests:");
    const failedTests = this.testResults.filter(r => !r.passed);
    if (failedTests.length === 0) {
      console.log("None! All tests passed.");
    } else {
      failedTests.forEach(test => {
        console.log(`❌ ${test.test}: ${test.details}`);
      });
    }
    
    console.log("\nRecommendations:");
    if (failedTests.some(t => t.test.includes('storage'))) {
      console.log("- Check browser permissions for storage access");
      console.log("- Verify extension is properly loaded");
    }
    if (failedTests.some(t => t.test.includes('communication'))) {
      console.log("- Ensure extension is running in proper context");
      console.log("- Check for content script injection issues");
    }
    if (failedTests.some(t => t.test.includes('synchronization'))) {
      console.log("- Verify storage quota limits");
      console.log("- Check for network connectivity issues");
    }
    
    console.log("\n🔧 Manual Testing Commands:");
    console.log("window.testSettings.diagnose() - Detailed diagnosis");
    console.log("window.testSettings.get() - Get current settings");
    console.log("window.testSettings.reset() - Reset to defaults");
    console.log("window.testSettings.export() - Export settings");
  }
}

// Auto-run verification if settings fix is loaded
if (window.settingsManager) {
  console.log("✅ Settings fix detected, running verification...");
  window.settingsVerifier = new SettingsVerifier();
} else {
  console.log("⚠️ Settings fix not detected. Load settings-comprehensive-fix.js first.");
  console.log("Then run: new SettingsVerifier()");
}

// Export for manual use
window.SettingsVerifier = SettingsVerifier;
