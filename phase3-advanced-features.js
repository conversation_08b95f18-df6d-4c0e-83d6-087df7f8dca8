// Phase 3: Advanced Features (Themes, Performance, Accessibility, YouTube Integration)
// Completes the v2.0.0 upgrade with advanced features and optimizations

console.log("🚀 Phase 3: Advanced Features");
console.log("=============================");

// Phase 3 Advanced Features Manager
class Phase3AdvancedFeatures {
  constructor() {
    this.advancedStatus = {
      prerequisiteCheck: false,
      themeSystem: false,
      performanceOptimization: false,
      accessibilityFeatures: false,
      youtubeIntegration: false,
      finalVerification: false
    };
    this.executePhase3();
  }

  executePhase3() {
    console.log("🔧 Starting Phase 3 advanced features...");
    
    this.checkPrerequisites();
    this.integrateThemeSystem();
    this.integratePerformanceOptimization();
    this.integrateAccessibilityFeatures();
    this.integrateYouTubeCompatibility();
    this.finalizeUpgrade();
    this.generatePhase3Report();
  }

  checkPrerequisites() {
    console.log("\n1️⃣ Checking Phase 2 prerequisites");
    console.log("==================================");
    
    const requirements = {
      phase1Complete: !!window.pipMasterInstance?.settingsPanel,
      phase2Complete: !!window.pipMasterInstance?.timelineControl,
      audioControl: !!window.pipMasterInstance?.settingsPanel?.applyAudioControlSettings,
      smartAutoPiP: !!window.pipMasterInstance?.smartAutoPiP
    };
    
    console.log("Prerequisites check:");
    Object.entries(requirements).forEach(([req, met]) => {
      console.log(`${met ? '✅' : '❌'} ${req}: ${met ? 'Available' : 'Missing'}`);
    });
    
    const allMet = Object.values(requirements).every(Boolean);
    this.advancedStatus.prerequisiteCheck = allMet;
    
    if (allMet) {
      console.log("🎯 Prerequisites PASSED - Phase 3 can proceed");
    } else {
      console.error("❌ Prerequisites FAILED - Complete Phase 1 & 2 first");
      return;
    }
  }

  integrateThemeSystem() {
    console.log("\n2️⃣ Integrating Advanced Theme System");
    console.log("====================================");
    
    try {
      // Create theme manager
      window.pipMasterInstance.themeManager = this.createThemeManager();
      console.log("✅ Created advanced theme manager");
      
      // Add theme application methods to settings panel
      const settingsPanel = window.pipMasterInstance.settingsPanel;
      
      settingsPanel.applyThemeSettings = function() {
        const theme = this.settings.overlayTheme || 'default';
        this.pipMaster.themeManager.setTheme(theme);
        console.log(`🎨 Applied theme: ${theme}`);
      };
      
      // Apply current theme
      settingsPanel.applyThemeSettings();
      
      this.advancedStatus.themeSystem = true;
      console.log("🎯 Theme System integration COMPLETE");
      
    } catch (error) {
      console.error("❌ Theme System integration failed:", error);
      this.advancedStatus.themeSystem = false;
    }
  }

  createThemeManager() {
    return {
      currentTheme: 'default',
      themes: {
        default: {
          background: 'rgba(0, 0, 0, 0.8)',
          color: '#ffffff',
          border: '2px solid rgba(255, 255, 255, 0.3)',
          borderRadius: '8px',
          backdropFilter: 'blur(10px)'
        },
        minimal: {
          background: 'rgba(255, 255, 255, 0.95)',
          color: '#333333',
          border: '1px solid rgba(0, 0, 0, 0.1)',
          borderRadius: '4px',
          backdropFilter: 'none'
        },
        neon: {
          background: 'rgba(0, 20, 40, 0.9)',
          color: '#00ffff',
          border: '2px solid #00ffff',
          borderRadius: '12px',
          backdropFilter: 'blur(15px)',
          boxShadow: '0 0 20px rgba(0, 255, 255, 0.5)'
        },
        dark: {
          background: 'rgba(20, 20, 20, 0.95)',
          color: '#ffffff',
          border: '2px solid #444444',
          borderRadius: '10px',
          backdropFilter: 'blur(20px)'
        },
        youtube: {
          background: 'rgba(255, 0, 0, 0.9)',
          color: '#ffffff',
          border: '2px solid #ff0000',
          borderRadius: '6px',
          backdropFilter: 'blur(8px)'
        }
      },
      
      setTheme: function(themeName) {
        if (!this.themes[themeName]) {
          console.warn(`Theme '${themeName}' not found, using default`);
          themeName = 'default';
        }
        
        this.currentTheme = themeName;
        this.applyThemeToOverlays(this.themes[themeName]);
        console.log(`🎨 Theme changed to: ${themeName}`);
      },
      
      applyThemeToOverlays: function(themeStyles) {
        const overlays = document.querySelectorAll('.pip-master-overlay');
        overlays.forEach(overlay => {
          const container = overlay.querySelector('.pip-master-container');
          if (container) {
            Object.assign(container.style, themeStyles);
          }
        });
        
        // Apply to timeline control
        const timeline = document.getElementById('pip-timeline-control');
        if (timeline) {
          const timelineContainer = timeline.firstElementChild;
          if (timelineContainer) {
            Object.assign(timelineContainer.style, {
              background: themeStyles.background,
              color: themeStyles.color,
              border: themeStyles.border
            });
          }
        }
      },
      
      getAvailableThemes: function() {
        return Object.keys(this.themes);
      }
    };
  }

  integratePerformanceOptimization() {
    console.log("\n3️⃣ Integrating Performance Optimization");
    console.log("=======================================");
    
    try {
      // Create performance optimizer
      window.pipMasterInstance.performanceOptimizer = this.createPerformanceOptimizer();
      console.log("✅ Created performance optimizer");
      
      // Add performance methods to settings panel
      const settingsPanel = window.pipMasterInstance.settingsPanel;
      
      settingsPanel.applyPerformanceSettings = function() {
        const optimizer = this.pipMaster.performanceOptimizer;
        
        if (this.settings.lowPowerMode) {
          optimizer.enableLowPowerMode();
        } else {
          optimizer.disableLowPowerMode();
        }
        
        const scanFreq = this.settings.scanFrequency || 'normal';
        optimizer.setScanFrequency(scanFreq);
        
        console.log(`⚡ Performance settings applied: lowPower=${this.settings.lowPowerMode}, scan=${scanFreq}`);
      };
      
      // Apply current performance settings
      settingsPanel.applyPerformanceSettings();
      
      this.advancedStatus.performanceOptimization = true;
      console.log("🎯 Performance Optimization integration COMPLETE");
      
    } catch (error) {
      console.error("❌ Performance Optimization integration failed:", error);
      this.advancedStatus.performanceOptimization = false;
    }
  }

  createPerformanceOptimizer() {
    return {
      lowPowerMode: false,
      scanFrequency: 'normal',
      scanInterval: null,
      
      enableLowPowerMode: function() {
        this.lowPowerMode = true;
        this.optimizeForBattery();
        console.log("🔋 Low power mode enabled");
      },
      
      disableLowPowerMode: function() {
        this.lowPowerMode = false;
        this.restoreNormalPerformance();
        console.log("⚡ Normal performance mode restored");
      },
      
      setScanFrequency: function(frequency) {
        this.scanFrequency = frequency;
        this.updateScanInterval();
        console.log(`📡 Scan frequency set to: ${frequency}`);
      },
      
      optimizeForBattery: function() {
        // Reduce scan frequency
        this.updateScanInterval('minimal');
        
        // Reduce animation frequency
        const overlays = document.querySelectorAll('.pip-master-overlay');
        overlays.forEach(overlay => {
          overlay.style.transition = 'opacity 0.1s ease';
        });
      },
      
      restoreNormalPerformance: function() {
        // Restore normal scan frequency
        this.updateScanInterval();
        
        // Restore normal animations
        const overlays = document.querySelectorAll('.pip-master-overlay');
        overlays.forEach(overlay => {
          overlay.style.transition = 'opacity 0.3s ease';
        });
      },
      
      updateScanInterval: function(overrideFreq) {
        const freq = overrideFreq || this.scanFrequency;
        const intervals = {
          minimal: 5000,   // 5 seconds
          normal: 2000,    // 2 seconds
          aggressive: 500  // 0.5 seconds
        };
        
        if (this.scanInterval) {
          clearInterval(this.scanInterval);
        }
        
        const interval = intervals[freq] || intervals.normal;
        this.scanInterval = setInterval(() => {
          if (window.pipMasterInstance?.performUniversalVideoScan) {
            window.pipMasterInstance.performUniversalVideoScan();
          }
        }, interval);
      },
      
      getPerformanceStats: function() {
        return {
          lowPowerMode: this.lowPowerMode,
          scanFrequency: this.scanFrequency,
          memoryUsage: performance.memory ? {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB'
          } : 'Not available'
        };
      }
    };
  }

  integrateAccessibilityFeatures() {
    console.log("\n4️⃣ Integrating Accessibility Features");
    console.log("====================================");
    
    try {
      // Create accessibility manager
      window.pipMasterInstance.accessibility = this.createAccessibilityManager();
      console.log("✅ Created accessibility manager");
      
      // Add accessibility methods to settings panel
      const settingsPanel = window.pipMasterInstance.settingsPanel;
      
      settingsPanel.applyAccessibilitySettings = function() {
        const accessibility = this.pipMaster.accessibility;
        
        if (this.settings.highContrast) {
          accessibility.enableHighContrastMode();
        } else {
          accessibility.disableHighContrastMode();
        }
        
        if (this.settings.reducedMotion) {
          accessibility.enableReducedMotionMode();
        } else {
          accessibility.disableReducedMotionMode();
        }
        
        console.log(`♿ Accessibility settings applied: contrast=${this.settings.highContrast}, motion=${this.settings.reducedMotion}`);
      };
      
      // Apply current accessibility settings
      settingsPanel.applyAccessibilitySettings();
      
      this.advancedStatus.accessibilityFeatures = true;
      console.log("🎯 Accessibility Features integration COMPLETE");
      
    } catch (error) {
      console.error("❌ Accessibility Features integration failed:", error);
      this.advancedStatus.accessibilityFeatures = false;
    }
  }

  createAccessibilityManager() {
    return {
      highContrastEnabled: false,
      reducedMotionEnabled: false,
      
      enableHighContrastMode: function() {
        this.highContrastEnabled = true;
        this.applyHighContrastStyles();
        console.log("♿ High contrast mode enabled");
      },
      
      disableHighContrastMode: function() {
        this.highContrastEnabled = false;
        this.removeHighContrastStyles();
        console.log("♿ High contrast mode disabled");
      },
      
      enableReducedMotionMode: function() {
        this.reducedMotionEnabled = true;
        this.applyReducedMotionStyles();
        console.log("♿ Reduced motion mode enabled");
      },
      
      disableReducedMotionMode: function() {
        this.reducedMotionEnabled = false;
        this.removeReducedMotionStyles();
        console.log("♿ Reduced motion mode disabled");
      },
      
      applyHighContrastStyles: function() {
        let style = document.getElementById('pip-master-high-contrast');
        if (!style) {
          style = document.createElement('style');
          style.id = 'pip-master-high-contrast';
          document.head.appendChild(style);
        }
        
        style.textContent = `
          .pip-master-overlay .pip-master-container {
            background: #000000 !important;
            color: #ffffff !important;
            border: 3px solid #ffffff !important;
          }
          .pip-master-overlay button {
            background: #ffffff !important;
            color: #000000 !important;
            border: 2px solid #ffffff !important;
          }
          #pip-timeline-control > div {
            background: #000000 !important;
            color: #ffffff !important;
            border: 3px solid #ffffff !important;
          }
        `;
      },
      
      removeHighContrastStyles: function() {
        const style = document.getElementById('pip-master-high-contrast');
        if (style) {
          style.remove();
        }
      },
      
      applyReducedMotionStyles: function() {
        let style = document.getElementById('pip-master-reduced-motion');
        if (!style) {
          style = document.createElement('style');
          style.id = 'pip-master-reduced-motion';
          document.head.appendChild(style);
        }
        
        style.textContent = `
          .pip-master-overlay,
          .pip-master-overlay *,
          #pip-timeline-control,
          #pip-timeline-control * {
            animation: none !important;
            transition: none !important;
          }
        `;
      },
      
      removeReducedMotionStyles: function() {
        const style = document.getElementById('pip-master-reduced-motion');
        if (style) {
          style.remove();
        }
      }
    };
  }

  integrateYouTubeCompatibility() {
    console.log("\n5️⃣ Integrating YouTube Compatibility");
    console.log("===================================");
    
    try {
      // Ensure YouTube functionality works with enhanced features
      this.setupYouTubeIntegration();
      
      // Add universal video scanning
      this.setupUniversalVideoScanning();
      
      this.advancedStatus.youtubeIntegration = true;
      console.log("🎯 YouTube Compatibility integration COMPLETE");
      
    } catch (error) {
      console.error("❌ YouTube Compatibility integration failed:", error);
      this.advancedStatus.youtubeIntegration = false;
    }
  }

  setupYouTubeIntegration() {
    // Ensure PiP Master works on YouTube
    if (!window.pipMasterInstance.performUniversalVideoScan) {
      window.pipMasterInstance.performUniversalVideoScan = function() {
        const videos = document.querySelectorAll('video');
        
        videos.forEach(video => {
          if (!video.hasAttribute('data-pip-master-processed')) {
            this.processVideo(video);
            video.setAttribute('data-pip-master-processed', 'true');
          }
        });
        
        console.log(`📹 Scanned ${videos.length} videos`);
      };
    }
    
    if (!window.pipMasterInstance.processVideo) {
      window.pipMasterInstance.processVideo = function(video) {
        // Create overlay for video
        this.createOverlayForVideo(video);
      };
    }
    
    if (!window.pipMasterInstance.createOverlayForVideo) {
      window.pipMasterInstance.createOverlayForVideo = function(video) {
        // Remove existing overlay
        const existingOverlay = video.parentElement?.querySelector('.pip-master-overlay');
        if (existingOverlay) {
          existingOverlay.remove();
        }
        
        // Create new overlay
        const overlay = document.createElement('div');
        overlay.className = 'pip-master-overlay';
        overlay.innerHTML = `
          <div class="pip-master-container" style="
            position: absolute; top: 10px; right: 10px; z-index: 9999;
            background: rgba(0, 0, 0, 0.8); color: white; padding: 8px 12px;
            border-radius: 6px; font-family: Arial, sans-serif; font-size: 14px;
            cursor: pointer; transition: opacity 0.3s ease;
            backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.3);
          ">
            📺 PiP
          </div>
        `;
        
        // Add click handler
        overlay.addEventListener('click', () => {
          if (document.pictureInPictureElement) {
            document.exitPictureInPicture();
          } else {
            video.requestPictureInPicture();
          }
        });
        
        // Position overlay
        const videoRect = video.getBoundingClientRect();
        if (videoRect.width > 200 && videoRect.height > 100) {
          video.parentElement.style.position = 'relative';
          video.parentElement.appendChild(overlay);
        }
      };
    }
    
    console.log("✅ YouTube integration methods added");
  }

  setupUniversalVideoScanning() {
    // Start automatic video scanning
    if (window.pipMasterInstance.performanceOptimizer) {
      window.pipMasterInstance.performanceOptimizer.updateScanInterval();
    } else {
      // Fallback scanning
      setInterval(() => {
        window.pipMasterInstance.performUniversalVideoScan();
      }, 2000);
    }
    
    // Initial scan
    window.pipMasterInstance.performUniversalVideoScan();
    
    console.log("✅ Universal video scanning active");
  }

  finalizeUpgrade() {
    console.log("\n6️⃣ Finalizing v2.0.0 Upgrade");
    console.log("=============================");
    
    try {
      // Update settings panel with all features
      this.updateCompleteSettingsPanel();
      
      // Update version information
      this.updateVersionInfo();
      
      // Apply all settings
      this.applyAllSettings();
      
      this.advancedStatus.finalVerification = true;
      console.log("🎯 v2.0.0 Upgrade finalization COMPLETE");
      
    } catch (error) {
      console.error("❌ Upgrade finalization failed:", error);
      this.advancedStatus.finalVerification = false;
    }
  }

  updateCompleteSettingsPanel() {
    const settingsPanel = window.pipMasterInstance.settingsPanel;
    
    // Update applySettings to include all Phase 3 features
    const originalApplySettings = settingsPanel.applySettings;
    
    settingsPanel.applySettings = function() {
      // Apply Phase 1 & 2 settings
      if (originalApplySettings) {
        originalApplySettings.call(this);
      }
      
      // Apply Phase 3 settings
      this.applyThemeSettings();
      this.applyPerformanceSettings();
      this.applyAccessibilitySettings();
      
      console.log("✅ All v2.0.0 settings applied");
    };
    
    console.log("✅ Complete settings panel updated");
  }

  updateVersionInfo() {
    // Update extension version
    window.pipMasterInstance.version = "2.0.0";
    
    // Update settings panel footer
    const settingsPanel = window.pipMasterInstance.settingsPanel;
    const originalHTML = settingsPanel.generateEnhancedPanelHTML;
    
    settingsPanel.generateEnhancedPanelHTML = function() {
      return originalHTML.call(this).replace(
        'Enhanced PiP Master v2.0 • Press Alt+S to toggle • Phase 1 Complete',
        'PiP Master v2.0.0 Enhanced • Alt+S: Settings • Alt+T: Timeline • Alt+P: PiP • Alt+H: Help'
      );
    };
    
    console.log("✅ Version updated to v2.0.0");
  }

  applyAllSettings() {
    const settingsPanel = window.pipMasterInstance.settingsPanel;
    
    // Load settings from localStorage
    settingsPanel.loadSettings();
    
    // Apply all settings
    settingsPanel.applySettings();
    
    console.log("✅ All v2.0.0 settings loaded and applied");
  }

  generatePhase3Report() {
    console.log("\n📊 PHASE 3 & UPGRADE COMPLETION REPORT");
    console.log("======================================");
    
    const { prerequisiteCheck, themeSystem, performanceOptimization, accessibilityFeatures, youtubeIntegration, finalVerification } = this.advancedStatus;
    
    console.log("Phase 3 Status:");
    console.log(`✅ 3.1 Prerequisites: ${prerequisiteCheck ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ 3.2 Theme System: ${themeSystem ? 'INTEGRATED' : 'FAILED'}`);
    console.log(`✅ 3.3 Performance Optimization: ${performanceOptimization ? 'INTEGRATED' : 'FAILED'}`);
    console.log(`✅ 3.4 Accessibility Features: ${accessibilityFeatures ? 'INTEGRATED' : 'FAILED'}`);
    console.log(`✅ 3.5 YouTube Integration: ${youtubeIntegration ? 'INTEGRATED' : 'FAILED'}`);
    console.log(`✅ 3.6 Final Verification: ${finalVerification ? 'COMPLETE' : 'FAILED'}`);
    
    const completedTasks = Object.values(this.advancedStatus).filter(Boolean).length;
    const totalTasks = Object.keys(this.advancedStatus).length;
    const completionRate = Math.round((completedTasks / totalTasks) * 100);
    
    console.log(`\n🏥 Phase 3 Completion: ${completedTasks}/${totalTasks} (${completionRate}%)`);
    
    if (completionRate >= 80) {
      console.log("🎉 PHASE 3 SUCCESSFUL - v2.0.0 UPGRADE COMPLETE!");
      console.log("\n🚀 PiP Master v2.0.0 Enhanced Features:");
      console.log("✅ Smart Auto-PiP with tab switch detection");
      console.log("✅ Timeline controls with hover preview");
      console.log("✅ Audio control for PiP mode");
      console.log("✅ Advanced theme system (5 themes)");
      console.log("✅ Performance optimization");
      console.log("✅ Accessibility features");
      console.log("✅ YouTube compatibility");
      console.log("✅ Enhanced keyboard shortcuts");
      console.log("\n⌨️ Keyboard Shortcuts:");
      console.log("Alt+P: Toggle Picture-in-Picture");
      console.log("Alt+S: Open/close settings panel");
      console.log("Alt+T: Toggle timeline controls");
      console.log("Alt+H: Show help");
      console.log("\n🎮 Test Your Upgraded Extension:");
      console.log("1. Press Alt+S to open enhanced settings");
      console.log("2. Try different themes and features");
      console.log("3. Enable Smart Auto-PiP and switch tabs");
      console.log("4. Press Alt+T to test timeline controls");
    } else {
      console.log("⚠️ PHASE 3 INCOMPLETE - Some advanced features may not work");
    }
  }
}

// Phase 3 verification command
window.verifyPhase3 = function() {
  console.log("🔍 Phase 3 Quick Verification");
  console.log("=============================");
  
  const checks = {
    themeManager: !!window.pipMasterInstance.themeManager,
    performanceOptimizer: !!window.pipMasterInstance.performanceOptimizer,
    accessibility: !!window.pipMasterInstance.accessibility,
    version: window.pipMasterInstance.version === "2.0.0",
    allFeatures: false
  };
  
  // Check all features
  const features = ['settingsPanel', 'timelineControl', 'smartAutoPiP', 'themeManager', 'performanceOptimizer', 'accessibility'];
  checks.allFeatures = features.every(feature => !!window.pipMasterInstance[feature]);
  
  Object.entries(checks).forEach(([check, result]) => {
    console.log(`${result ? '✅' : '❌'} ${check}: ${result}`);
  });
  
  const score = Object.values(checks).filter(Boolean).length;
  console.log(`\n📊 Score: ${score}/5 (${score * 20}%)`);
  
  return score >= 4;
};

// Complete upgrade verification
window.verifyCompleteUpgrade = function() {
  console.log("🔍 Complete v1.0.0 → v2.0.0 Upgrade Verification");
  console.log("================================================");
  
  const phase1 = window.verifyPhase1 ? window.verifyPhase1() : false;
  const phase2 = window.verifyPhase2 ? window.verifyPhase2() : false;
  const phase3 = window.verifyPhase3();
  
  console.log(`Phase 1: ${phase1 ? '✅ COMPLETE' : '❌ INCOMPLETE'}`);
  console.log(`Phase 2: ${phase2 ? '✅ COMPLETE' : '❌ INCOMPLETE'}`);
  console.log(`Phase 3: ${phase3 ? '✅ COMPLETE' : '❌ INCOMPLETE'}`);
  
  const allComplete = phase1 && phase2 && phase3;
  
  if (allComplete) {
    console.log("\n🎉 UPGRADE SUCCESSFUL!");
    console.log("PiP Master v2.0.0 Enhanced is fully functional!");
  } else {
    console.log("\n⚠️ UPGRADE INCOMPLETE");
    console.log("Some phases need attention before the upgrade is complete.");
  }
  
  return allComplete;
};

// Auto-execute Phase 3 (only if Phase 2 is complete)
if (window.verifyPhase2 && window.verifyPhase2()) {
  console.log("🚀 Auto-executing Phase 3...");
  window.phase3Advanced = new Phase3AdvancedFeatures();
} else {
  console.log("⚠️ Phase 2 not complete - run Phase 2 first");
}

console.log("\n📋 Phase 3 Commands:");
console.log("====================");
console.log("verifyPhase3()                    - Quick verification");
console.log("verifyCompleteUpgrade()           - Complete upgrade verification");
console.log("new Phase3AdvancedFeatures()      - Manual Phase 3 execution");
