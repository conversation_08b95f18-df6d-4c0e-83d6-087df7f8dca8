<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube PiP Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #45a049;
        }
        
        .test-button.danger {
            background: #f44336;
        }
        
        .test-button.danger:hover {
            background: #da190b;
        }
        
        .log-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 10px;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .video-container {
            position: relative;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .mock-youtube-player {
            width: 100%;
            height: 400px;
            background: linear-gradient(45deg, #333, #666);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            position: relative;
        }
        
        .mock-video {
            width: 100%;
            height: 100%;
            background: #000;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 YouTube PiP Fix Test Suite</h1>
        <p>This page tests the YouTube Picture-in-Picture fixes and provides debugging tools.</p>
    </div>

    <div class="test-section">
        <h2>📊 Extension Status</h2>
        <div id="extension-status" class="status info">Checking extension status...</div>
        <button class="test-button" onclick="checkExtensionStatus()">Refresh Status</button>
    </div>

    <div class="test-section">
        <h2>🔧 Quick Tests</h2>
        <button class="test-button" onclick="testPiPSupport()">Test PiP Support</button>
        <button class="test-button" onclick="loadYouTubeFix()">Load YouTube Fix</button>
        <button class="test-button" onclick="simulateYouTubeVideo()">Simulate YouTube Video</button>
        <button class="test-button" onclick="testOverlayCreation()">Test Overlay Creation</button>
        <button class="test-button danger" onclick="clearLog()">Clear Log</button>
    </div>

    <div class="test-section">
        <h2>📺 Mock YouTube Player</h2>
        <div class="video-container">
            <div class="mock-youtube-player" id="mock-player">
                <video class="mock-video" id="test-video" controls>
                    <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>
        </div>
        <button class="test-button" onclick="testPiPOnMockVideo()">Test PiP on Mock Video</button>
    </div>

    <div class="test-section">
        <h2>📝 Debug Log</h2>
        <div id="log-output" class="log-output"></div>
    </div>

    <script>
        // Logging system
        const logOutput = document.getElementById('log-output');
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        function addToLog(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logOutput.textContent += logEntry;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        // Override console methods
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToLog(args.join(' '), 'warn');
        };

        // Test functions
        function checkExtensionStatus() {
            const statusDiv = document.getElementById('extension-status');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ Extension environment detected';
                console.log('Extension environment is available');
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Extension environment not detected (testing in regular browser)';
                console.log('Extension environment not available - testing in regular browser mode');
            }

            // Check for PiP Master instance
            if (window.pipMasterInstance) {
                console.log('✅ PiP Master instance found');
                console.log('Platform:', window.pipMasterInstance.platform);
                console.log('Videos tracked:', window.pipMasterInstance.videos.size);
            } else {
                console.log('❌ PiP Master instance not found');
            }

            // Check for YouTube fix
            if (window.youtubePiPFix) {
                console.log('✅ YouTube PiP Fix loaded');
            } else {
                console.log('❌ YouTube PiP Fix not loaded');
            }
        }

        function testPiPSupport() {
            console.log('Testing Picture-in-Picture support...');
            
            if ('pictureInPictureEnabled' in document) {
                console.log('✅ Picture-in-Picture API is supported');
                console.log('PiP enabled:', document.pictureInPictureEnabled);
            } else {
                console.log('❌ Picture-in-Picture API is not supported');
            }

            const testVideo = document.getElementById('test-video');
            if (testVideo) {
                console.log('Test video element found');
                console.log('Video PiP disabled:', testVideo.disablePictureInPicture);
                console.log('Video ready state:', testVideo.readyState);
                console.log('Video dimensions:', testVideo.videoWidth + 'x' + testVideo.videoHeight);
            }
        }

        function loadYouTubeFix() {
            console.log('Loading YouTube PiP Fix...');
            
            const script = document.createElement('script');
            script.src = 'youtube-pip-fix.js';
            script.onload = () => {
                console.log('✅ YouTube PiP Fix loaded successfully');
            };
            script.onerror = () => {
                console.log('❌ Failed to load YouTube PiP Fix');
            };
            document.head.appendChild(script);
        }

        function simulateYouTubeVideo() {
            console.log('Simulating YouTube video detection...');
            
            const mockPlayer = document.getElementById('mock-player');
            mockPlayer.id = 'movie_player';
            mockPlayer.className = 'html5-video-player';
            
            console.log('Mock YouTube player created with ID: movie_player');
            
            // Trigger video detection if YouTube fix is loaded
            if (window.youtubePiPFix) {
                window.youtubePiPFix.scanForVideos();
            } else {
                console.log('YouTube fix not loaded - load it first');
            }
        }

        function testOverlayCreation() {
            console.log('Testing overlay creation...');
            
            const testVideo = document.getElementById('test-video');
            if (!testVideo) {
                console.log('❌ Test video not found');
                return;
            }

            // Create a simple overlay
            const overlay = document.createElement('div');
            overlay.className = 'test-pip-overlay';
            overlay.innerHTML = `
                <div style="
                    position: absolute;
                    top: 16px;
                    right: 16px;
                    background: rgba(255, 0, 0, 0.8);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 6px;
                    cursor: pointer;
                    z-index: 10000;
                    font-size: 14px;
                ">📺 Test PiP</div>
            `;

            overlay.addEventListener('click', () => {
                console.log('Test overlay clicked');
                testPiPOnMockVideo();
            });

            const container = testVideo.parentElement;
            container.style.position = 'relative';
            container.appendChild(overlay);
            
            console.log('✅ Test overlay created and positioned');
        }

        async function testPiPOnMockVideo() {
            console.log('Testing PiP on mock video...');
            
            const testVideo = document.getElementById('test-video');
            if (!testVideo) {
                console.log('❌ Test video not found');
                return;
            }

            try {
                if (document.pictureInPictureElement) {
                    console.log('Exiting existing PiP...');
                    await document.exitPictureInPicture();
                } else {
                    console.log('Requesting PiP...');
                    await testVideo.requestPictureInPicture();
                    console.log('✅ PiP activated successfully!');
                }
            } catch (error) {
                console.error('❌ PiP test failed:', error.message);
            }
        }

        function clearLog() {
            logOutput.textContent = '';
            console.log('Log cleared');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎬 YouTube PiP Test Suite loaded');
            checkExtensionStatus();
        });
    </script>
</body>
</html>
