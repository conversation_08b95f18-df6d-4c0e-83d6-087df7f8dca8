// YouTube Suitability Test Script
// Tests the fixed suitability logic on YouTube videos

console.log("🧪 YouTube Suitability Test Script");
console.log("==================================");

// Function to test YouTube video suitability after fixes
window.testYouTubeSuitabilityFixes = function() {
  console.log("🎬 Testing YouTube Suitability Fixes");
  console.log("====================================");
  
  // Check environment
  const isYouTube = window.location.hostname.includes('youtube.com');
  const isVideoPage = window.location.pathname.includes('/watch');
  
  console.log(`On YouTube: ${isYouTube}`);
  console.log(`On video page: ${isVideoPage}`);
  
  if (!isYouTube) {
    console.warn("⚠️ Not on YouTube - navigate to YouTube for best results");
  }
  
  // Check extension
  if (!window.pipMasterInstance) {
    console.error("❌ Extension instance not available!");
    return false;
  }
  
  console.log(`Platform detected: ${window.pipMasterInstance.platform}`);
  
  // Find YouTube videos
  const youtubeSelectors = [
    "#movie_player video",
    ".html5-video-player video",
    ".video-stream",
    "video.video-stream",
    "video"
  ];
  
  let allVideos = [];
  youtubeSelectors.forEach(selector => {
    try {
      const videos = document.querySelectorAll(selector);
      console.log(`Selector "${selector}": ${videos.length} videos`);
      videos.forEach(video => {
        if (!allVideos.includes(video)) {
          allVideos.push(video);
        }
      });
    } catch (error) {
      console.warn(`Selector "${selector}" failed:`, error);
    }
  });
  
  console.log(`\nTotal unique videos found: ${allVideos.length}`);
  
  if (allVideos.length === 0) {
    console.error("❌ No videos found!");
    return false;
  }
  
  // Test each video
  let passedCount = 0;
  let failedCount = 0;
  
  allVideos.forEach((video, index) => {
    console.log(`\n📹 TESTING VIDEO ${index + 1}:`);
    console.log("========================");
    
    // Basic video info
    console.log("Video info:");
    console.log(`  Source: ${video.src || video.currentSrc || 'no src'}`);
    console.log(`  Ready state: ${video.readyState}`);
    console.log(`  Dimensions: ${video.videoWidth || '?'}x${video.videoHeight || '?'}`);
    console.log(`  Duration: ${video.duration || 'unknown'}`);
    console.log(`  Paused: ${video.paused}`);
    
    // Container info
    const moviePlayer = video.closest('#movie_player');
    const htmlPlayer = video.closest('.html5-video-player');
    const adContainer = video.closest('.ad-showing, .video-ads');
    
    console.log("Container info:");
    console.log(`  In #movie_player: ${!!moviePlayer}`);
    console.log(`  In .html5-video-player: ${!!htmlPlayer}`);
    console.log(`  In ad container: ${!!adContainer}`);
    
    // Test suitability
    try {
      const result = window.pipMasterInstance.isVideoSuitableForPiP(video);
      console.log(`\n🎯 SUITABILITY RESULT: ${result ? '✅ PASSED' : '❌ FAILED'}`);
      
      if (result) {
        passedCount++;
        console.log("🎉 This video should work with PiP!");
        
        // Check if it's already tracked
        const isTracked = window.pipMasterInstance.videos.has(video);
        const hasOverlay = window.pipMasterInstance.overlays.has(video);
        
        console.log(`  Already tracked: ${isTracked}`);
        console.log(`  Has overlay: ${hasOverlay}`);
        
        if (!isTracked) {
          console.log("  ⚠️ Video passed but not tracked - may need manual processing");
        }
      } else {
        failedCount++;
        console.log("❌ Video failed suitability check");
      }
    } catch (error) {
      console.error(`❌ Error testing video ${index + 1}:`, error);
      failedCount++;
    }
  });
  
  // Summary
  console.log("\n📊 TEST SUMMARY:");
  console.log("================");
  console.log(`Total videos tested: ${allVideos.length}`);
  console.log(`Passed suitability: ${passedCount} ✅`);
  console.log(`Failed suitability: ${failedCount} ❌`);
  console.log(`Success rate: ${Math.round((passedCount / allVideos.length) * 100)}%`);
  
  if (passedCount > 0) {
    console.log("\n🎉 SUCCESS: Some videos are now passing suitability checks!");
    console.log("The fixes are working. Videos should now show overlay buttons.");
  } else {
    console.log("\n❌ ISSUE: No videos passed suitability checks");
    console.log("There may be additional issues to investigate.");
  }
  
  return { total: allVideos.length, passed: passedCount, failed: failedCount };
};

// Function to force process videos that pass suitability
window.forceProcessPassingVideos = function() {
  console.log("🔧 Force Processing Videos That Pass Suitability");
  console.log("================================================");
  
  if (!window.pipMasterInstance) {
    console.error("❌ Extension instance not available!");
    return;
  }
  
  const videos = document.querySelectorAll('video');
  let processed = 0;
  
  videos.forEach((video, index) => {
    console.log(`\nProcessing video ${index + 1}...`);
    
    try {
      // Test suitability first
      const suitable = window.pipMasterInstance.isVideoSuitableForPiP(video);
      console.log(`  Suitability: ${suitable ? '✅ PASSED' : '❌ FAILED'}`);
      
      if (suitable) {
        // Force add to tracking
        if (!window.pipMasterInstance.videos.has(video)) {
          window.pipMasterInstance.videos.add(video);
          console.log(`  ✅ Added to tracking`);
        }
        
        // Force create overlay
        if (!window.pipMasterInstance.overlays.has(video)) {
          window.pipMasterInstance.createOverlay(video);
          console.log(`  ✅ Overlay created`);
          processed++;
        } else {
          console.log(`  ℹ️ Overlay already exists`);
        }
      } else {
        console.log(`  ⏭️ Skipping - failed suitability check`);
      }
    } catch (error) {
      console.error(`  ❌ Error processing video ${index + 1}:`, error);
    }
  });
  
  console.log(`\n✅ Processed ${processed} suitable videos`);
  
  // Check final state
  setTimeout(() => {
    const trackedCount = window.pipMasterInstance.videos.size;
    const overlayCount = document.querySelectorAll('.pip-master-overlay').length;
    
    console.log(`\n📊 Final State:`);
    console.log(`Videos tracked: ${trackedCount}`);
    console.log(`Overlays in DOM: ${overlayCount}`);
    
    if (overlayCount > 0) {
      console.log("🎉 SUCCESS: Overlays should now be visible on YouTube videos!");
      console.log("👀 Look for overlay buttons on the videos");
      console.log("⌨️ Try Alt+P keyboard shortcut");
    }
  }, 1000);
  
  return processed;
};

// Function to test PiP activation
window.testYouTubePiPActivation = function(videoIndex = 0) {
  console.log(`🧪 Testing PiP Activation on Video ${videoIndex + 1}`);
  console.log("==============================================");
  
  const videos = document.querySelectorAll('video');
  
  if (videos.length === 0) {
    console.error("❌ No videos found");
    return false;
  }
  
  if (videoIndex >= videos.length) {
    console.error(`❌ Video index ${videoIndex} not found (only ${videos.length} videos)`);
    return false;
  }
  
  const video = videos[videoIndex];
  console.log("Testing video:", {
    src: video.src || video.currentSrc || "no src",
    readyState: video.readyState,
    dimensions: `${video.videoWidth}x${video.videoHeight}`,
    paused: video.paused,
    pipDisabled: video.disablePictureInPicture
  });
  
  // Test suitability first
  if (window.pipMasterInstance) {
    const suitable = window.pipMasterInstance.isVideoSuitableForPiP(video);
    console.log(`Suitability check: ${suitable ? '✅ PASSED' : '❌ FAILED'}`);
    
    if (!suitable) {
      console.error("❌ Video failed suitability check - PiP likely won't work");
      return false;
    }
  }
  
  // Test PiP activation
  console.log("🚀 Attempting PiP activation...");
  
  video.requestPictureInPicture()
    .then(() => {
      console.log("✅ PiP activation successful!");
      console.log("🎉 YouTube PiP is working!");
      
      // Auto-exit after 3 seconds
      setTimeout(() => {
        if (document.pictureInPictureElement) {
          document.exitPictureInPicture();
          console.log("✅ PiP exited automatically");
        }
      }, 3000);
      
      return true;
    })
    .catch(error => {
      console.error("❌ PiP activation failed:", error.message);
      
      // Provide specific guidance
      if (error.message.includes('policy')) {
        console.log("💡 YouTube policy restriction - try different video or wait for main content");
      } else if (error.message.includes('disabled')) {
        console.log("💡 PiP disabled on this video");
      } else if (error.message.includes('state')) {
        console.log("💡 Video not ready - wait for it to load and play");
      } else if (error.message.includes('NotAllowed')) {
        console.log("💡 Browser or site policy blocking PiP");
      }
      
      return false;
    });
};

// Auto-run test on YouTube
if (window.location.hostname.includes('youtube.com')) {
  console.log("🎬 Auto-running YouTube suitability tests...");
  
  // Wait for page to load
  setTimeout(() => {
    window.testYouTubeSuitabilityFixes();
    
    // Auto-process passing videos
    setTimeout(() => {
      window.forceProcessPassingVideos();
    }, 2000);
  }, 3000);
}

// Display available commands
console.log("\n📋 Available Test Commands:");
console.log("===========================");
console.log("testYouTubeSuitabilityFixes()    - Test suitability fixes");
console.log("forceProcessPassingVideos()      - Process videos that pass checks");
console.log("testYouTubePiPActivation(index)  - Test PiP on specific video");
console.log("");
console.log("🚀 Quick start: testYouTubeSuitabilityFixes() (auto-runs on YouTube)");
console.log("🔧 Force fix: forceProcessPassingVideos()");
console.log("🧪 Test PiP: testYouTubePiPActivation(0)");
