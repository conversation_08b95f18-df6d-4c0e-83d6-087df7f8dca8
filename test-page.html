<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PiP Master Test Page</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background: #f5f5f5;
      }

      .header {
        text-align: center;
        margin-bottom: 40px;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .video-section {
        margin-bottom: 40px;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .video-section h2 {
        margin-bottom: 20px;
        color: #333;
      }

      .video-container {
        position: relative;
        margin-bottom: 20px;
      }

      video {
        width: 100%;
        max-width: 600px;
        height: auto;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }

      .video-info {
        margin-top: 10px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
        font-size: 14px;
        color: #666;
      }

      .instructions {
        background: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 4px;
      }

      .instructions h3 {
        margin-top: 0;
        color: #1565c0;
      }

      .instructions ul {
        margin-bottom: 0;
      }

      .instructions li {
        margin-bottom: 8px;
      }

      .keyboard-shortcuts {
        background: #fff3e0;
        border-left: 4px solid #ff9800;
        padding: 20px;
        border-radius: 4px;
      }

      .keyboard-shortcuts h3 {
        margin-top: 0;
        color: #e65100;
      }

      .shortcut {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #ffcc02;
      }

      .shortcut:last-child {
        border-bottom: none;
      }

      .shortcut-key {
        font-family: monospace;
        background: #fff;
        padding: 4px 8px;
        border-radius: 4px;
        border: 1px solid #ddd;
        font-weight: bold;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>🎬 PiP Master Test Page</h1>
      <p>Test the Picture-in-Picture functionality with the videos below</p>
    </div>

    <div class="instructions">
      <h3>📋 How to Test</h3>
      <ul>
        <li>Make sure the PiP Master extension is installed and enabled</li>
        <li>Play any video below</li>
        <li>
          Look for the PiP button overlay (usually in the top-right corner)
        </li>
        <li>
          Click the button or use keyboard shortcuts to activate
          Picture-in-Picture
        </li>
        <li>Test different settings through the extension popup</li>
      </ul>
    </div>

    <div class="keyboard-shortcuts">
      <h3>⌨️ Keyboard Shortcuts</h3>
      <div class="shortcut">
        <span>Toggle Picture-in-Picture</span>
        <span class="shortcut-key">Alt + P</span>
      </div>
      <div class="shortcut">
        <span>Increase Opacity</span>
        <span class="shortcut-key">Alt + .</span>
      </div>
      <div class="shortcut">
        <span>Decrease Opacity</span>
        <span class="shortcut-key">Alt + ,</span>
      </div>
    </div>

    <div class="video-section">
      <h2>🎥 Sample Video 1 - Big Buck Bunny</h2>
      <div class="video-container">
        <video controls preload="metadata">
          <source
            src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
            type="video/mp4"
          />
          Your browser does not support the video tag.
        </video>
        <div class="video-info">
          <strong>Source:</strong> Blender Foundation<br />
          <strong>Duration:</strong> ~10 minutes<br />
          <strong>Resolution:</strong> 1280x720
        </div>
      </div>
    </div>

    <div class="video-section">
      <h2>🐘 Sample Video 2 - Elephant Dream</h2>
      <div class="video-container">
        <video controls preload="metadata">
          <source
            src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4"
            type="video/mp4"
          />
          Your browser does not support the video tag.
        </video>
        <div class="video-info">
          <strong>Source:</strong> Blender Foundation<br />
          <strong>Duration:</strong> ~11 minutes<br />
          <strong>Resolution:</strong> 1280x720
        </div>
      </div>
    </div>

    <div class="video-section">
      <h2>🌊 Sample Video 3 - Sintel</h2>
      <div class="video-container">
        <video controls preload="metadata">
          <source
            src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4"
            type="video/mp4"
          />
          Your browser does not support the video tag.
        </video>
        <div class="video-info">
          <strong>Source:</strong> Blender Foundation<br />
          <strong>Duration:</strong> ~15 minutes<br />
          <strong>Resolution:</strong> 1280x720
        </div>
      </div>
    </div>

    <div class="video-section">
      <h2>🎬 Sample Video 4 - Tears of Steel</h2>
      <div class="video-container">
        <video controls preload="metadata">
          <source
            src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4"
            type="video/mp4"
          />
          Your browser does not support the video tag.
        </video>
        <div class="video-info">
          <strong>Source:</strong> Blender Foundation<br />
          <strong>Duration:</strong> ~12 minutes<br />
          <strong>Resolution:</strong> 1920x800
        </div>
      </div>
    </div>

    <div class="instructions">
      <h3>🔧 Testing Checklist</h3>
      <ul>
        <li>✅ PiP overlay buttons appear on videos</li>
        <li>✅ Clicking overlay button activates PiP</li>
        <li>✅ Keyboard shortcut (Alt+P) works</li>
        <li>✅ Extension popup shows correct video count</li>
        <li>✅ Settings can be changed and persist</li>
        <li>✅ Opacity controls work</li>
        <li>✅ Overlay position can be changed</li>
        <li>✅ Dark/light theme support</li>
      </ul>
    </div>

    <script>
      // Add some interactivity for testing
      document.addEventListener("DOMContentLoaded", function () {
        const videos = document.querySelectorAll("video");

        videos.forEach((video, index) => {
          video.addEventListener("loadedmetadata", function () {
            console.log(`Video ${index + 1} metadata loaded:`, {
              duration: video.duration,
              videoWidth: video.videoWidth,
              videoHeight: video.videoHeight,
            });
          });

          video.addEventListener("play", function () {
            console.log(`Video ${index + 1} started playing`);
          });

          video.addEventListener("enterpictureinpicture", function () {
            console.log(`Video ${index + 1} entered Picture-in-Picture mode`);
          });

          video.addEventListener("leavepictureinpicture", function () {
            console.log(`Video ${index + 1} left Picture-in-Picture mode`);
          });
        });
      });
    </script>
  </body>
</html>
