// PiP Master Background Service Worker
// Handles global keyboard shortcuts and extension lifecycle

console.log("PiP Master: Background script starting...");

class PiPMasterBackground {
  constructor() {
    console.log("PiP Master: Initializing background service...");
    this.initializeExtension();
    this.setupEventListeners();
  }

  initializeExtension() {
    console.log("PiP Master: Setting up installation listener...");
    // Set default settings on installation
    chrome.runtime.onInstalled.addListener((details) => {
      console.log("PiP Master: Extension installed/updated:", details.reason);
      if (details.reason === "install") {
        this.setDefaultSettings();
      }
    });
  }

  async setDefaultSettings() {
    console.log("PiP Master: Setting default settings...");
    const defaultSettings = {
      enabled: true,
      showOverlay: true,
      overlayPosition: "top-right",
      opacity: 0.9,
      snapToCorners: true,
      theme: "auto",
      shortcuts: {
        togglePip: "Alt+P",
        increaseOpacity: "Alt+.",
        decreaseOpacity: "Alt+,",
      },
      audioControls: true,
      alwaysOnTop: true,
      resizable: true,
      autoActivate: false, // Auto-activate PiP for long videos
      hoverActivation: true, // Show overlay on hover
      doubleClickActivation: true, // Allow double-click to activate PiP
    };

    try {
      await chrome.storage.sync.set({ pipMasterSettings: defaultSettings });
      console.log("PiP Master: Default settings initialized successfully");
    } catch (error) {
      console.error("PiP Master: Failed to set default settings:", error);
    }
  }

  setupEventListeners() {
    console.log("PiP Master: Setting up event listeners...");

    // Handle keyboard shortcuts
    chrome.commands.onCommand.addListener((command) => {
      console.log("PiP Master: Keyboard command received:", command);
      this.handleCommand(command);
    });

    // Handle messages from content scripts
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      console.log(
        "PiP Master: Message received:",
        message.type,
        "from tab:",
        sender.tab?.id
      );
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    console.log("PiP Master: Event listeners set up successfully");
  }

  async handleCommand(command) {
    console.log("PiP Master: Handling command:", command);
    try {
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });

      if (!tab) {
        console.warn("PiP Master: No active tab found");
        return;
      }

      console.log("PiP Master: Sending command to tab:", tab.id, tab.url);

      // Send command to content script
      await chrome.tabs.sendMessage(tab.id, {
        type: "KEYBOARD_COMMAND",
        command: command,
      });

      console.log("PiP Master: Command sent successfully");
    } catch (error) {
      console.error("PiP Master: Failed to handle command:", error);
      console.error("Error details:", error.message);
    }
  }

  async handleMessage(message, sender, sendResponse) {
    console.log("PiP Master: Processing message:", message.type);
    try {
      switch (message.type) {
        case "GET_SETTINGS":
          console.log("PiP Master: Getting settings...");
          const settings = await this.getSettings();
          console.log("PiP Master: Settings retrieved:", Object.keys(settings));
          sendResponse({ success: true, data: settings });
          break;

        case "UPDATE_SETTINGS":
          console.log(
            "PiP Master: Updating settings:",
            Object.keys(message.data)
          );
          await this.updateSettings(message.data);
          console.log("PiP Master: Settings updated successfully");
          sendResponse({ success: true });
          break;

        case "PIP_STATUS_CHANGED":
          // Handle PiP status changes for analytics or other features
          console.log("PiP Master: PiP status changed:", message.data);
          sendResponse({ success: true });
          break;

        default:
          console.warn("PiP Master: Unknown message type:", message.type);
          sendResponse({ success: false, error: "Unknown message type" });
      }
    } catch (error) {
      console.error("PiP Master: Error handling message:", error);
      console.error("Error details:", error.message);
      sendResponse({ success: false, error: error.message });
    }
  }

  async getSettings() {
    console.log("PiP Master: Retrieving settings from storage...");
    try {
      const result = await chrome.storage.sync.get("pipMasterSettings");
      const settings = result.pipMasterSettings || {};
      console.log(
        "PiP Master: Settings loaded:",
        Object.keys(settings).length,
        "keys"
      );
      return settings;
    } catch (error) {
      console.error("PiP Master: Failed to get settings:", error);
      return {};
    }
  }

  async updateSettings(newSettings) {
    console.log("PiP Master: Updating settings in storage...");
    try {
      const currentSettings = await this.getSettings();
      const updatedSettings = { ...currentSettings, ...newSettings };
      await chrome.storage.sync.set({ pipMasterSettings: updatedSettings });
      console.log("PiP Master: Settings saved to storage");

      // Notify all content scripts about settings change
      const tabs = await chrome.tabs.query({});
      console.log(
        "PiP Master: Notifying",
        tabs.length,
        "tabs about settings update"
      );

      for (const tab of tabs) {
        try {
          await chrome.tabs.sendMessage(tab.id, {
            type: "SETTINGS_UPDATED",
            data: updatedSettings,
          });
        } catch (error) {
          // Tab might not have content script loaded, ignore
          console.debug(
            "PiP Master: Could not notify tab",
            tab.id,
            "- content script not loaded"
          );
        }
      }
      console.log("PiP Master: Settings update notifications sent");
    } catch (error) {
      console.error("PiP Master: Failed to update settings:", error);
      throw error;
    }
  }
}

// Initialize the background service
console.log("PiP Master: Creating background service instance...");
const pipMasterBackground = new PiPMasterBackground();
console.log("PiP Master: Background service initialized successfully");
