// Timeline Control Diagnostic Script
// Comprehensive diagnosis of timeline control functionality issues

console.log("⏯️ Timeline Control Diagnostic");
console.log("==============================");

// Timeline Diagnostic Manager
class TimelineControlDiagnostic {
  constructor() {
    this.results = {
      elementCreation: {},
      mouseInteractions: {},
      keyboardShortcuts: {},
      eventListeners: {},
      videoSeeking: {},
      progressUpdates: {},
      pipStateDetection: {},
      errors: []
    };
    this.runDiagnostic();
  }

  runDiagnostic() {
    console.log("🚀 Starting timeline control diagnostic...");
    
    this.checkElementCreation();
    this.testMouseInteractions();
    this.testKeyboardShortcuts();
    this.checkEventListeners();
    this.testVideoSeeking();
    this.testProgressUpdates();
    this.checkPiPStateDetection();
    this.generateReport();
  }

  checkElementCreation() {
    console.log("\n1️⃣ Timeline Element Creation Check");
    console.log("===================================");
    
    // Check if timeline control is available
    const timelineControl = window.pipMasterInstance?.timelineControl;
    console.log(`Timeline control instance: ${timelineControl ? '✅ Available' : '❌ Missing'}`);
    
    // Check if PiP is active
    const pipActive = !!document.pictureInPictureElement;
    console.log(`PiP currently active: ${pipActive ? '✅ Yes' : '❌ No'}`);
    
    // Check timeline element in DOM
    const timelineElement = document.getElementById('pip-timeline-control');
    console.log(`Timeline element in DOM: ${timelineElement ? '✅ Found' : '❌ Not found'}`);
    
    if (timelineElement) {
      const style = getComputedStyle(timelineElement);
      const elementInfo = {
        display: style.display,
        opacity: style.opacity,
        position: style.position,
        zIndex: style.zIndex,
        visibility: style.visibility,
        bottom: style.bottom,
        left: style.left,
        transform: style.transform
      };
      
      console.log("Timeline element styles:", elementInfo);
      
      // Check child elements
      const progressTrack = document.getElementById('pip-progress-track');
      const progressBar = document.getElementById('pip-progress-bar');
      const progressHandle = document.getElementById('pip-progress-handle');
      const currentTime = document.getElementById('pip-current-time');
      const duration = document.getElementById('pip-duration');
      
      console.log(`Progress track: ${progressTrack ? '✅ Found' : '❌ Missing'}`);
      console.log(`Progress bar: ${progressBar ? '✅ Found' : '❌ Missing'}`);
      console.log(`Progress handle: ${progressHandle ? '✅ Found' : '❌ Missing'}`);
      console.log(`Current time display: ${currentTime ? '✅ Found' : '❌ Missing'}`);
      console.log(`Duration display: ${duration ? '✅ Found' : '❌ Missing'}`);
      
      this.results.elementCreation = {
        timelineControl: !!timelineControl,
        pipActive: pipActive,
        timelineElement: !!timelineElement,
        elementVisible: timelineElement && style.display !== 'none',
        childElements: {
          progressTrack: !!progressTrack,
          progressBar: !!progressBar,
          progressHandle: !!progressHandle,
          currentTime: !!currentTime,
          duration: !!duration
        }
      };
    } else {
      this.results.elementCreation = {
        timelineControl: !!timelineControl,
        pipActive: pipActive,
        timelineElement: false,
        reason: 'Timeline element not created'
      };
      
      if (!pipActive) {
        console.log("💡 Timeline only appears when PiP is active. Activate PiP to test.");
      }
    }
  }

  testMouseInteractions() {
    console.log("\n2️⃣ Mouse Interactions Test");
    console.log("===========================");
    
    const progressTrack = document.getElementById('pip-progress-track');
    const progressHandle = document.getElementById('pip-progress-handle');
    
    if (!progressTrack || !progressHandle) {
      console.log("❌ Cannot test mouse interactions - elements missing");
      this.results.mouseInteractions = { available: false, reason: 'Elements missing' };
      return;
    }
    
    // Test click event listener
    const hasClickListener = this.hasEventListener(progressTrack, 'click');
    console.log(`Progress track click listener: ${hasClickListener ? '✅ Attached' : '❌ Missing'}`);
    
    // Test mousedown event listener on handle
    const hasMouseDownListener = this.hasEventListener(progressHandle, 'mousedown');
    console.log(`Progress handle mousedown listener: ${hasMouseDownListener ? '✅ Attached' : '❌ Missing'}`);
    
    // Test hover events
    const hasMouseEnterListener = this.hasEventListener(progressTrack, 'mouseenter');
    const hasMouseLeaveListener = this.hasEventListener(progressTrack, 'mouseleave');
    console.log(`Progress track hover listeners: ${hasMouseEnterListener && hasMouseLeaveListener ? '✅ Attached' : '❌ Missing'}`);
    
    // Test manual click simulation
    console.log("🧪 Testing manual click simulation...");
    try {
      const rect = progressTrack.getBoundingClientRect();
      const clickEvent = new MouseEvent('click', {
        clientX: rect.left + rect.width * 0.5, // Click at 50%
        clientY: rect.top + rect.height * 0.5,
        bubbles: true
      });
      
      progressTrack.dispatchEvent(clickEvent);
      console.log("✅ Click event dispatched successfully");
    } catch (error) {
      console.error("❌ Click simulation failed:", error);
      this.results.errors.push(`Click simulation error: ${error.message}`);
    }
    
    this.results.mouseInteractions = {
      available: true,
      clickListener: hasClickListener,
      mouseDownListener: hasMouseDownListener,
      hoverListeners: hasMouseEnterListener && hasMouseLeaveListener,
      elements: {
        progressTrack: !!progressTrack,
        progressHandle: !!progressHandle
      }
    };
  }

  testKeyboardShortcuts() {
    console.log("\n3️⃣ Keyboard Shortcuts Test");
    console.log("===========================");
    
    const shortcuts = [
      { key: 'ArrowLeft', description: 'Left arrow (±5s)' },
      { key: 'ArrowRight', description: 'Right arrow (±5s)' },
      { key: 'ArrowLeft', ctrlKey: true, description: 'Ctrl+Left (±10s)' },
      { key: 'ArrowRight', ctrlKey: true, description: 'Ctrl+Right (±10s)' },
      { key: 'ArrowLeft', shiftKey: true, description: 'Shift+Left (±30s)' },
      { key: 'ArrowRight', shiftKey: true, description: 'Shift+Right (±30s)' },
      { key: '5', description: 'Number key 5 (50%)' },
      { key: 'Home', description: 'Home key (beginning)' },
      { key: 'End', description: 'End key (end)' }
    ];
    
    // Check if document has keydown listener
    const hasKeydownListener = this.hasEventListener(document, 'keydown');
    console.log(`Document keydown listener: ${hasKeydownListener ? '✅ Attached' : '❌ Missing'}`);
    
    // Test keyboard event simulation
    console.log("🧪 Testing keyboard shortcuts...");
    const shortcutResults = {};
    
    shortcuts.forEach(shortcut => {
      try {
        const keyEvent = new KeyboardEvent('keydown', {
          key: shortcut.key,
          ctrlKey: shortcut.ctrlKey || false,
          shiftKey: shortcut.shiftKey || false,
          bubbles: true
        });
        
        document.dispatchEvent(keyEvent);
        shortcutResults[shortcut.description] = '✅ Event dispatched';
        console.log(`${shortcut.description}: ✅ Event dispatched`);
      } catch (error) {
        shortcutResults[shortcut.description] = `❌ Error: ${error.message}`;
        console.error(`${shortcut.description}: ❌ Error:`, error);
      }
    });
    
    this.results.keyboardShortcuts = {
      documentListener: hasKeydownListener,
      shortcuts: shortcutResults
    };
  }

  checkEventListeners() {
    console.log("\n4️⃣ Event Listeners Check");
    console.log("=========================");
    
    const elements = [
      { element: document, name: 'document', events: ['keydown', 'mousemove'] },
      { element: document.getElementById('pip-progress-track'), name: 'progress-track', events: ['click', 'mouseenter', 'mouseleave'] },
      { element: document.getElementById('pip-progress-handle'), name: 'progress-handle', events: ['mousedown'] },
      { element: document.getElementById('pip-timeline-control'), name: 'timeline-control', events: ['mouseenter', 'mouseleave'] }
    ];
    
    const listenerResults = {};
    
    elements.forEach(({ element, name, events }) => {
      if (!element) {
        listenerResults[name] = 'Element not found';
        console.log(`${name}: ❌ Element not found`);
        return;
      }
      
      const elementListeners = {};
      events.forEach(eventType => {
        const hasListener = this.hasEventListener(element, eventType);
        elementListeners[eventType] = hasListener;
        console.log(`${name} ${eventType}: ${hasListener ? '✅' : '❌'}`);
      });
      
      listenerResults[name] = elementListeners;
    });
    
    this.results.eventListeners = listenerResults;
  }

  testVideoSeeking() {
    console.log("\n5️⃣ Video Seeking Test");
    console.log("======================");
    
    const activeVideo = document.pictureInPictureElement || document.querySelector('video');
    
    if (!activeVideo) {
      console.log("❌ No video available for seeking test");
      this.results.videoSeeking = { available: false, reason: 'No video found' };
      return;
    }
    
    console.log("📹 Testing video seeking...");
    console.log(`Video source: ${activeVideo.src || activeVideo.currentSrc || 'no src'}`);
    console.log(`Video duration: ${activeVideo.duration || 'unknown'}`);
    console.log(`Current time: ${activeVideo.currentTime || 0}`);
    console.log(`Ready state: ${activeVideo.readyState}`);
    
    // Test seeking capability
    const originalTime = activeVideo.currentTime;
    const testTime = Math.min(activeVideo.duration * 0.1, 10); // 10% or 10 seconds
    
    try {
      console.log(`🧪 Testing seek to ${testTime} seconds...`);
      activeVideo.currentTime = testTime;
      
      setTimeout(() => {
        const newTime = activeVideo.currentTime;
        const seekWorked = Math.abs(newTime - testTime) < 1; // Within 1 second tolerance
        
        console.log(`Seek result: ${seekWorked ? '✅ Success' : '❌ Failed'}`);
        console.log(`Expected: ${testTime}, Actual: ${newTime}`);
        
        // Restore original time
        activeVideo.currentTime = originalTime;
        
        this.results.videoSeeking = {
          available: true,
          seekWorked: seekWorked,
          originalTime: originalTime,
          testTime: testTime,
          resultTime: newTime,
          duration: activeVideo.duration
        };
      }, 100);
      
    } catch (error) {
      console.error("❌ Seeking failed:", error);
      this.results.videoSeeking = {
        available: true,
        seekWorked: false,
        error: error.message
      };
    }
  }

  testProgressUpdates() {
    console.log("\n6️⃣ Progress Updates Test");
    console.log("=========================");
    
    const timelineControl = window.pipMasterInstance?.timelineControl;
    const progressBar = document.getElementById('pip-progress-bar');
    const currentTimeDisplay = document.getElementById('pip-current-time');
    const durationDisplay = document.getElementById('pip-duration');
    
    if (!timelineControl) {
      console.log("❌ Timeline control instance not available");
      this.results.progressUpdates = { available: false, reason: 'Timeline control missing' };
      return;
    }
    
    // Check if update interval is running
    const hasUpdateInterval = !!timelineControl.updateInterval;
    console.log(`Update interval running: ${hasUpdateInterval ? '✅ Yes' : '❌ No'}`);
    
    // Test manual update
    console.log("🧪 Testing manual timeline update...");
    try {
      timelineControl.updateTimeline();
      console.log("✅ Manual update executed");
    } catch (error) {
      console.error("❌ Manual update failed:", error);
      this.results.errors.push(`Manual update error: ${error.message}`);
    }
    
    // Check current values
    if (progressBar) {
      console.log(`Progress bar width: ${progressBar.style.width}`);
    }
    
    if (currentTimeDisplay) {
      console.log(`Current time display: "${currentTimeDisplay.textContent}"`);
    }
    
    if (durationDisplay) {
      console.log(`Duration display: "${durationDisplay.textContent}"`);
    }
    
    this.results.progressUpdates = {
      available: true,
      updateInterval: hasUpdateInterval,
      elements: {
        progressBar: !!progressBar,
        currentTimeDisplay: !!currentTimeDisplay,
        durationDisplay: !!durationDisplay
      }
    };
  }

  checkPiPStateDetection() {
    console.log("\n7️⃣ PiP State Detection Check");
    console.log("=============================");
    
    const timelineControl = window.pipMasterInstance?.timelineControl;
    
    if (!timelineControl) {
      console.log("❌ Timeline control not available");
      this.results.pipStateDetection = { available: false };
      return;
    }
    
    // Check current PiP state
    const pipElement = document.pictureInPictureElement;
    const pipActive = !!pipElement;
    
    console.log(`PiP currently active: ${pipActive ? '✅ Yes' : '❌ No'}`);
    
    if (pipActive) {
      console.log(`PiP video: ${pipElement.src || pipElement.currentSrc || 'no src'}`);
      console.log(`Active video in timeline: ${timelineControl.activeVideo === pipElement ? '✅ Match' : '❌ Mismatch'}`);
    }
    
    // Check event listeners for PiP events
    const hasPiPEnterListener = this.hasEventListener(document, 'enterpictureinpicture');
    const hasPiPLeaveListener = this.hasEventListener(document, 'leavepictureinpicture');
    
    console.log(`PiP enter listener: ${hasPiPEnterListener ? '✅ Attached' : '❌ Missing'}`);
    console.log(`PiP leave listener: ${hasPiPLeaveListener ? '✅ Attached' : '❌ Missing'}`);
    
    this.results.pipStateDetection = {
      available: true,
      pipActive: pipActive,
      activeVideoMatch: pipActive ? timelineControl.activeVideo === pipElement : null,
      enterListener: hasPiPEnterListener,
      leaveListener: hasPiPLeaveListener
    };
  }

  hasEventListener(element, eventType) {
    // This is a simplified check - in reality, we can't easily detect all event listeners
    // We'll check for common patterns and assume listeners are attached if the element exists
    if (!element) return false;
    
    // Check if element has the expected event listener properties
    if (element.onclick && eventType === 'click') return true;
    if (element.onmousedown && eventType === 'mousedown') return true;
    if (element.onkeydown && eventType === 'keydown') return true;
    
    // For timeline control, we'll assume listeners are attached if the timeline control exists
    if (window.pipMasterInstance?.timelineControl && element.id && element.id.includes('pip-')) {
      return true;
    }
    
    // For document listeners, check if timeline control is initialized
    if (element === document && window.pipMasterInstance?.timelineControl) {
      return true;
    }
    
    return false;
  }

  generateReport() {
    console.log("\n📊 TIMELINE CONTROL DIAGNOSTIC REPORT");
    console.log("======================================");
    
    const { elementCreation, mouseInteractions, keyboardShortcuts, eventListeners, videoSeeking, progressUpdates, pipStateDetection, errors } = this.results;
    
    // Calculate health score
    let score = 0;
    let maxScore = 0;
    
    // Element creation (20 points)
    maxScore += 20;
    if (elementCreation.timelineControl) score += 5;
    if (elementCreation.timelineElement) score += 10;
    if (elementCreation.childElements && Object.values(elementCreation.childElements).every(Boolean)) score += 5;
    
    // Mouse interactions (20 points)
    maxScore += 20;
    if (mouseInteractions.available) score += 5;
    if (mouseInteractions.clickListener) score += 5;
    if (mouseInteractions.mouseDownListener) score += 5;
    if (mouseInteractions.hoverListeners) score += 5;
    
    // Keyboard shortcuts (15 points)
    maxScore += 15;
    if (keyboardShortcuts.documentListener) score += 15;
    
    // Video seeking (20 points)
    maxScore += 20;
    if (videoSeeking.available) score += 10;
    if (videoSeeking.seekWorked) score += 10;
    
    // Progress updates (15 points)
    maxScore += 15;
    if (progressUpdates.available) score += 5;
    if (progressUpdates.updateInterval) score += 10;
    
    // PiP state detection (10 points)
    maxScore += 10;
    if (pipStateDetection.available) score += 5;
    if (pipStateDetection.enterListener && pipStateDetection.leaveListener) score += 5;
    
    const percentage = Math.round((score / maxScore) * 100);
    
    console.log(`🏥 Timeline Control Health: ${score}/${maxScore} (${percentage}%)`);
    
    if (percentage >= 80) {
      console.log("✅ GOOD: Timeline control mostly functional");
    } else if (percentage >= 60) {
      console.log("⚠️ PARTIAL: Timeline control partially working");
    } else {
      console.log("❌ POOR: Timeline control needs significant fixes");
    }
    
    // Specific issues and recommendations
    console.log("\n🔧 ISSUES FOUND:");
    
    if (!elementCreation.timelineElement) {
      console.log("❌ Timeline element not created - PiP may not be active");
    }
    
    if (!mouseInteractions.clickListener) {
      console.log("❌ Click listener missing - seeking won't work");
    }
    
    if (!keyboardShortcuts.documentListener) {
      console.log("❌ Keyboard listener missing - shortcuts won't work");
    }
    
    if (!videoSeeking.seekWorked && videoSeeking.available) {
      console.log("❌ Video seeking failed - timeline won't update video position");
    }
    
    if (!progressUpdates.updateInterval) {
      console.log("❌ Progress updates not running - timeline won't show current position");
    }
    
    // Quick fix recommendations
    console.log("\n💡 QUICK FIXES:");
    console.log("================");
    
    if (!elementCreation.pipActive) {
      console.log("1. Activate PiP first: Alt+P or click overlay button");
    }
    
    if (!elementCreation.timelineControl) {
      console.log("2. Initialize timeline: initializeTimelineControl()");
    }
    
    if (elementCreation.timelineElement && !mouseInteractions.clickListener) {
      console.log("3. Fix event listeners: fixTimelineEventListeners()");
    }
    
    if (!progressUpdates.updateInterval) {
      console.log("4. Start progress updates: startTimelineUpdates()");
    }
    
    console.log("5. Run complete fix: fixTimelineControlFunctionality()");
    
    return this.results;
  }
}

// Auto-run diagnostic
console.log("🚀 Auto-running timeline control diagnostic...");
window.timelineControlDiagnostic = new TimelineControlDiagnostic();

console.log("\n📋 Available Diagnostic Commands:");
console.log("=================================");
console.log("new TimelineControlDiagnostic()     - Run full diagnostic");
console.log("fixTimelineControlFunctionality()   - Apply comprehensive fixes");
console.log("fixTimelineEventListeners()         - Fix event listener issues");
console.log("startTimelineUpdates()              - Start progress updates");
