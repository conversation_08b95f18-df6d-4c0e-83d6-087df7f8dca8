// Google PiP Extension Analysis & Implementation
// Based on Chrome extension best practices and Google's approach

console.log("🔍 Google PiP Extension Analysis & Enhancement");
console.log("==============================================");

// Google's approach analysis based on Chrome extension patterns
window.googlePipAnalysis = {
  
  // Google's video detection approach (simplified but effective)
  findVideos() {
    console.log("🎯 Google-style video detection");
    
    // Google uses a simple but effective approach
    const videos = Array.from(document.querySelectorAll('video')).filter(video => {
      // Basic visibility check
      const rect = video.getBoundingClientRect();
      const style = getComputedStyle(video);
      
      return (
        // Must be a video element
        video.tagName === 'VIDEO' &&
        // Must not be explicitly disabled
        !video.disablePictureInPicture &&
        // Must be somewhat visible
        rect.width > 0 && rect.height > 0 &&
        style.display !== 'none' &&
        // Must have some content
        (video.readyState === 0 || video.videoWidth > 0 || video.videoHeight > 0)
      );
    });
    
    console.log(`Found ${videos.length} suitable videos`);
    return videos;
  },
  
  // Google's suitability check (very permissive)
  isVideoSuitable(video) {
    console.log("✅ Google-style suitability check");
    
    // Google's approach is very permissive - they let the browser decide
    if (!video || video.tagName !== 'VIDEO') {
      console.log("❌ Not a video element");
      return false;
    }
    
    if (video.disablePictureInPicture) {
      console.log("❌ PiP explicitly disabled");
      return false;
    }
    
    // Google doesn't do complex ad detection - they rely on browser behavior
    // If the video can't do PiP, the browser will handle the error
    
    const rect = video.getBoundingClientRect();
    if (rect.width === 0 && rect.height === 0) {
      console.log("❌ Video has no dimensions");
      return false;
    }
    
    console.log("✅ Video is suitable");
    return true;
  },
  
  // Google's PiP activation (simple and robust)
  async activatePiP(video) {
    console.log("🚀 Google-style PiP activation");
    
    if (!video) {
      // Find the best video automatically
      const videos = this.findVideos();
      if (videos.length === 0) {
        throw new Error('No suitable video found for Picture-in-Picture');
      }
      
      // Google prioritizes the largest video
      video = videos.reduce((largest, current) => {
        const largestRect = largest.getBoundingClientRect();
        const currentRect = current.getBoundingClientRect();
        const largestArea = largestRect.width * largestRect.height;
        const currentArea = currentRect.width * currentRect.height;
        return currentArea > largestArea ? current : largest;
      });
    }
    
    if (!this.isVideoSuitable(video)) {
      throw new Error('Video is not suitable for Picture-in-Picture');
    }
    
    try {
      if (document.pictureInPictureElement) {
        console.log("Exiting existing PiP");
        await document.exitPictureInPicture();
      } else {
        console.log("Requesting PiP for video:", video.src || video.currentSrc || 'no src');
        await video.requestPictureInPicture();
        console.log("✅ PiP activated successfully");
      }
    } catch (error) {
      console.error("❌ PiP activation failed:", error.message);
      throw error;
    }
  }
};

// Enhanced PiP Master implementation based on Google's approach
window.enhancePipMaster = function() {
  console.log("🔧 Enhancing PiP Master with Google's approach");
  
  if (!window.pipMasterInstance) {
    console.error("❌ PiP Master instance not available");
    return false;
  }
  
  // Backup original methods
  window.pipMasterInstance._originalFindVideos = window.pipMasterInstance.performUniversalVideoScan;
  window.pipMasterInstance._originalIsVideoSuitable = window.pipMasterInstance.isVideoSuitableForPiP;
  window.pipMasterInstance._originalTogglePiP = window.pipMasterInstance.togglePiP;
  
  // Enhanced video detection (Google-style)
  window.pipMasterInstance.performUniversalVideoScan = function() {
    console.log("🎯 Enhanced video detection (Google-style)");
    
    const videos = Array.from(document.querySelectorAll('video')).filter(video => {
      const rect = video.getBoundingClientRect();
      const style = getComputedStyle(video);
      
      return (
        video.tagName === 'VIDEO' &&
        !video.disablePictureInPicture &&
        rect.width > 0 && rect.height > 0 &&
        style.display !== 'none'
      );
    });
    
    console.log(`Enhanced scan found ${videos.length} videos`);
    
    videos.forEach(video => {
      if (!this.videos.has(video)) {
        console.log("Processing video:", video.src || video.currentSrc || 'no src');
        this.handleVideoFound(video);
      }
    });
  };
  
  // Enhanced suitability check (Google-style - very permissive)
  window.pipMasterInstance.isVideoSuitableForPiP = function(video) {
    console.log("✅ Enhanced suitability check (Google-style)");
    
    if (!video || video.tagName !== 'VIDEO') {
      console.log("❌ Not a video element");
      return false;
    }
    
    if (video.disablePictureInPicture) {
      console.log("❌ PiP explicitly disabled");
      return false;
    }
    
    // Very basic checks - let the browser handle the rest
    const rect = video.getBoundingClientRect();
    if (rect.width === 0 && rect.height === 0) {
      console.log("❌ Video has no dimensions");
      return false;
    }
    
    // Skip complex ad detection - Google doesn't do it
    console.log("✅ Video passed enhanced suitability check");
    return true;
  };
  
  // Enhanced PiP activation (Google-style)
  window.pipMasterInstance.togglePiP = async function(video) {
    console.log("🚀 Enhanced PiP activation (Google-style)");
    
    try {
      if (!video) {
        // Find the best video automatically (Google's approach)
        const videos = Array.from(document.querySelectorAll('video')).filter(v => {
          const rect = v.getBoundingClientRect();
          return (
            v.tagName === 'VIDEO' &&
            !v.disablePictureInPicture &&
            rect.width > 0 && rect.height > 0 &&
            getComputedStyle(v).display !== 'none'
          );
        });
        
        if (videos.length === 0) {
          throw new Error('No suitable video found for Picture-in-Picture');
        }
        
        // Prioritize the largest video
        video = videos.reduce((largest, current) => {
          const largestRect = largest.getBoundingClientRect();
          const currentRect = current.getBoundingClientRect();
          const largestArea = largestRect.width * largestRect.height;
          const currentArea = currentRect.width * currentRect.height;
          return currentArea > largestArea ? current : largest;
        });
      }
      
      if (document.pictureInPictureElement) {
        console.log("Exiting existing PiP");
        await document.exitPictureInPicture();
      } else {
        console.log("Requesting PiP for video:", video.src || video.currentSrc || 'no src');
        await video.requestPictureInPicture();
        this.activeVideo = video;
        console.log("✅ Enhanced PiP activated successfully");
        
        // Show success feedback
        if (this.showSuccess) {
          this.showSuccess("Picture-in-Picture activated!");
        }
      }
    } catch (error) {
      console.error("❌ Enhanced PiP activation failed:", error.message);
      
      // Show error feedback
      if (this.showError) {
        this.showError(this.getEnhancedErrorMessage ? this.getEnhancedErrorMessage(error, video) : error.message);
      }
      
      throw error;
    }
  };
  
  console.log("✅ PiP Master enhanced with Google's approach");
  return true;
};

// Test the enhanced implementation
window.testEnhancedPipMaster = function() {
  console.log("🧪 Testing Enhanced PiP Master");
  console.log("==============================");
  
  // Test video detection
  const videos = window.googlePipAnalysis.findVideos();
  console.log(`Google-style detection found: ${videos.length} videos`);
  
  if (videos.length > 0) {
    videos.forEach((video, index) => {
      console.log(`Video ${index + 1}:`, {
        src: video.src || video.currentSrc || 'no src',
        dimensions: `${video.videoWidth || '?'}x${video.videoHeight || '?'}`,
        readyState: video.readyState,
        suitable: window.googlePipAnalysis.isVideoSuitable(video)
      });
    });
    
    // Test PiP activation on first suitable video
    const suitableVideo = videos.find(v => window.googlePipAnalysis.isVideoSuitable(v));
    if (suitableVideo) {
      console.log("🚀 Testing PiP activation...");
      window.googlePipAnalysis.activatePiP(suitableVideo)
        .then(() => {
          console.log("✅ Test successful!");
          setTimeout(() => {
            if (document.pictureInPictureElement) {
              document.exitPictureInPicture();
            }
          }, 3000);
        })
        .catch(error => {
          console.error("❌ Test failed:", error.message);
        });
    }
  }
};

// Auto-enhance on YouTube
if (window.location.hostname.includes('youtube.com')) {
  console.log("🎬 Auto-enhancing for YouTube...");
  setTimeout(() => {
    window.enhancePipMaster();
    setTimeout(() => {
      window.testEnhancedPipMaster();
    }, 1000);
  }, 2000);
}

// Display available commands
console.log("\n📋 Available Enhancement Commands:");
console.log("==================================");
console.log("enhancePipMaster()           - Apply Google's approach to PiP Master");
console.log("testEnhancedPipMaster()      - Test the enhanced implementation");
console.log("googlePipAnalysis.findVideos()        - Find videos Google-style");
console.log("googlePipAnalysis.activatePiP()       - Activate PiP Google-style");
console.log("");
console.log("🚀 Quick start: enhancePipMaster() (auto-runs on YouTube)");
console.log("🧪 To test: testEnhancedPipMaster()");
