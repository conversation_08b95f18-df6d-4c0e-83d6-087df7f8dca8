# PiP Master Extension - Debug Guide

## 🔍 Debugging Video Detection Issues

### Step 1: Load the Fixed Extension

1. **Go to** `chrome://extensions/`
2. **Remove** the old PiP Master extension if installed
3. **Click "Load unpacked"**
4. **Select** the PiP folder (`c:\git_projects\PIP`)
5. **Verify** no errors appear in the extension card

### Step 2: Open Test Page and DevTools

1. **Open** the test page: `file:///c:/git_projects/PIP/test-page.html`
2. **Press F12** to open Chrome DevTools
3. **Go to Console tab**
4. **Clear the console** (Ctrl+L)

### Step 3: Check Extension Loading

Look for these console messages (should appear immediately):

```
PiP Master: Background script starting...
PiP Master: Content script starting...
PiP Master: Initializing content script...
PiP Master: Detecting platform for hostname: ...
PiP Master: DOM already ready, creating instance...
```

**If you don't see these messages:**
- Extension didn't load properly
- Check chrome://extensions/ for errors
- Try reloading the extension

### Step 4: Check Video Detection

Look for these messages after page loads:

```
PiP Master: Starting universal video scan...
PiP Master: Basic scan found X videos
PiP Master: Processing video 1: ...
PiP Master: Checking video suitability for: ...
PiP Master: Video passed suitability check: ...
```

**If you see "Basic scan found 0 videos":**
- Videos aren't being detected at all
- Run manual debug commands (see Step 5)

### Step 5: Manual Debug Commands

Run these commands in the Console to debug:

```javascript
// Check if extension loaded
console.log('Extension loaded:', !!window.pipMasterContentLoaded);
console.log('Instance available:', !!window.pipMasterInstance);

// Check basic video detection
const videos = document.querySelectorAll('video');
console.log('Videos in DOM:', videos.length);
videos.forEach((v, i) => console.log(`Video ${i}:`, v.src || v.currentSrc || 'no src'));

// Manual video scan
window.pipMasterDebug.scanForVideos();

// Check tracked videos
console.log('Tracked videos:', window.pipMasterDebug.getVideoCount());
window.pipMasterDebug.listVideos();

// Test PiP on first video
window.pipMasterDebug.testPiP(0);
```

### Step 6: Check PiP API Support

```javascript
// Check PiP API availability
console.log('PiP API supported:', 'pictureInPictureEnabled' in document);
console.log('PiP enabled:', document.pictureInPictureEnabled);

// Test manual PiP
const video = document.querySelector('video');
if (video) {
  video.requestPictureInPicture()
    .then(() => console.log('✅ Manual PiP works'))
    .catch(err => console.error('❌ Manual PiP failed:', err));
}
```

### Step 7: Check for Overlays

```javascript
// Check if overlays were created
const overlays = document.querySelectorAll('.pip-master-overlay');
console.log('Overlays found:', overlays.length);

// Check overlay visibility
overlays.forEach((overlay, i) => {
  console.log(`Overlay ${i}:`, {
    display: overlay.style.display,
    visibility: getComputedStyle(overlay).visibility,
    opacity: getComputedStyle(overlay).opacity
  });
});
```

### Step 8: Force Video Processing

If videos exist but aren't being processed:

```javascript
// Force process all videos
const videos = document.querySelectorAll('video');
videos.forEach((video, i) => {
  console.log(`Forcing processing of video ${i}...`);
  if (window.pipMasterInstance) {
    window.pipMasterInstance.handleVideoFound(video);
  }
});
```

## 🐛 Common Issues and Solutions

### Issue: "Basic scan found 0 videos" but videos exist

**Cause:** Videos might be loading after the scan
**Solution:**
```javascript
// Wait for videos to load, then scan
setTimeout(() => {
  window.pipMasterDebug.scanForVideos();
}, 2000);
```

### Issue: Videos found but suitability check fails

**Cause:** Strict suitability checks
**Solution:** Check what's failing:
```javascript
const video = document.querySelector('video');
if (video && window.pipMasterInstance) {
  console.log('Video details:', {
    tagName: video.tagName,
    disablePictureInPicture: video.disablePictureInPicture,
    readyState: video.readyState,
    videoWidth: video.videoWidth,
    videoHeight: video.videoHeight,
    display: getComputedStyle(video).display,
    visibility: getComputedStyle(video).visibility
  });
}
```

### Issue: Overlays not appearing

**Cause:** CSS not loading or positioning issues
**Solution:**
```javascript
// Check if CSS loaded
const cssLoaded = document.querySelector('style[href*="content.css"]') || 
                  Array.from(document.styleSheets).some(sheet => 
                    sheet.href && sheet.href.includes('content.css'));
console.log('CSS loaded:', cssLoaded);

// Manually create overlay for testing
const video = document.querySelector('video');
if (video && window.pipMasterInstance) {
  window.pipMasterInstance.createOverlay(video);
}
```

### Issue: Extension not responding to keyboard shortcuts

**Cause:** Background script communication issues
**Solution:**
```javascript
// Test message passing
chrome.runtime.sendMessage({type: 'GET_SETTINGS'}, response => {
  console.log('Background response:', response);
});
```

## 📊 Expected Console Output

When working correctly, you should see:

```
PiP Master: Background script starting...
PiP Master: Content script starting...
PiP Master: Detecting platform for hostname: 
PiP Master: Using generic platform detection
PiP Master: DOM already ready, creating instance...
PiP Master: Initializing on generic platform
PiP Master: Requesting settings from background...
PiP Master: Settings loaded: ["enabled", "showOverlay", ...]
PiP Master: Starting video detection...
PiP Master: Starting universal video scan...
PiP Master: Basic scan found 4 videos
PiP Master: Processing video 1: {src: "...", readyState: 1, ...}
PiP Master: handleVideoFound called for video: ...
PiP Master: Checking video suitability for: ...
PiP Master: Video passed suitability check: ...
PiP Master: Creating overlay for video...
```

## 🚨 Red Flags

These indicate problems:

- No "Content script starting" message
- "Basic scan found 0 videos" when videos exist
- "Video not suitable for PiP" for all videos
- JavaScript errors in console
- "Instance not available" when running debug commands

## 🔧 Quick Fixes

1. **Reload extension**: Go to chrome://extensions/, click reload
2. **Refresh page**: Hard refresh (Ctrl+Shift+R)
3. **Clear console**: Ctrl+L to see fresh logs
4. **Manual scan**: `window.pipMasterDebug.scanForVideos()`
5. **Check instance**: `console.log(window.pipMasterInstance)`
