// YouTube PiP Fix Verification Script
// Run this in the browser console on YouTube to verify the fixes are working

console.log("🔍 YouTube PiP Fix Verification v1.0");
console.log("=====================================");

// Test 1: Check if we're on YouTube
const isYouTube = window.location.hostname.includes('youtube.com');
console.log(`✓ YouTube detected: ${isYouTube ? '✅' : '❌'}`);

if (!isYouTube) {
    console.log("⚠️ This script should be run on YouTube.com");
    console.log("Navigate to a YouTube video and run this script again.");
}

// Test 2: Check PiP API support
const pipSupported = 'pictureInPictureEnabled' in document;
console.log(`✓ PiP API supported: ${pipSupported ? '✅' : '❌'}`);
console.log(`✓ PiP enabled: ${document.pictureInPictureEnabled ? '✅' : '❌'}`);

// Test 3: Check for video elements
const videos = document.querySelectorAll('video');
console.log(`✓ Video elements found: ${videos.length}`);

if (videos.length > 0) {
    const mainVideo = videos[0];
    console.log("📺 Main video details:");
    console.log(`   - Source: ${mainVideo.src || mainVideo.currentSrc || 'no src'}`);
    console.log(`   - Ready state: ${mainVideo.readyState}`);
    console.log(`   - Dimensions: ${mainVideo.videoWidth}x${mainVideo.videoHeight}`);
    console.log(`   - PiP disabled: ${mainVideo.disablePictureInPicture}`);
    console.log(`   - Paused: ${mainVideo.paused}`);
}

// Test 4: Check extension status
console.log("\n🔧 Extension Status:");
console.log(`✓ PiP Master loaded: ${!!window.pipMasterInstance ? '✅' : '❌'}`);
console.log(`✓ YouTube fix loaded: ${!!window.youtubePiPFix ? '✅' : '❌'}`);

if (window.pipMasterInstance) {
    console.log(`   - Platform: ${window.pipMasterInstance.platform}`);
    console.log(`   - Videos tracked: ${window.pipMasterInstance.videos.size}`);
    console.log(`   - Overlays created: ${window.pipMasterInstance.overlays.size}`);
}

// Test 5: Check for overlays
const overlays = document.querySelectorAll('.pip-master-overlay, .youtube-pip-overlay');
console.log(`✓ PiP overlays found: ${overlays.length}`);

overlays.forEach((overlay, i) => {
    const style = getComputedStyle(overlay);
    console.log(`   Overlay ${i + 1}: display=${style.display}, opacity=${style.opacity}, z-index=${style.zIndex}`);
});

// Test 6: Check for YouTube player
const moviePlayer = document.querySelector('#movie_player');
const htmlPlayer = document.querySelector('.html5-video-player');
console.log(`✓ YouTube movie player: ${!!moviePlayer ? '✅' : '❌'}`);
console.log(`✓ HTML5 player: ${!!htmlPlayer ? '✅' : '❌'}`);

if (moviePlayer) {
    console.log(`   - Classes: ${moviePlayer.className}`);
    console.log(`   - Ad showing: ${moviePlayer.classList.contains('ad-showing')}`);
}

// Test 7: Test PiP functionality
console.log("\n🧪 Testing PiP Functionality:");

if (videos.length > 0 && pipSupported) {
    const testVideo = videos[0];
    
    console.log("Testing PiP on main video...");
    
    testVideo.requestPictureInPicture()
        .then(() => {
            console.log("✅ PiP test successful!");
            
            // Exit PiP after 2 seconds
            setTimeout(() => {
                if (document.pictureInPictureElement) {
                    document.exitPictureInPicture()
                        .then(() => console.log("✅ PiP exit successful!"))
                        .catch(err => console.log("❌ PiP exit failed:", err.message));
                }
            }, 2000);
        })
        .catch(error => {
            console.log("❌ PiP test failed:", error.message);
            
            // Provide specific troubleshooting
            if (error.name === 'NotAllowedError') {
                console.log("💡 Suggestion: This might be a YouTube policy restriction");
            } else if (error.message.includes('disabled')) {
                console.log("💡 Suggestion: PiP is disabled on this video element");
            } else if (error.name === 'InvalidStateError') {
                console.log("💡 Suggestion: Video might not be fully loaded yet");
            }
        });
} else {
    console.log("⏭️ Skipping PiP test (no videos or PiP not supported)");
}

// Test 8: Manual fix application
console.log("\n🔧 Manual Fix Commands:");
console.log("If overlays are missing, try these commands:");
console.log("");
console.log("// Force video scan");
console.log("if (window.pipMasterInstance) window.pipMasterInstance.performUniversalVideoScan();");
console.log("");
console.log("// Load YouTube fix");
console.log("const script = document.createElement('script');");
console.log("script.src = 'youtube-pip-fix.js';");
console.log("document.head.appendChild(script);");
console.log("");
console.log("// Create manual overlay");
console.log("const video = document.querySelector('video');");
console.log("if (video && window.pipMasterInstance) {");
console.log("    window.pipMasterInstance.handleVideoFound(video);");
console.log("}");

// Summary
setTimeout(() => {
    console.log("\n📊 Verification Summary:");
    console.log("========================");
    
    const checks = [
        { name: "YouTube page", status: isYouTube },
        { name: "PiP API support", status: pipSupported },
        { name: "Videos found", status: videos.length > 0 },
        { name: "Extension loaded", status: !!window.pipMasterInstance },
        { name: "Overlays present", status: overlays.length > 0 },
        { name: "YouTube player", status: !!moviePlayer || !!htmlPlayer }
    ];
    
    checks.forEach(check => {
        console.log(`${check.status ? '✅' : '❌'} ${check.name}`);
    });
    
    const passedChecks = checks.filter(c => c.status).length;
    const totalChecks = checks.length;
    
    console.log(`\n🎯 Overall Status: ${passedChecks}/${totalChecks} checks passed`);
    
    if (passedChecks === totalChecks) {
        console.log("🎉 All checks passed! YouTube PiP should be working correctly.");
    } else if (passedChecks >= totalChecks - 2) {
        console.log("⚠️ Most checks passed. Minor issues may exist but PiP should work.");
    } else {
        console.log("❌ Several issues detected. Check the troubleshooting guide.");
    }
    
    console.log("\n💡 Next Steps:");
    if (!isYouTube) {
        console.log("1. Navigate to a YouTube video page");
    } else if (!window.pipMasterInstance) {
        console.log("1. Check if the extension is loaded in chrome://extensions/");
        console.log("2. Refresh the page and try again");
    } else if (overlays.length === 0) {
        console.log("1. Try refreshing the page");
        console.log("2. Run the manual fix commands above");
        console.log("3. Check for YouTube ads (wait for them to finish)");
    } else {
        console.log("1. Look for the PiP button (📺) on the video");
        console.log("2. Click it or press Alt+P to activate PiP");
        console.log("3. If it doesn't work, check the error messages above");
    }
}, 3000);

console.log("\n⏳ Running verification tests... (will take ~3 seconds)");
