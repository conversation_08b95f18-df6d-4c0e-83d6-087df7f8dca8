{"manifest_version": 3, "name": "PiP Master (Debug)", "version": "1.0.1", "description": "Universal Picture-in-Picture extension - Debug Version", "author": "PiP Master Team", "permissions": ["storage", "activeTab", "scripting"], "host_permissions": ["https://*/*", "http://*/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content/content-simple.js"], "css": ["content/content.css"], "run_at": "document_end", "all_frames": false}], "action": {"default_popup": "popup/popup.html", "default_title": "PiP Master (Debug)"}, "options_page": "options/options.html", "commands": {"toggle-pip": {"suggested_key": {"default": "Alt+P", "mac": "Alt+P"}, "description": "Toggle Picture-in-Picture mode"}}, "web_accessible_resources": [{"resources": ["content/*"], "matches": ["<all_urls>"]}]}