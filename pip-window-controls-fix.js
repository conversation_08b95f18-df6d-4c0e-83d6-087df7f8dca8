// PiP Window Controls Fix - Volume and Timeline Controls for PiP Window
// Focused fix for missing controls within the Picture-in-Picture window

console.log("🔧 PiP Window Controls Diagnostic & Fix");
console.log("=======================================");

// PiP Window Controls Manager
class PiPWindowControlsFixer {
  constructor() {
    this.pipVideo = null;
    this.volumeControlsActive = false;
    this.timelineControlsActive = false;
    this.pipEventListeners = [];
    this.runPiPControlsDiagnostic();
  }

  runPiPControlsDiagnostic() {
    console.log("🚀 Diagnosing PiP window controls...");
    
    this.checkPiPStatus();
    this.diagnoseVolumeControls();
    this.diagnoseTimelineControls();
    this.checkSettingsIntegration();
    this.applyPiPControlsFixes();
    this.setupPiPEventHandling();
  }

  checkPiPStatus() {
    console.log("\n1️⃣ Checking PiP Status");
    console.log("======================");
    
    this.pipVideo = document.pictureInPictureElement;
    const pipActive = !!this.pipVideo;
    
    console.log(`PiP active: ${pipActive ? '✅' : '❌'}`);
    
    if (pipActive) {
      console.log(`PiP video element: ${this.pipVideo.tagName}`);
      console.log(`PiP video duration: ${this.pipVideo.duration}`);
      console.log(`PiP video volume: ${this.pipVideo.volume}`);
      console.log(`PiP video muted: ${this.pipVideo.muted}`);
    } else {
      console.log("⚠️ No video in PiP mode - activate PiP to test controls");
      // Try to find a video to test with
      const video = document.querySelector('video');
      if (video) {
        console.log("💡 Found video - you can activate PiP with Alt+P or click PiP button");
      }
    }
  }

  diagnoseVolumeControls() {
    console.log("\n2️⃣ Diagnosing Volume Controls");
    console.log("=============================");
    
    const settings = window.pipMasterInstance?.settingsPanel?.settings;
    const volumeEnabled = settings?.volumeControlEnabled !== false;
    const audioControlEnabled = settings?.audioControlEnabled !== false;
    
    console.log(`Volume controls enabled in settings: ${volumeEnabled ? '✅' : '❌'}`);
    console.log(`Audio control enabled in settings: ${audioControlEnabled ? '✅' : '❌'}`);
    
    // Check if volume control listeners exist
    const hasVolumeListeners = this.checkForVolumeListeners();
    console.log(`Volume keyboard listeners active: ${hasVolumeListeners ? '✅' : '❌'}`);
    
    if (!hasVolumeListeners || !volumeEnabled) {
      console.log("🔧 Fix needed: Volume controls not properly set up for PiP window");
    }
  }

  diagnoseTimelineControls() {
    console.log("\n3️⃣ Diagnosing Timeline Controls");
    console.log("===============================");
    
    const settings = window.pipMasterInstance?.settingsPanel?.settings;
    const timelineEnabled = settings?.timelinePreviewEnabled !== false;
    const timelineControl = window.pipMasterInstance?.timelineControl;
    
    console.log(`Timeline preview enabled in settings: ${timelineEnabled ? '✅' : '❌'}`);
    console.log(`Timeline control system exists: ${!!timelineControl ? '✅' : '❌'}`);
    
    // Check for timeline elements in PiP context
    const timelineElement = document.getElementById('pip-timeline-control');
    console.log(`Timeline element exists: ${!!timelineElement ? '✅' : '❌'}`);
    
    if (timelineControl) {
      const hasHoverPreview = typeof timelineControl.enableHoverPreview === 'function';
      console.log(`Timeline hover preview method: ${hasHoverPreview ? '✅' : '❌'}`);
    }
    
    if (!timelineEnabled || !timelineControl) {
      console.log("🔧 Fix needed: Timeline controls not properly set up for PiP window");
    }
  }

  checkSettingsIntegration() {
    console.log("\n4️⃣ Checking Settings Integration");
    console.log("================================");
    
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    
    if (!settingsPanel) {
      console.log("❌ Settings panel not available");
      return;
    }
    
    // Check if settings apply methods exist
    const hasAudioApply = typeof settingsPanel.applyAudioControlSettings === 'function';
    const hasTimelineApply = typeof settingsPanel.applyTimelinePreviewSettings === 'function';
    
    console.log(`Audio control apply method: ${hasAudioApply ? '✅' : '❌'}`);
    console.log(`Timeline preview apply method: ${hasTimelineApply ? '✅' : '❌'}`);
    
    // Test settings application
    try {
      if (hasAudioApply) settingsPanel.applyAudioControlSettings();
      if (hasTimelineApply) settingsPanel.applyTimelinePreviewSettings();
      console.log("✅ Settings application test passed");
    } catch (error) {
      console.log("❌ Settings application test failed:", error.message);
    }
  }

  checkForVolumeListeners() {
    // Check if volume control listeners are active
    return document.querySelector('[data-pip-volume-listener]') !== null;
  }

  applyPiPControlsFixes() {
    console.log("\n🔧 APPLYING PIP CONTROLS FIXES");
    console.log("==============================");
    
    this.setupPiPVolumeControls();
    this.setupPiPTimelineControls();
    this.integrateWithSettings();
  }

  setupPiPVolumeControls() {
    console.log("🔊 Setting up PiP volume controls...");
    
    // Remove existing volume listeners
    this.removePiPEventListeners();
    
    // Create volume control handler
    const volumeHandler = (event) => {
      const pipVideo = document.pictureInPictureElement;
      if (!pipVideo) return;
      
      // Check if Ctrl key is pressed with arrow keys
      if (event.ctrlKey && (event.key === 'ArrowUp' || event.key === 'ArrowDown')) {
        event.preventDefault();
        event.stopPropagation();
        
        const volumeChange = event.key === 'ArrowUp' ? 0.1 : -0.1;
        const newVolume = Math.max(0, Math.min(1, pipVideo.volume + volumeChange));
        
        pipVideo.volume = newVolume;
        pipVideo.muted = false; // Unmute when adjusting volume
        
        console.log(`🔊 PiP volume adjusted: ${Math.round(newVolume * 100)}%`);
        
        // Show volume indicator
        this.showVolumeIndicator(newVolume);
      }
    };
    
    // Add volume control listener
    document.addEventListener('keydown', volumeHandler, true);
    this.pipEventListeners.push({ type: 'keydown', handler: volumeHandler, element: document });
    
    // Mark as active
    if (!document.querySelector('[data-pip-volume-listener]')) {
      const marker = document.createElement('div');
      marker.setAttribute('data-pip-volume-listener', 'true');
      marker.style.display = 'none';
      document.body.appendChild(marker);
    }
    
    this.volumeControlsActive = true;
    console.log("✅ PiP volume controls activated (Ctrl + ↑/↓)");
  }

  setupPiPTimelineControls() {
    console.log("⏯️ Setting up PiP timeline controls...");
    
    // Create timeline control system for PiP
    if (!window.pipMasterInstance.timelineControl) {
      window.pipMasterInstance.timelineControl = {};
    }
    
    const timelineControl = window.pipMasterInstance.timelineControl;
    
    // Enhanced timeline control methods
    timelineControl.showInPiP = () => {
      const pipVideo = document.pictureInPictureElement;
      if (!pipVideo) return;
      
      this.createPiPTimelineElement(pipVideo);
    };
    
    timelineControl.hideInPiP = () => {
      const timelineElement = document.getElementById('pip-timeline-overlay');
      if (timelineElement) {
        timelineElement.style.opacity = '0';
        setTimeout(() => timelineElement.remove(), 300);
      }
    };
    
    timelineControl.toggle = () => {
      const existing = document.getElementById('pip-timeline-overlay');
      if (existing && existing.style.opacity === '1') {
        timelineControl.hideInPiP();
      } else {
        timelineControl.showInPiP();
      }
    };
    
    // Set up hover detection for PiP window
    timelineControl.enableHoverPreview = () => {
      this.setupPiPHoverDetection();
      console.log("✅ PiP timeline hover preview enabled");
    };
    
    timelineControl.disableHoverPreview = () => {
      this.removePiPHoverDetection();
      console.log("✅ PiP timeline hover preview disabled");
    };
    
    // Alt+T keyboard shortcut for timeline
    const timelineKeyHandler = (event) => {
      if (event.altKey && event.key.toLowerCase() === 't') {
        event.preventDefault();
        if (document.pictureInPictureElement) {
          timelineControl.toggle();
          console.log("⏯️ PiP timeline toggled (Alt+T)");
        }
      }
    };
    
    document.addEventListener('keydown', timelineKeyHandler, true);
    this.pipEventListeners.push({ type: 'keydown', handler: timelineKeyHandler, element: document });
    
    this.timelineControlsActive = true;
    console.log("✅ PiP timeline controls activated (Alt+T and hover)");
  }

  createPiPTimelineElement(pipVideo) {
    // Remove existing timeline
    const existing = document.getElementById('pip-timeline-overlay');
    if (existing) existing.remove();
    
    // Create timeline overlay
    const timeline = document.createElement('div');
    timeline.id = 'pip-timeline-overlay';
    timeline.style.cssText = `
      position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.9); color: white; padding: 12px 20px;
      border-radius: 8px; font-family: Arial, sans-serif; font-size: 14px;
      z-index: 10001; opacity: 1; transition: opacity 0.3s ease;
      backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.3);
      pointer-events: auto; user-select: none;
    `;
    
    // Timeline content
    const currentTime = Math.floor(pipVideo.currentTime);
    const duration = Math.floor(pipVideo.duration);
    const progress = (pipVideo.currentTime / pipVideo.duration) * 100;
    
    timeline.innerHTML = `
      <div style="margin-bottom: 8px; text-align: center;">
        ⏯️ ${this.formatTime(currentTime)} / ${this.formatTime(duration)}
      </div>
      <div style="width: 200px; height: 4px; background: rgba(255,255,255,0.3); border-radius: 2px; position: relative; cursor: pointer;" id="pip-progress-bar">
        <div style="width: ${progress}%; height: 100%; background: #ff0000; border-radius: 2px; transition: width 0.1s;"></div>
      </div>
      <div style="margin-top: 8px; font-size: 12px; opacity: 0.8; text-align: center;">
        Click to seek • Alt+T to hide
      </div>
    `;
    
    // Add seek functionality
    const progressBar = timeline.querySelector('#pip-progress-bar');
    progressBar.addEventListener('click', (e) => {
      const rect = progressBar.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const percentage = clickX / rect.width;
      const newTime = percentage * pipVideo.duration;
      
      pipVideo.currentTime = newTime;
      console.log(`⏯️ PiP video seeked to ${this.formatTime(Math.floor(newTime))}`);
    });
    
    document.body.appendChild(timeline);
    
    // Auto-update timeline
    const updateInterval = setInterval(() => {
      if (!document.pictureInPictureElement || !document.getElementById('pip-timeline-overlay')) {
        clearInterval(updateInterval);
        return;
      }
      
      const currentTime = Math.floor(pipVideo.currentTime);
      const progress = (pipVideo.currentTime / pipVideo.duration) * 100;
      
      const timeDisplay = timeline.querySelector('div');
      const progressFill = timeline.querySelector('#pip-progress-bar div');
      
      if (timeDisplay) {
        timeDisplay.innerHTML = `⏯️ ${this.formatTime(currentTime)} / ${this.formatTime(duration)}`;
      }
      if (progressFill) {
        progressFill.style.width = `${progress}%`;
      }
    }, 1000);
  }

  setupPiPHoverDetection() {
    // This would require access to PiP window events, which is limited
    // For now, we'll use Alt+T as the primary method
    console.log("💡 PiP hover detection limited by browser security - use Alt+T to toggle timeline");
  }

  removePiPHoverDetection() {
    // Remove hover detection if implemented
  }

  integrateWithSettings() {
    console.log("🔗 Integrating with settings panel...");
    
    const settingsPanel = window.pipMasterInstance?.settingsPanel;
    if (!settingsPanel) return;
    
    // Override apply methods to include PiP controls
    const originalApplyAudio = settingsPanel.applyAudioControlSettings;
    settingsPanel.applyAudioControlSettings = () => {
      if (originalApplyAudio) originalApplyAudio.call(settingsPanel);
      
      // Apply to PiP controls
      if (settingsPanel.settings.audioControlEnabled && settingsPanel.settings.volumeControlEnabled) {
        if (!this.volumeControlsActive) {
          this.setupPiPVolumeControls();
        }
      }
    };
    
    const originalApplyTimeline = settingsPanel.applyTimelinePreviewSettings;
    settingsPanel.applyTimelinePreviewSettings = () => {
      if (originalApplyTimeline) originalApplyTimeline.call(settingsPanel);
      
      // Apply to PiP controls
      if (settingsPanel.settings.timelinePreviewEnabled) {
        if (!this.timelineControlsActive) {
          this.setupPiPTimelineControls();
        }
        window.pipMasterInstance.timelineControl.enableHoverPreview();
      } else {
        window.pipMasterInstance.timelineControl.disableHoverPreview();
      }
    };
    
    console.log("✅ Settings integration complete");
  }

  showVolumeIndicator(volume) {
    // Remove existing indicator
    const existing = document.getElementById('pip-volume-indicator');
    if (existing) existing.remove();
    
    // Create volume indicator
    const indicator = document.createElement('div');
    indicator.id = 'pip-volume-indicator';
    indicator.style.cssText = `
      position: fixed; top: 20px; right: 20px; z-index: 10002;
      background: rgba(0, 0, 0, 0.9); color: white; padding: 12px 16px;
      border-radius: 8px; font-family: Arial, sans-serif; font-size: 16px;
      backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.3);
    `;
    
    const volumePercent = Math.round(volume * 100);
    const volumeIcon = volume === 0 ? '🔇' : volume < 0.5 ? '🔉' : '🔊';
    
    indicator.innerHTML = `${volumeIcon} ${volumePercent}%`;
    
    document.body.appendChild(indicator);
    
    // Auto-remove after 2 seconds
    setTimeout(() => {
      if (indicator.parentNode) {
        indicator.style.opacity = '0';
        setTimeout(() => indicator.remove(), 300);
      }
    }, 2000);
  }

  formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  removePiPEventListeners() {
    this.pipEventListeners.forEach(({ type, handler, element }) => {
      element.removeEventListener(type, handler, true);
    });
    this.pipEventListeners = [];
    
    // Remove volume listener marker
    const marker = document.querySelector('[data-pip-volume-listener]');
    if (marker) marker.remove();
  }

  generateReport() {
    console.log("\n📊 PIP CONTROLS STATUS REPORT");
    console.log("=============================");
    
    const pipActive = !!document.pictureInPictureElement;
    
    console.log(`📺 PiP Status: ${pipActive ? '✅ Active' : '❌ Inactive'}`);
    console.log(`🔊 Volume Controls: ${this.volumeControlsActive ? '✅ Active' : '❌ Inactive'}`);
    console.log(`⏯️ Timeline Controls: ${this.timelineControlsActive ? '✅ Active' : '❌ Inactive'}`);
    
    if (pipActive) {
      console.log("\n🎮 Available PiP Controls:");
      console.log("- Ctrl + ↑/↓: Volume adjustment");
      console.log("- Alt + T: Toggle timeline");
      console.log("- Click timeline: Seek to position");
    } else {
      console.log("\n💡 To test PiP controls:");
      console.log("1. Activate PiP mode (Alt+P or click PiP button)");
      console.log("2. Try Ctrl + ↑/↓ for volume");
      console.log("3. Try Alt + T for timeline");
    }
  }
}

// Quick test commands
window.testPiPVolumeControls = function() {
  console.log("🧪 Testing PiP Volume Controls");
  console.log("==============================");
  
  const pipVideo = document.pictureInPictureElement;
  if (!pipVideo) {
    console.log("❌ No video in PiP mode - activate PiP first");
    return false;
  }
  
  console.log("✅ PiP video found");
  console.log("🎮 Test: Press Ctrl + ↑ or Ctrl + ↓ to adjust volume");
  console.log(`Current volume: ${Math.round(pipVideo.volume * 100)}%`);
  
  return true;
};

window.testPiPTimelineControls = function() {
  console.log("🧪 Testing PiP Timeline Controls");
  console.log("================================");
  
  const pipVideo = document.pictureInPictureElement;
  if (!pipVideo) {
    console.log("❌ No video in PiP mode - activate PiP first");
    return false;
  }
  
  console.log("✅ PiP video found");
  console.log("🎮 Test: Press Alt + T to toggle timeline");
  
  // Test timeline toggle
  if (window.pipMasterInstance?.timelineControl?.toggle) {
    window.pipMasterInstance.timelineControl.toggle();
    console.log("✅ Timeline toggle test executed");
  } else {
    console.log("❌ Timeline control not available");
  }
  
  return true;
};

window.fixPiPControls = function() {
  console.log("🔧 Quick PiP Controls Fix");
  console.log("=========================");
  
  new PiPWindowControlsFixer();
  
  console.log("✅ PiP controls fix applied");
  console.log("🎮 Test with: testPiPVolumeControls() and testPiPTimelineControls()");
};

// Auto-run fix
console.log("🚀 Auto-running PiP controls diagnostic...");
window.pipControlsFixer = new PiPWindowControlsFixer();
window.pipControlsFixer.generateReport();

console.log("\n📋 PiP Controls Commands:");
console.log("=========================");
console.log("testPiPVolumeControls()   - Test volume controls in PiP");
console.log("testPiPTimelineControls() - Test timeline controls in PiP");
console.log("fixPiPControls()          - Apply PiP controls fix");
