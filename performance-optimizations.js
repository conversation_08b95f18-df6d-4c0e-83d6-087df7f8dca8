// Performance Optimizations and Cross-Platform Enhancements
// Intelligent detection and battery-efficient operation

console.log("⚡ Performance Optimizations & Cross-Platform Support");
console.log("====================================================");

// Enhancement 6: Performance Optimizer
class PerformanceOptimizer {
  constructor(pipMaster) {
    this.pipMaster = pipMaster;
    this.isLowPowerMode = false;
    this.detectionFrequency = 'normal'; // normal, reduced, minimal
    this.lastScanTime = 0;
    this.scanThrottle = 2000; // Default 2 seconds
    this.setupPerformanceMonitoring();
  }

  setupPerformanceMonitoring() {
    this.detectBatteryStatus();
    this.setupIntelligentThrottling();
    this.setupMemoryOptimization();
    this.setupIdleDetection();
  }

  async detectBatteryStatus() {
    try {
      if ('getBattery' in navigator) {
        const battery = await navigator.getBattery();
        
        this.updatePowerMode(battery);
        
        // Listen for battery changes
        battery.addEventListener('levelchange', () => this.updatePowerMode(battery));
        battery.addEventListener('chargingchange', () => this.updatePowerMode(battery));
        
        console.log(`🔋 Battery: ${Math.round(battery.level * 100)}%, Charging: ${battery.charging}`);
      }
    } catch (error) {
      console.log("Battery API not available");
    }
  }

  updatePowerMode(battery) {
    const wasLowPower = this.isLowPowerMode;
    
    // Enable low power mode if battery is low and not charging
    this.isLowPowerMode = battery.level < 0.2 && !battery.charging;
    
    if (this.isLowPowerMode !== wasLowPower) {
      if (this.isLowPowerMode) {
        this.enableLowPowerMode();
      } else {
        this.disableLowPowerMode();
      }
    }
  }

  enableLowPowerMode() {
    console.log("🔋 Enabling low power mode");
    
    // Reduce scan frequency
    this.scanThrottle = 10000; // 10 seconds
    this.detectionFrequency = 'minimal';
    
    // Disable auto-PiP to save battery
    if (this.pipMaster.smartAutoPiP) {
      this.pipMaster.smartAutoPiP.disable();
    }
    
    // Reduce overlay animations
    this.addLowPowerStyles();
  }

  disableLowPowerMode() {
    console.log("⚡ Disabling low power mode");
    
    // Restore normal scan frequency
    this.scanThrottle = 2000; // 2 seconds
    this.detectionFrequency = 'normal';
    
    // Remove low power styles
    this.removeLowPowerStyles();
  }

  addLowPowerStyles() {
    const style = document.createElement('style');
    style.id = 'pip-master-low-power';
    style.textContent = `
      .pip-master-overlay {
        animation: none !important;
        transition: opacity 0.1s ease !important;
      }
      
      .pip-master-overlay:hover {
        transition: opacity 0.1s ease !important;
      }
    `;
    document.head.appendChild(style);
  }

  removeLowPowerStyles() {
    const style = document.getElementById('pip-master-low-power');
    if (style) {
      style.remove();
    }
  }

  setupIntelligentThrottling() {
    // Throttle video scanning based on activity
    const originalScan = this.pipMaster.performUniversalVideoScan.bind(this.pipMaster);
    
    this.pipMaster.performUniversalVideoScan = () => {
      const now = Date.now();
      
      // Check if enough time has passed
      if (now - this.lastScanTime < this.scanThrottle) {
        console.log("⏭️ Scan throttled for performance");
        return;
      }
      
      this.lastScanTime = now;
      
      // Use requestIdleCallback for better performance
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
          originalScan();
        }, { timeout: 1000 });
      } else {
        // Fallback for browsers without requestIdleCallback
        setTimeout(originalScan, 0);
      }
    };
  }

  setupMemoryOptimization() {
    // Clean up unused overlays periodically
    setInterval(() => {
      this.cleanupUnusedOverlays();
    }, 30000); // Every 30 seconds
    
    // Monitor memory usage
    if ('memory' in performance) {
      setInterval(() => {
        this.checkMemoryUsage();
      }, 60000); // Every minute
    }
  }

  cleanupUnusedOverlays() {
    const overlays = document.querySelectorAll('.pip-master-overlay');
    let cleaned = 0;
    
    overlays.forEach(overlay => {
      // Find associated video
      const container = overlay.parentElement;
      const video = container ? container.querySelector('video') : null;
      
      // Remove overlay if video is gone or not suitable
      if (!video || !this.pipMaster.isVideoSuitableForPiP(video)) {
        overlay.remove();
        cleaned++;
      }
    });
    
    if (cleaned > 0) {
      console.log(`🧹 Cleaned up ${cleaned} unused overlays`);
    }
  }

  checkMemoryUsage() {
    if ('memory' in performance) {
      const memory = performance.memory;
      const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
      const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
      
      console.log(`💾 Memory usage: ${usedMB}MB / ${limitMB}MB`);
      
      // If memory usage is high, trigger cleanup
      if (usedMB > limitMB * 0.8) {
        console.log("🧹 High memory usage detected, triggering cleanup");
        this.performMemoryCleanup();
      }
    }
  }

  performMemoryCleanup() {
    // Clear unused overlays
    this.cleanupUnusedOverlays();
    
    // Clear cached data
    if (this.pipMaster.videos) {
      const validVideos = new Set();
      this.pipMaster.videos.forEach(video => {
        if (document.contains(video)) {
          validVideos.add(video);
        }
      });
      this.pipMaster.videos = validVideos;
    }
    
    // Force garbage collection if available
    if ('gc' in window) {
      window.gc();
    }
  }

  setupIdleDetection() {
    let idleTimer;
    let isIdle = false;
    const idleTime = 60000; // 1 minute
    
    const resetIdleTimer = () => {
      clearTimeout(idleTimer);
      
      if (isIdle) {
        isIdle = false;
        this.onUserActive();
      }
      
      idleTimer = setTimeout(() => {
        isIdle = true;
        this.onUserIdle();
      }, idleTime);
    };
    
    // Reset timer on user activity
    ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
      document.addEventListener(event, resetIdleTimer, { passive: true });
    });
    
    resetIdleTimer();
  }

  onUserIdle() {
    console.log("😴 User idle detected - reducing activity");
    
    // Reduce scan frequency when idle
    this.scanThrottle = Math.max(this.scanThrottle * 2, 10000);
    
    // Pause non-essential features
    this.detectionFrequency = 'reduced';
  }

  onUserActive() {
    console.log("👋 User active - resuming normal activity");
    
    // Restore normal scan frequency
    this.scanThrottle = this.isLowPowerMode ? 10000 : 2000;
    this.detectionFrequency = this.isLowPowerMode ? 'minimal' : 'normal';
  }

  getPerformanceStats() {
    const stats = {
      lowPowerMode: this.isLowPowerMode,
      detectionFrequency: this.detectionFrequency,
      scanThrottle: this.scanThrottle,
      lastScanTime: new Date(this.lastScanTime).toLocaleTimeString()
    };
    
    if ('memory' in performance) {
      const memory = performance.memory;
      stats.memoryUsage = {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB',
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
      };
    }
    
    return stats;
  }
}

// Enhancement 7: Cross-Platform Video Detector
class CrossPlatformDetector {
  constructor(pipMaster) {
    this.pipMaster = pipMaster;
    this.platformProfiles = this.definePlatformProfiles();
    this.setupPlatformSpecificOptimizations();
  }

  definePlatformProfiles() {
    return {
      // Video Streaming Services
      netflix: {
        selectors: ['video', '.VideoContainer video', '.watch-video video'],
        waitForLoad: true,
        drmWarning: true,
        skipAds: false
      },
      
      hulu: {
        selectors: ['video', '.video-player video', '.player-container video'],
        waitForLoad: true,
        skipAds: true
      },
      
      disney: {
        selectors: ['video', '.btm-media-client-element video'],
        waitForLoad: true,
        drmWarning: true
      },
      
      amazon: {
        selectors: ['video', '.webPlayerContainer video', '.webPlayerElement video'],
        waitForLoad: true,
        drmWarning: true
      },
      
      // Social Media Platforms
      instagram: {
        selectors: ['video', '[role="presentation"] video', '.x1lliihq video'],
        skipAds: false,
        autoDetect: true
      },
      
      facebook: {
        selectors: ['video', '[data-video-id] video', '.videoStage video'],
        skipAds: true,
        autoDetect: true
      },
      
      twitter: {
        selectors: ['video', '[data-testid="videoPlayer"] video', '.PlayableMedia-player video'],
        skipAds: false,
        autoDetect: true
      },
      
      tiktok: {
        selectors: ['video', '.video-player video', '[data-e2e="video-player"] video'],
        skipAds: false,
        autoDetect: true
      },
      
      // Video Conferencing
      zoom: {
        selectors: ['video', '.video-object video', '.video-container video'],
        skipAds: false,
        conferenceMode: true
      },
      
      teams: {
        selectors: ['video', '.video-stream video', '[data-tid="video-container"] video'],
        skipAds: false,
        conferenceMode: true
      },
      
      meet: {
        selectors: ['video', '[data-allocation-index] video', '.N1LNQB video'],
        skipAds: false,
        conferenceMode: true
      },
      
      // Educational Platforms
      coursera: {
        selectors: ['video', '.video-player video', '.vjs-tech video'],
        waitForLoad: true,
        skipAds: false
      },
      
      udemy: {
        selectors: ['video', '.video-player video', '.vjs-tech video'],
        waitForLoad: true,
        skipAds: false
      },
      
      khan: {
        selectors: ['video', '.video-player video'],
        waitForLoad: true,
        skipAds: false
      }
    };
  }

  setupPlatformSpecificOptimizations() {
    const hostname = window.location.hostname.replace(/^(www\.|m\.|mobile\.)/, '');
    const platform = this.detectPlatform(hostname);
    
    if (platform && this.platformProfiles[platform]) {
      this.applyPlatformOptimizations(platform, this.platformProfiles[platform]);
    }
  }

  detectPlatform(hostname) {
    const platformMap = {
      'netflix.com': 'netflix',
      'hulu.com': 'hulu',
      'disneyplus.com': 'disney',
      'primevideo.com': 'amazon',
      'amazon.com': 'amazon',
      'instagram.com': 'instagram',
      'facebook.com': 'facebook',
      'twitter.com': 'twitter',
      'x.com': 'twitter',
      'tiktok.com': 'tiktok',
      'zoom.us': 'zoom',
      'teams.microsoft.com': 'teams',
      'meet.google.com': 'meet',
      'coursera.org': 'coursera',
      'udemy.com': 'udemy',
      'khanacademy.org': 'khan'
    };
    
    return Object.keys(platformMap).find(domain => hostname.includes(domain)) 
           ? platformMap[Object.keys(platformMap).find(domain => hostname.includes(domain))]
           : null;
  }

  applyPlatformOptimizations(platform, profile) {
    console.log(`🎯 Applying optimizations for ${platform}`);
    
    // DRM warning for streaming services
    if (profile.drmWarning) {
      this.setupDRMWarning();
    }
    
    // Conference mode optimizations
    if (profile.conferenceMode) {
      this.setupConferenceModeOptimizations();
    }
    
    // Auto-detect optimizations
    if (profile.autoDetect) {
      this.setupAutoDetectOptimizations();
    }
    
    // Platform-specific selectors
    if (profile.selectors) {
      this.enhanceVideoDetection(profile.selectors);
    }
  }

  setupDRMWarning() {
    // Show warning for DRM-protected content
    document.addEventListener('enterpictureinpicture', (event) => {
      const video = event.target;
      if (video.src && video.src.startsWith('blob:')) {
        this.pipMaster.accessibility.announceToScreenReader(
          "This content may be DRM-protected. Picture-in-Picture may not work on all videos."
        );
      }
    });
  }

  setupConferenceModeOptimizations() {
    // Optimize for video conferencing
    console.log("📹 Conference mode optimizations enabled");
    
    // Prioritize larger videos (main speaker)
    this.pipMaster.conferenceMode = true;
    
    // Reduce scan frequency to avoid disrupting calls
    if (this.pipMaster.performanceOptimizer) {
      this.pipMaster.performanceOptimizer.scanThrottle = 5000; // 5 seconds
    }
  }

  setupAutoDetectOptimizations() {
    // Enhanced auto-detection for social media
    console.log("🔍 Auto-detect optimizations enabled");
    
    // More frequent scanning for dynamic content
    if (this.pipMaster.performanceOptimizer) {
      this.pipMaster.performanceOptimizer.scanThrottle = 1000; // 1 second
    }
  }

  enhanceVideoDetection(selectors) {
    // Add platform-specific selectors to detection
    const originalScan = this.pipMaster.performUniversalVideoScan.bind(this.pipMaster);
    
    this.pipMaster.performUniversalVideoScan = () => {
      // Run original scan
      originalScan();
      
      // Add platform-specific detection
      selectors.forEach(selector => {
        try {
          const videos = document.querySelectorAll(selector);
          videos.forEach(video => {
            if (video.tagName === 'VIDEO' && !this.pipMaster.videos.has(video)) {
              this.pipMaster.handleVideoFound(video);
            }
          });
        } catch (error) {
          console.warn(`Platform selector "${selector}" failed:`, error);
        }
      });
    };
  }

  getPlatformInfo() {
    const hostname = window.location.hostname.replace(/^(www\.|m\.|mobile\.)/, '');
    const platform = this.detectPlatform(hostname);
    
    return {
      hostname: hostname,
      detectedPlatform: platform,
      profile: platform ? this.platformProfiles[platform] : null,
      optimizationsApplied: !!platform
    };
  }
}

// Initialize performance and cross-platform features
window.initializeAdvancedFeatures = function() {
  console.log("⚡ Initializing Advanced Features");
  
  if (!window.pipMasterInstance) {
    console.error("❌ PiP Master instance not available");
    return false;
  }
  
  // Initialize Performance Optimizer
  window.pipMasterInstance.performanceOptimizer = new PerformanceOptimizer(window.pipMasterInstance);
  
  // Initialize Cross-Platform Detector
  window.pipMasterInstance.crossPlatformDetector = new CrossPlatformDetector(window.pipMasterInstance);
  
  console.log("✅ Advanced features initialized");
  
  // Show platform info
  const platformInfo = window.pipMasterInstance.crossPlatformDetector.getPlatformInfo();
  console.log("🌐 Platform Info:", platformInfo);
  
  return true;
};

// Auto-initialize
if (window.pipMasterInstance) {
  window.initializeAdvancedFeatures();
} else {
  setTimeout(() => {
    if (window.pipMasterInstance) {
      window.initializeAdvancedFeatures();
    }
  }, 2000);
}

console.log("\n📋 Advanced Features Commands:");
console.log("==============================");
console.log("initializeAdvancedFeatures()                                    - Initialize advanced features");
console.log("pipMasterInstance.performanceOptimizer.getPerformanceStats()    - View performance stats");
console.log("pipMasterInstance.crossPlatformDetector.getPlatformInfo()       - View platform info");
