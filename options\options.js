// PiP Master Options Page Script
// Handles the settings/options page functionality

class PiPMasterOptions {
  constructor() {
    this.settings = {};
    this.defaultSettings = {
      enabled: true,
      showOverlay: true,
      overlayPosition: "top-right",
      opacity: 0.9,
      snapToCorners: true,
      theme: "auto",
      shortcuts: {
        togglePip: "Alt+P",
        increaseOpacity: "Alt+.",
        decreaseOpacity: "Alt+,",
      },
      audioControls: true,
      alwaysOnTop: true,
      resizable: true,
      detectionSensitivity: "medium",
      minVideoSize: 100,
    };

    this.init();
  }

  async init() {
    try {
      // Load current settings
      await this.loadSettings();

      // Set up event listeners
      this.setupEventListeners();

      // Update UI with current settings
      this.updateUI();

      console.log("PiP Master: Options page initialized");
    } catch (error) {
      console.error("PiP Master: Failed to initialize options page:", error);
    }
  }

  async loadSettings() {
    try {
      const response = await this.sendMessage({ type: "GET_SETTINGS" });
      this.settings = { ...this.defaultSettings, ...response.data };
    } catch (error) {
      console.error("PiP Master: Failed to load settings:", error);
      this.settings = { ...this.defaultSettings };
    }
  }

  setupEventListeners() {
    // Checkbox settings
    const checkboxes = [
      "enabled",
      "showOverlay",
      "snapToCorners",
      "alwaysOnTop",
      "resizable",
      "audioControls",
    ];

    checkboxes.forEach((id) => {
      const element = document.getElementById(id);
      if (element) {
        element.addEventListener("change", (e) => {
          this.updateSetting(id, e.target.checked);
        });
      }
    });

    // Select settings
    const selects = [
      "theme",
      "overlayPosition",
      "detectionSensitivity",
      "minVideoSize",
    ];

    selects.forEach((id) => {
      const element = document.getElementById(id);
      if (element) {
        element.addEventListener("change", (e) => {
          this.updateSetting(id, e.target.value);
        });
      }
    });

    // Opacity slider
    const opacitySlider = document.getElementById("opacity");
    const opacityValue = document.getElementById("opacityValue");

    if (opacitySlider && opacityValue) {
      opacitySlider.addEventListener("input", (e) => {
        const value = parseFloat(e.target.value);
        opacityValue.textContent = Math.round(value * 100) + "%";
        this.updateSetting("opacity", value);
      });
    }

    // Keyboard shortcuts
    const shortcutButtons = document.querySelectorAll(".shortcut-key");
    shortcutButtons.forEach((button) => {
      button.addEventListener("click", (e) => {
        this.editShortcut(e.target);
      });
    });

    // Footer buttons
    const resetBtn = document.getElementById("resetBtn");
    const saveBtn = document.getElementById("saveBtn");

    if (resetBtn) {
      resetBtn.addEventListener("click", () => {
        this.resetToDefaults();
      });
    }

    if (saveBtn) {
      saveBtn.addEventListener("click", () => {
        this.saveSettings();
      });
    }

    // Auto-save on changes
    document.addEventListener("change", () => {
      this.autoSave();
    });
  }

  updateUI() {
    // Update checkboxes
    const checkboxes = [
      "enabled",
      "showOverlay",
      "snapToCorners",
      "alwaysOnTop",
      "resizable",
      "audioControls",
    ];

    checkboxes.forEach((id) => {
      const element = document.getElementById(id);
      if (element) {
        element.checked = this.settings[id] !== false;
      }
    });

    // Update selects
    const selects = [
      "theme",
      "overlayPosition",
      "detectionSensitivity",
      "minVideoSize",
    ];

    selects.forEach((id) => {
      const element = document.getElementById(id);
      if (element && this.settings[id] !== undefined) {
        element.value = this.settings[id];
      }
    });

    // Update opacity slider
    const opacitySlider = document.getElementById("opacity");
    const opacityValue = document.getElementById("opacityValue");

    if (opacitySlider && opacityValue) {
      const opacity = this.settings.opacity || 0.9;
      opacitySlider.value = opacity;
      opacityValue.textContent = Math.round(opacity * 100) + "%";
    }

    // Update keyboard shortcuts
    this.updateShortcutDisplay();
  }

  updateShortcutDisplay() {
    const shortcuts = this.settings.shortcuts || this.defaultSettings.shortcuts;

    const shortcutElements = {
      "toggle-pip": shortcuts.togglePip,
      "increase-opacity": shortcuts.increaseOpacity,
      "decrease-opacity": shortcuts.decreaseOpacity,
    };

    Object.entries(shortcutElements).forEach(([command, shortcut]) => {
      const element = document.querySelector(`[data-command="${command}"]`);
      if (element) {
        element.textContent = shortcut;
      }
    });
  }

  updateSetting(key, value) {
    this.settings[key] = value;
    console.log(`PiP Master: Updated setting ${key} to ${value}`);
  }

  async autoSave() {
    // Debounce auto-save
    clearTimeout(this.autoSaveTimeout);
    this.autoSaveTimeout = setTimeout(() => {
      this.saveSettings(false); // Silent save
    }, 1000);
  }

  async saveSettings(showNotification = true) {
    try {
      await this.sendMessage({
        type: "UPDATE_SETTINGS",
        data: this.settings,
      });

      if (showNotification) {
        this.showSaveNotification();
      }

      console.log("PiP Master: Settings saved successfully");
    } catch (error) {
      console.error("PiP Master: Failed to save settings:", error);
    }
  }

  async resetToDefaults() {
    if (
      confirm(
        "Are you sure you want to reset all settings to their default values?"
      )
    ) {
      this.settings = { ...this.defaultSettings };
      this.updateUI();
      await this.saveSettings();
    }
  }

  editShortcut(button) {
    const command = button.dataset.command;
    const currentShortcut = button.textContent;

    // Create input overlay
    const overlay = document.createElement("div");
    overlay.className = "shortcut-overlay";
    overlay.innerHTML = `
      <div class="shortcut-modal">
        <h3>Edit Keyboard Shortcut</h3>
        <p>Press the new key combination for "${this.getShortcutName(
          command
        )}"</p>
        <div class="shortcut-input">
          <input type="text" id="shortcutInput" value="${currentShortcut}" readonly>
        </div>
        <div class="shortcut-actions">
          <button id="cancelShortcut" class="btn btn-secondary">Cancel</button>
          <button id="saveShortcut" class="btn btn-primary">Save</button>
        </div>
      </div>
    `;

    // Add styles
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    `;

    document.body.appendChild(overlay);

    const input = overlay.querySelector("#shortcutInput");
    const cancelBtn = overlay.querySelector("#cancelShortcut");
    const saveBtn = overlay.querySelector("#saveShortcut");

    let newShortcut = currentShortcut;

    // Listen for key combinations
    const handleKeyDown = (e) => {
      e.preventDefault();

      const keys = [];
      if (e.ctrlKey) keys.push("Ctrl");
      if (e.altKey) keys.push("Alt");
      if (e.shiftKey) keys.push("Shift");
      if (e.metaKey) keys.push("Meta");

      if (e.key && !["Control", "Alt", "Shift", "Meta"].includes(e.key)) {
        keys.push(e.key === " " ? "Space" : e.key);
      }

      if (keys.length > 1) {
        newShortcut = keys.join("+");
        input.value = newShortcut;
      }
    };

    input.addEventListener("keydown", handleKeyDown);

    cancelBtn.addEventListener("click", () => {
      document.body.removeChild(overlay);
    });

    saveBtn.addEventListener("click", () => {
      this.updateShortcutSetting(command, newShortcut);
      button.textContent = newShortcut;
      document.body.removeChild(overlay);
      this.saveSettings();
    });

    // Focus input
    input.focus();
  }

  getShortcutName(command) {
    const names = {
      "toggle-pip": "Toggle Picture-in-Picture",
      "increase-opacity": "Increase Opacity",
      "decrease-opacity": "Decrease Opacity",
    };
    return names[command] || command;
  }

  updateShortcutSetting(command, shortcut) {
    if (!this.settings.shortcuts) {
      this.settings.shortcuts = { ...this.defaultSettings.shortcuts };
    }

    const shortcutMap = {
      "toggle-pip": "togglePip",
      "increase-opacity": "increaseOpacity",
      "decrease-opacity": "decreaseOpacity",
    };

    const key = shortcutMap[command];
    if (key) {
      this.settings.shortcuts[key] = shortcut;
    }
  }

  showSaveNotification() {
    const notification = document.getElementById("saveNotification");
    if (notification) {
      notification.classList.add("show");

      setTimeout(() => {
        notification.classList.remove("show");
      }, 3000);
    }
  }

  async sendMessage(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        resolve(response || {});
      });
    });
  }
}

// Initialize options page when DOM is ready
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", () => {
    new PiPMasterOptions();
  });
} else {
  new PiPMasterOptions();
}
