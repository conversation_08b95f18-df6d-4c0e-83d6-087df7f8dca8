// Comprehensive Settings Fix for PiP Master
// This script fixes all settings-related issues and provides debugging tools

console.log("⚙️ PiP Master Settings Comprehensive Fix v2.0");
console.log("===============================================");

class SettingsManager {
  constructor() {
    this.defaultSettings = {
      enabled: true,
      showOverlay: true,
      overlayPosition: "top-right",
      opacity: 0.9,
      snapToCorners: true,
      theme: "auto",
      shortcuts: {
        togglePip: "Alt+P",
        increaseOpacity: "Alt+.",
        decreaseOpacity: "Alt+,",
      },
      audioControls: true,
      alwaysOnTop: true,
      resizable: true,
      autoActivate: false,
      hoverActivation: true,
      doubleClickActivation: true,
      detectionSensitivity: "medium",
      minVideoSize: 100,
    };
    
    this.init();
  }

  async init() {
    console.log("⚙️ Initializing Settings Manager...");
    
    // Test storage access
    await this.testStorageAccess();
    
    // Initialize default settings if needed
    await this.initializeDefaultSettings();
    
    // Test settings operations
    await this.testSettingsOperations();
    
    console.log("✅ Settings Manager initialized successfully");
  }

  async testStorageAccess() {
    console.log("🔍 Testing storage access...");
    
    try {
      // Test chrome.storage.sync
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
        await chrome.storage.sync.set({ testKey: 'testValue' });
        const result = await chrome.storage.sync.get('testKey');
        if (result.testKey === 'testValue') {
          console.log("✅ chrome.storage.sync is working");
          await chrome.storage.sync.remove('testKey');
        }
      } else {
        console.warn("⚠️ chrome.storage.sync not available");
      }
      
      // Test chrome.storage.local as fallback
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        await chrome.storage.local.set({ testKey: 'testValue' });
        const result = await chrome.storage.local.get('testKey');
        if (result.testKey === 'testValue') {
          console.log("✅ chrome.storage.local is working");
          await chrome.storage.local.remove('testKey');
        }
      } else {
        console.warn("⚠️ chrome.storage.local not available");
      }
      
    } catch (error) {
      console.error("❌ Storage access test failed:", error);
    }
  }

  async initializeDefaultSettings() {
    console.log("🔧 Initializing default settings...");
    
    try {
      const currentSettings = await this.getSettings();
      
      // Merge with defaults to ensure all keys exist
      const mergedSettings = { ...this.defaultSettings, ...currentSettings };
      
      // Save merged settings
      await this.saveSettings(mergedSettings);
      
      console.log("✅ Default settings initialized");
      console.log("Settings keys:", Object.keys(mergedSettings));
      
    } catch (error) {
      console.error("❌ Failed to initialize default settings:", error);
    }
  }

  async getSettings() {
    try {
      // Try chrome.storage.sync first
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
        const result = await chrome.storage.sync.get('pipMasterSettings');
        if (result.pipMasterSettings) {
          console.log("📥 Settings loaded from chrome.storage.sync");
          return result.pipMasterSettings;
        }
      }
      
      // Fallback to chrome.storage.local
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        const result = await chrome.storage.local.get('pipMasterSettings');
        if (result.pipMasterSettings) {
          console.log("📥 Settings loaded from chrome.storage.local");
          return result.pipMasterSettings;
        }
      }
      
      // Fallback to localStorage
      const localSettings = localStorage.getItem('pipMasterSettings');
      if (localSettings) {
        console.log("📥 Settings loaded from localStorage");
        return JSON.parse(localSettings);
      }
      
      console.log("📥 No existing settings found, using defaults");
      return {};
      
    } catch (error) {
      console.error("❌ Failed to get settings:", error);
      return {};
    }
  }

  async saveSettings(settings) {
    try {
      // Save to chrome.storage.sync (primary)
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
        await chrome.storage.sync.set({ pipMasterSettings: settings });
        console.log("💾 Settings saved to chrome.storage.sync");
      }
      
      // Save to chrome.storage.local (backup)
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        await chrome.storage.local.set({ pipMasterSettings: settings });
        console.log("💾 Settings saved to chrome.storage.local");
      }
      
      // Save to localStorage (fallback)
      localStorage.setItem('pipMasterSettings', JSON.stringify(settings));
      console.log("💾 Settings saved to localStorage");
      
      return true;
      
    } catch (error) {
      console.error("❌ Failed to save settings:", error);
      return false;
    }
  }

  async testSettingsOperations() {
    console.log("🧪 Testing settings operations...");
    
    try {
      // Test save and load cycle
      const testSettings = { ...this.defaultSettings, testFlag: true };
      
      const saveResult = await this.saveSettings(testSettings);
      if (!saveResult) {
        throw new Error("Save operation failed");
      }
      
      const loadedSettings = await this.getSettings();
      if (!loadedSettings.testFlag) {
        throw new Error("Load operation failed - test flag not found");
      }
      
      // Clean up test flag
      delete testSettings.testFlag;
      await this.saveSettings(testSettings);
      
      console.log("✅ Settings operations test passed");
      
    } catch (error) {
      console.error("❌ Settings operations test failed:", error);
    }
  }

  async updateSetting(key, value) {
    try {
      const currentSettings = await this.getSettings();
      currentSettings[key] = value;
      
      const success = await this.saveSettings(currentSettings);
      if (success) {
        console.log(`✅ Updated setting: ${key} = ${value}`);
        
        // Notify content scripts if in background context
        if (typeof chrome !== 'undefined' && chrome.tabs) {
          this.notifyContentScripts(currentSettings);
        }
        
        return true;
      }
      
      return false;
      
    } catch (error) {
      console.error(`❌ Failed to update setting ${key}:`, error);
      return false;
    }
  }

  async notifyContentScripts(settings) {
    try {
      const tabs = await chrome.tabs.query({});
      console.log(`📢 Notifying ${tabs.length} tabs about settings update`);
      
      for (const tab of tabs) {
        try {
          await chrome.tabs.sendMessage(tab.id, {
            type: "SETTINGS_UPDATED",
            data: settings,
          });
        } catch (error) {
          // Tab might not have content script loaded, ignore
          console.debug(`Tab ${tab.id} notification failed (expected for non-extension pages)`);
        }
      }
      
    } catch (error) {
      console.error("❌ Failed to notify content scripts:", error);
    }
  }

  async resetToDefaults() {
    console.log("🔄 Resetting settings to defaults...");
    
    try {
      const success = await this.saveSettings(this.defaultSettings);
      if (success) {
        console.log("✅ Settings reset to defaults");
        
        // Notify content scripts
        if (typeof chrome !== 'undefined' && chrome.tabs) {
          this.notifyContentScripts(this.defaultSettings);
        }
        
        return true;
      }
      
      return false;
      
    } catch (error) {
      console.error("❌ Failed to reset settings:", error);
      return false;
    }
  }

  async exportSettings() {
    try {
      const settings = await this.getSettings();
      const exportData = {
        version: "1.0.0",
        timestamp: new Date().toISOString(),
        settings: settings
      };
      
      console.log("📤 Settings exported:", exportData);
      return exportData;
      
    } catch (error) {
      console.error("❌ Failed to export settings:", error);
      return null;
    }
  }

  async importSettings(importData) {
    try {
      if (!importData || !importData.settings) {
        throw new Error("Invalid import data");
      }
      
      // Validate settings structure
      const validatedSettings = { ...this.defaultSettings, ...importData.settings };
      
      const success = await this.saveSettings(validatedSettings);
      if (success) {
        console.log("📥 Settings imported successfully");
        
        // Notify content scripts
        if (typeof chrome !== 'undefined' && chrome.tabs) {
          this.notifyContentScripts(validatedSettings);
        }
        
        return true;
      }
      
      return false;
      
    } catch (error) {
      console.error("❌ Failed to import settings:", error);
      return false;
    }
  }

  async diagnoseSettings() {
    console.log("🔍 Diagnosing settings system...");
    
    const diagnosis = {
      storageAvailable: {
        sync: false,
        local: false,
        localStorage: false
      },
      settingsFound: {
        sync: false,
        local: false,
        localStorage: false
      },
      currentSettings: null,
      issues: []
    };

    // Check storage availability
    try {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
        diagnosis.storageAvailable.sync = true;
        const result = await chrome.storage.sync.get('pipMasterSettings');
        diagnosis.settingsFound.sync = !!result.pipMasterSettings;
      }
    } catch (error) {
      diagnosis.issues.push("chrome.storage.sync access failed: " + error.message);
    }

    try {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        diagnosis.storageAvailable.local = true;
        const result = await chrome.storage.local.get('pipMasterSettings');
        diagnosis.settingsFound.local = !!result.pipMasterSettings;
      }
    } catch (error) {
      diagnosis.issues.push("chrome.storage.local access failed: " + error.message);
    }

    try {
      diagnosis.storageAvailable.localStorage = typeof localStorage !== 'undefined';
      if (diagnosis.storageAvailable.localStorage) {
        diagnosis.settingsFound.localStorage = !!localStorage.getItem('pipMasterSettings');
      }
    } catch (error) {
      diagnosis.issues.push("localStorage access failed: " + error.message);
    }

    // Get current settings
    try {
      diagnosis.currentSettings = await this.getSettings();
    } catch (error) {
      diagnosis.issues.push("Failed to load current settings: " + error.message);
    }

    console.log("🔍 Settings diagnosis complete:", diagnosis);
    return diagnosis;
  }
}

// Initialize settings manager
window.settingsManager = new SettingsManager();

// Export functions for manual testing
window.testSettings = {
  diagnose: () => window.settingsManager.diagnoseSettings(),
  reset: () => window.settingsManager.resetToDefaults(),
  export: () => window.settingsManager.exportSettings(),
  import: (data) => window.settingsManager.importSettings(data),
  update: (key, value) => window.settingsManager.updateSetting(key, value),
  get: () => window.settingsManager.getSettings()
};

console.log("✅ Settings fix loaded. Use window.testSettings for manual testing.");
console.log("Available commands:");
console.log("- window.testSettings.diagnose() - Diagnose settings issues");
console.log("- window.testSettings.get() - Get current settings");
console.log("- window.testSettings.update(key, value) - Update a setting");
console.log("- window.testSettings.reset() - Reset to defaults");
console.log("- window.testSettings.export() - Export settings");
console.log("- window.testSettings.import(data) - Import settings");
