// Extension Loading Diagnostic Script
// Run this first to check if the extension is loading at all

console.log("🔧 PiP Master Extension Loading Test");
console.log("====================================");

// Test 1: Check if extension APIs are available
console.log("\n1. Chrome Extension API Test:");
console.log("✓ Chrome object available:", typeof chrome !== 'undefined');
console.log("✓ Chrome runtime available:", !!(chrome && chrome.runtime));
console.log("✓ Chrome runtime ID:", chrome?.runtime?.id || "Not available");

if (typeof chrome === 'undefined' || !chrome.runtime) {
  console.error("❌ Chrome extension APIs not available!");
  console.log("This means:");
  console.log("- Extension is not loaded");
  console.log("- You're not on a valid web page");
  console.log("- Extension permissions are wrong");
  console.log("\nGo to chrome://extensions/ and check if PiP Master is loaded");
  return;
}

// Test 2: Check content script loading
console.log("\n2. Content Script Loading Test:");
console.log("✓ pipMasterContentLoaded:", window.pipMasterContentLoaded);
console.log("✓ pipMasterInstance:", !!window.pipMasterInstance);
console.log("✓ pipMasterDebug:", !!window.pipMasterDebug);
console.log("✓ pipMasterDiagnostics:", !!window.pipMasterDiagnostics);

// Test 3: Check for content script errors
console.log("\n3. Content Script Error Check:");
if (!window.pipMasterContentLoaded) {
  console.error("❌ Content script not loaded!");
  console.log("Possible causes:");
  console.log("- Content script failed to execute");
  console.log("- JavaScript errors in content script");
  console.log("- Manifest content_scripts configuration wrong");
  console.log("- Page loaded before extension");
  
  // Try to manually load content script
  console.log("\n🔄 Attempting manual content script injection...");
  
  // Check if we can inject the script
  if (chrome.runtime && chrome.runtime.getURL) {
    try {
      const scriptUrl = chrome.runtime.getURL('content/content.js');
      console.log("Content script URL:", scriptUrl);
      
      // Try to load it manually
      const script = document.createElement('script');
      script.src = scriptUrl;
      script.onload = () => {
        console.log("✅ Manual content script injection successful");
        setTimeout(() => {
          console.log("Checking again...");
          console.log("pipMasterContentLoaded:", window.pipMasterContentLoaded);
          console.log("pipMasterInstance:", !!window.pipMasterInstance);
        }, 1000);
      };
      script.onerror = (error) => {
        console.error("❌ Manual content script injection failed:", error);
      };
      document.head.appendChild(script);
    } catch (error) {
      console.error("❌ Cannot inject content script:", error);
    }
  }
} else {
  console.log("✅ Content script appears to be loaded");
}

// Test 4: Check manifest and permissions
console.log("\n4. Extension Manifest Check:");
if (chrome.runtime && chrome.runtime.getManifest) {
  try {
    const manifest = chrome.runtime.getManifest();
    console.log("✓ Manifest version:", manifest.manifest_version);
    console.log("✓ Extension name:", manifest.name);
    console.log("✓ Extension version:", manifest.version);
    console.log("✓ Permissions:", manifest.permissions);
    console.log("✓ Host permissions:", manifest.host_permissions);
    console.log("✓ Content scripts:", manifest.content_scripts?.length || 0);
    
    if (manifest.content_scripts && manifest.content_scripts.length > 0) {
      console.log("✓ Content script matches:", manifest.content_scripts[0].matches);
      console.log("✓ Content script files:", manifest.content_scripts[0].js);
    }
  } catch (error) {
    console.error("❌ Cannot read manifest:", error);
  }
}

// Test 5: Check page compatibility
console.log("\n5. Page Compatibility Check:");
console.log("✓ Current URL:", window.location.href);
console.log("✓ Protocol:", window.location.protocol);
console.log("✓ Hostname:", window.location.hostname);

const isValidPage = window.location.protocol === 'http:' || window.location.protocol === 'https:' || window.location.protocol === 'file:';
console.log("✓ Valid page for extension:", isValidPage);

if (!isValidPage) {
  console.warn("⚠️ Extension may not work on this page type");
  console.log("Try on a regular website (http/https)");
}

// Test 6: Check for conflicting extensions
console.log("\n6. Conflict Detection:");
const possibleConflicts = [
  'pipMaster', 'pictureInPicture', 'pip', 'videoPlayer', 'enhancer'
];

let conflicts = [];
for (let prop in window) {
  if (possibleConflicts.some(conflict => prop.toLowerCase().includes(conflict.toLowerCase()))) {
    conflicts.push(prop);
  }
}

console.log("✓ Potential conflicts found:", conflicts.length);
if (conflicts.length > 0) {
  console.log("  Conflicting properties:", conflicts);
  console.log("  Consider disabling other video extensions temporarily");
}

// Test 7: Basic video detection
console.log("\n7. Basic Video Detection:");
const videos = document.querySelectorAll('video');
console.log("✓ Video elements found:", videos.length);

videos.forEach((video, i) => {
  console.log(`  Video ${i + 1}:`, {
    src: video.src || video.currentSrc || "no src",
    readyState: video.readyState,
    tagName: video.tagName
  });
});

// Test 8: PiP API availability
console.log("\n8. Picture-in-Picture API Check:");
console.log("✓ PiP API supported:", 'pictureInPictureEnabled' in document);
console.log("✓ PiP enabled:", document.pictureInPictureEnabled);

if (!('pictureInPictureEnabled' in document)) {
  console.error("❌ Picture-in-Picture API not supported in this browser");
  console.log("Update to Chrome 88+ or a compatible browser");
}

// Test 9: Try to communicate with background script
console.log("\n9. Background Script Communication Test:");
if (chrome.runtime && chrome.runtime.sendMessage) {
  chrome.runtime.sendMessage({type: 'GET_SETTINGS'}, (response) => {
    if (chrome.runtime.lastError) {
      console.error("❌ Background script communication failed:", chrome.runtime.lastError.message);
      console.log("Background script may not be running");
    } else {
      console.log("✅ Background script communication successful");
      console.log("Response:", response);
    }
  });
} else {
  console.error("❌ Cannot communicate with background script");
}

// Test 10: Manual extension reload suggestion
console.log("\n10. Extension Reload Test:");
console.log("If extension is not working, try these steps:");
console.log("1. Go to chrome://extensions/");
console.log("2. Find 'PiP Master' extension");
console.log("3. Click the reload button (🔄)");
console.log("4. Refresh this page");
console.log("5. Run this test script again");

// Summary and next steps
setTimeout(() => {
  console.log("\n📊 Extension Loading Summary:");
  console.log("=============================");
  console.log("Chrome APIs:", typeof chrome !== 'undefined' ? "✅" : "❌");
  console.log("Content script:", window.pipMasterContentLoaded ? "✅" : "❌");
  console.log("Extension instance:", !!window.pipMasterInstance ? "✅" : "❌");
  console.log("Debug functions:", !!window.pipMasterDebug ? "✅" : "❌");
  console.log("Videos on page:", document.querySelectorAll('video').length);
  console.log("PiP API support:", 'pictureInPictureEnabled' in document ? "✅" : "❌");
  
  console.log("\n🔧 Next Steps:");
  if (!window.pipMasterContentLoaded) {
    console.log("❌ EXTENSION NOT LOADED");
    console.log("1. Check chrome://extensions/ - is PiP Master enabled?");
    console.log("2. Look for error messages in the extension");
    console.log("3. Try reloading the extension");
    console.log("4. Check if content script files exist");
  } else if (!window.pipMasterDebug) {
    console.log("⚠️ EXTENSION PARTIALLY LOADED");
    console.log("1. Content script loaded but debug functions missing");
    console.log("2. Check console for JavaScript errors");
    console.log("3. Try refreshing the page");
  } else {
    console.log("✅ EXTENSION LOADED SUCCESSFULLY");
    console.log("1. You can now use: window.pipMasterDebug.scanForVideos()");
    console.log("2. Try the YouTube debug script if on YouTube");
    console.log("3. Look for overlay buttons on videos");
  }
}, 2000);

console.log("\n⏳ Running extension loading tests... (2 seconds)");
console.log("Watch for results above...");
